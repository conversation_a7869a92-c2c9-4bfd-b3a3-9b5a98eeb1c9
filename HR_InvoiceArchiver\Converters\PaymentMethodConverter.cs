using System;
using System.Globalization;
using System.Windows.Data;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Converters
{
    public class PaymentMethodConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is PaymentMethod method)
            {
                return method switch
                {
                    PaymentMethod.Cash => "نقدي",
                    PaymentMethod.Check => "شيك",
                    PaymentMethod.BankTransfer => "تحويل بنكي",
                    PaymentMethod.CreditCard => "بطاقة ائتمان",
                    PaymentMethod.Other => "أخرى",
                    _ => "غير محدد"
                };
            }
            return "غير محدد";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string text)
            {
                return text switch
                {
                    "نقدي" => PaymentMethod.Cash,
                    "شيك" => PaymentMethod.Check,
                    "تحويل بنكي" => PaymentMethod.BankTransfer,
                    "بطاقة ائتمان" => PaymentMethod.CreditCard,
                    "أخرى" => PaymentMethod.Other,
                    _ => PaymentMethod.Cash
                };
            }
            return PaymentMethod.Cash;
        }
    }
}
