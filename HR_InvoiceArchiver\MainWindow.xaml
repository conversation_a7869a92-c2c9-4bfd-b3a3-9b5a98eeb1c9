<Window x:Class="HR_InvoiceArchiver.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Invoice Archiver"
        Height="900" Width="1400"
        MinHeight="700" MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        WindowState="Normal"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">

    <Window.Resources>
        <!-- Modern Flat Colors -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#1E40AF"/>
        <SolidColorBrush x:Key="AccentColor" Color="#10B981"/>
        <SolidColorBrush x:Key="ErrorColor" Color="#EF4444"/>
        <SolidColorBrush x:Key="BackgroundColor" Color="#F8FAFC"/>
        <SolidColorBrush x:Key="HeaderColor" Color="#FFFFFF"/>

        <!-- Enhanced Modern Gradient for Sidebar -->
        <LinearGradientBrush x:Key="SidebarGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="0.3"/>
            <GradientStop Color="#1E40AF" Offset="0.7"/>
            <GradientStop Color="#3B82F6" Offset="1"/>
        </LinearGradientBrush>

        <!-- Sidebar Overlay Pattern -->
        <LinearGradientBrush x:Key="SidebarOverlay" StartPoint="0,0" EndPoint="1,0">
            <GradientStop Color="#20FFFFFF" Offset="0"/>
            <GradientStop Color="#10FFFFFF" Offset="0.5"/>
            <GradientStop Color="#05FFFFFF" Offset="1"/>
        </LinearGradientBrush>

        <!-- Selected Button Gradient -->
        <LinearGradientBrush x:Key="SelectedButtonGradient" StartPoint="0,0" EndPoint="1,0">
            <GradientStop Color="#10B981" Offset="0"/>
            <GradientStop Color="#059669" Offset="1"/>
        </LinearGradientBrush>

        <!-- Enhanced Modern Navigation Button Style -->
        <Style x:Key="ModernNavButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="58"/>
            <Setter Property="Margin" Value="12,4"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="16"
                                Margin="{TemplateBinding Margin}"
                                Padding="20,0">
                            <ContentPresenter HorizontalAlignment="Right" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                            <GradientStop Color="#40FFFFFF" Offset="0"/>
                                            <GradientStop Color="#20FFFFFF" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                            <GradientStop Color="#60FFFFFF" Offset="0"/>
                                            <GradientStop Color="#30FFFFFF" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Enhanced Selected Button Style -->
        <Style x:Key="SelectedNavButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernNavButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SelectedButtonGradient}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="16"
                                Margin="{TemplateBinding Margin}"
                                Padding="20,0">
                            <Border.Effect>
                                <DropShadowEffect Color="#10B981"
                                                  Opacity="0.5"
                                                  BlurRadius="15"
                                                  ShadowDepth="4"
                                                  Direction="270"/>
                            </Border.Effect>
                            <!-- Inner glow effect -->
                            <Border Background="Transparent"
                                   CornerRadius="16"
                                   BorderThickness="1"
                                   BorderBrush="#40FFFFFF">
                                <ContentPresenter HorizontalAlignment="Right" VerticalAlignment="Center"/>
                            </Border>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                            <GradientStop Color="#059669" Offset="0"/>
                                            <GradientStop Color="#10B981" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                            <GradientStop Color="#047857" Offset="0"/>
                                            <GradientStop Color="#059669" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Window Control Button Style for Sidebar -->
        <Style x:Key="WindowControlButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="46"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="6">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#1AFFFFFF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Close Button Style for Sidebar -->
        <Style x:Key="CloseButtonStyle" TargetType="Button" BasedOn="{StaticResource WindowControlButtonStyle}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="6">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{StaticResource ErrorColor}"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <!-- Main Window Border with Modern Shadow -->
    <Border Background="{StaticResource BackgroundColor}" CornerRadius="16">
        <Border.Effect>
            <DropShadowEffect Color="Black" Opacity="0.15" BlurRadius="25" ShadowDepth="5"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="280"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Invisible Header for Window Dragging -->
            <Border Grid.Row="0" Grid.Column="1"
                    Background="Transparent"
                    Height="30"
                    VerticalAlignment="Top"
                    MouseLeftButtonDown="HeaderBar_MouseLeftButtonDown">
            </Border>

            <!-- Enhanced Sidebar with Modern Design -->
            <Border Grid.Row="0" Grid.Column="0"
                    Background="{StaticResource SidebarGradient}"
                    CornerRadius="20,0,0,20">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.15" BlurRadius="20" ShadowDepth="5" Direction="0"/>
                </Border.Effect>

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="60"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Overlay pattern for depth -->
                    <Border Grid.RowSpan="2"
                           Background="{StaticResource SidebarOverlay}"
                           CornerRadius="20,0,0,20"
                           Opacity="0.3"/>

                    <!-- Window Control Buttons in Sidebar Header -->
                    <Border Grid.Row="0" Background="Transparent" CornerRadius="16,0,0,0">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center" Margin="15,0,0,0">
                            <Button x:Name="CloseButton" Style="{StaticResource CloseButtonStyle}" Click="CloseButton_Click">
                                <materialDesign:PackIcon Kind="Close" Width="16" Height="16" Foreground="White"/>
                            </Button>
                            <Button x:Name="MaximizeButton" Style="{StaticResource WindowControlButtonStyle}" Click="MaximizeButton_Click">
                                <materialDesign:PackIcon Kind="WindowMaximize" Width="16" Height="16" Foreground="White"/>
                            </Button>
                            <Button x:Name="MinimizeButton" Style="{StaticResource WindowControlButtonStyle}" Click="MinimizeButton_Click">
                                <materialDesign:PackIcon Kind="WindowMinimize" Width="16" Height="16" Foreground="White"/>
                            </Button>
                        </StackPanel>
                    </Border>

                    <!-- Sidebar Content with ScrollViewer -->
                    <ScrollViewer Grid.Row="1"
                                 VerticalScrollBarVisibility="Auto"
                                 HorizontalScrollBarVisibility="Disabled"
                                 Margin="0,10,0,20">
                        <StackPanel>
                        <!-- Enhanced Application Title -->
                        <Border Background="#20FFFFFF"
                                Margin="15,0,15,25"
                                Padding="0,20,0,25"
                                CornerRadius="18">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="8" ShadowDepth="2"/>
                            </Border.Effect>
                            <StackPanel HorizontalAlignment="Center">
                                <!-- App Icon with glow effect -->
                                <Border Background="#30FFFFFF"
                                       Width="60" Height="60"
                                       CornerRadius="30"
                                       Margin="0,0,0,15">
                                    <Border.Effect>
                                        <DropShadowEffect Color="White" Opacity="0.3" BlurRadius="10" ShadowDepth="0"/>
                                    </Border.Effect>
                                    <materialDesign:PackIcon Kind="FileDocumentOutline"
                                                             Width="32" Height="32"
                                                             Foreground="White"
                                                             HorizontalAlignment="Center"
                                                             VerticalAlignment="Center"/>
                                </Border>

                                <TextBlock Text="نظام أرشفة الفواتير"
                                           FontSize="18"
                                           FontWeight="Bold"
                                           FontFamily="Segoe UI"
                                           Foreground="White"
                                           HorizontalAlignment="Center"
                                           TextAlignment="Center"
                                           Margin="0,0,0,8">
                                    <TextBlock.Effect>
                                        <DropShadowEffect Color="Black" Opacity="0.3" BlurRadius="4" ShadowDepth="1"/>
                                    </TextBlock.Effect>
                                </TextBlock>

                                <TextBlock Text="إدارة متكاملة وحديثة"
                                           FontSize="12"
                                           FontWeight="Medium"
                                           Foreground="#E0FFFFFF"
                                           HorizontalAlignment="Center"
                                           TextAlignment="Center">
                                    <TextBlock.Effect>
                                        <DropShadowEffect Color="Black" Opacity="0.2" BlurRadius="3" ShadowDepth="1"/>
                                    </TextBlock.Effect>
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <!-- Enhanced Main Tools Section Header -->
                        <Border Background="#30FFFFFF"
                                CornerRadius="16"
                                Margin="15,0,15,20"
                                Padding="18,12">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="6" ShadowDepth="2"/>
                            </Border.Effect>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <materialDesign:PackIcon Kind="ViewDashboard"
                                                         Width="18" Height="18"
                                                         Foreground="White"
                                                         Margin="0,0,10,0">
                                    <materialDesign:PackIcon.Effect>
                                        <DropShadowEffect Color="White" Opacity="0.3" BlurRadius="4" ShadowDepth="0"/>
                                    </materialDesign:PackIcon.Effect>
                                </materialDesign:PackIcon>
                                <TextBlock Text="الأدوات الرئيسية"
                                           FontSize="14"
                                           FontWeight="SemiBold"
                                           Foreground="White">
                                    <TextBlock.Effect>
                                        <DropShadowEffect Color="Black" Opacity="0.2" BlurRadius="3" ShadowDepth="1"/>
                                    </TextBlock.Effect>
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <!-- Main Navigation Buttons -->
                        <Button x:Name="DashboardButton"
                                Style="{StaticResource SelectedNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="لوحة التحكم" Foreground="White" FontSize="14" FontWeight="SemiBold" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="ViewDashboard" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="InvoicesButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="الفواتير" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="FileDocumentOutline" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="PaymentsButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="المدفوعات" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="CreditCard" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="SuppliersButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="الموردين" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="TruckDelivery" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="OffersButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="عروض المندوبين" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="ShoppingCart" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="SearchButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="البحث" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="Magnify" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="ReportsButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="التقارير" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="FileChart" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="ChartsButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="المخططات البيانية" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="ChartLine" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- Enhanced Separator -->
                        <Border Height="2"
                               Background="#40FFFFFF"
                               Margin="30,25,30,25"
                               CornerRadius="1">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="3" ShadowDepth="1"/>
                            </Border.Effect>
                        </Border>

                        <!-- Enhanced Advanced Tools Section Header -->
                        <Border Background="#30FFFFFF"
                                CornerRadius="16"
                                Margin="15,0,15,20"
                                Padding="18,12">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="6" ShadowDepth="2"/>
                            </Border.Effect>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <materialDesign:PackIcon Kind="Cog"
                                                         Width="18" Height="18"
                                                         Foreground="White"
                                                         Margin="0,0,10,0">
                                    <materialDesign:PackIcon.Effect>
                                        <DropShadowEffect Color="White" Opacity="0.3" BlurRadius="4" ShadowDepth="0"/>
                                    </materialDesign:PackIcon.Effect>
                                </materialDesign:PackIcon>
                                <TextBlock Text="الأدوات المتقدمة"
                                           FontSize="14"
                                           FontWeight="SemiBold"
                                           Foreground="White">
                                    <TextBlock.Effect>
                                        <DropShadowEffect Color="Black" Opacity="0.2" BlurRadius="3" ShadowDepth="1"/>
                                    </TextBlock.Effect>
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <Button x:Name="SettingsButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="الإعدادات" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="Cog" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>


                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Border>

            <!-- Main Content Area - Full Flat -->
            <Border Grid.Row="0" Grid.Column="1"
                    Background="{StaticResource BackgroundColor}"
                    CornerRadius="0,16,16,0"
                    Margin="8,0,0,0">
                <ContentPresenter x:Name="MainContentPresenter" Margin="0,0,0,0"/>
            </Border>
        </Grid>
    </Border>
</Window>
