using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.ComponentModel;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Pages
{
    public partial class PaymentsPage : UserControl, INavigationAware
    {
        private readonly IPaymentService _paymentService;
        private readonly IToastService _toastService;
        private readonly INavigationService _navigationService;

        public ObservableCollection<Payment> Payments { get; set; } = new();
        private ICollectionView _paymentsView;

        public PaymentsPage(
            IPaymentService paymentService,
            IToastService toastService,
            INavigationService navigationService)
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: Constructor started");
                InitializeComponent();

                _paymentService = paymentService ?? throw new ArgumentNullException(nameof(paymentService));
                _toastService = toastService ?? throw new ArgumentNullException(nameof(toastService));
                _navigationService = navigationService ?? throw new ArgumentNullException(nameof(navigationService));

                // Setup collection view for filtering
                _paymentsView = CollectionViewSource.GetDefaultView(Payments);
                _paymentsView.Filter = FilterPayments;
                PaymentsDataGrid.ItemsSource = _paymentsView;



                System.Console.WriteLine("PaymentsPage: Constructor completed successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: Constructor error: {ex.Message}");
                System.Console.WriteLine($"PaymentsPage: Stack trace: {ex.StackTrace}");
                throw;
            }
        }



        public async void OnNavigatedTo(object parameter)
        {
            System.Console.WriteLine("PaymentsPage: OnNavigatedTo called");
            try
            {
                // Handle navigation parameters
                if (parameter is string action && action == "add")
                {
                    System.Console.WriteLine("PaymentsPage: Add payment parameter detected");
                    AddPaymentButton_Click(this, new RoutedEventArgs());
                }
                else if (parameter is string multiAction && multiAction == "multi")
                {
                    System.Console.WriteLine("PaymentsPage: Multi payment parameter detected");
                    AddMultiPaymentButton_Click(this, new RoutedEventArgs());
                }
                else
                {
                    System.Console.WriteLine("PaymentsPage: Loading payments data");
                    await LoadPaymentsAsync();
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: OnNavigatedTo error: {ex.Message}");
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء التنقل: {ex.Message}");
            }
        }

        public void OnNavigatedFrom()
        {
            System.Console.WriteLine("PaymentsPage: OnNavigatedFrom called");
        }

        private async Task LoadPaymentsAsync()
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: LoadPaymentsAsync started");

                // Show loading
                LoadingPanel.Visibility = Visibility.Visible;
                EmptyStatePanel.Visibility = Visibility.Collapsed;

                // Load payments
                var payments = await _paymentService.GetAllPaymentsAsync();

                Payments.Clear();
                foreach (var payment in payments)
                {
                    Payments.Add(payment);
                }

                // Update statistics
                await UpdateStatisticsAsync();

                // Hide loading and show content
                LoadingPanel.Visibility = Visibility.Collapsed;

                if (Payments.Count == 0)
                {
                    EmptyStatePanel.Visibility = Visibility.Visible;
                }
                else
                {
                    EmptyStatePanel.Visibility = Visibility.Collapsed;
                }

                System.Console.WriteLine("PaymentsPage: LoadPaymentsAsync completed successfully");
                _toastService?.ShowSuccess("تم التحديث", "تم تحميل المدفوعات بنجاح");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: LoadPaymentsAsync error: {ex.Message}");
                LoadingPanel.Visibility = Visibility.Collapsed;
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء تحميل المدفوعات: {ex.Message}");
            }
        }

        private bool FilterPayments(object item)
        {
            if (item is not Payment payment) return false;

            // Search filter
            var searchText = SearchTextBox?.Text?.Trim().ToLower() ?? "";
            if (!string.IsNullOrEmpty(searchText))
            {
                var matchesSearch = payment.ReceiptNumber.ToLower().Contains(searchText) ||
                                  (payment.SupplierName?.ToLower().Contains(searchText) ?? false) ||
                                  (payment.Notes?.ToLower().Contains(searchText) ?? false);

                if (!matchesSearch) return false;
            }

            // Date range filter
            if (FromDatePicker?.SelectedDate.HasValue == true)
            {
                if (payment.PaymentDate < FromDatePicker.SelectedDate.Value) return false;
            }

            if (ToDatePicker?.SelectedDate.HasValue == true)
            {
                if (payment.PaymentDate > ToDatePicker.SelectedDate.Value) return false;
            }

            // Amount range filter
            if (!string.IsNullOrEmpty(MinAmountTextBox?.Text) &&
                decimal.TryParse(MinAmountTextBox.Text, out decimal minAmount))
            {
                if (payment.Amount < minAmount) return false;
            }

            // MaxAmountTextBox temporarily disabled
            /*if (!string.IsNullOrEmpty(MaxAmountTextBox?.Text) &&
                decimal.TryParse(MaxAmountTextBox.Text, out decimal maxAmount))
            {
                if (payment.Amount > maxAmount) return false;
            }*/

            // Payment status filter temporarily disabled
            /*
            if (PaymentStatusComboBox?.SelectedItem is ComboBoxItem statusItem &&
                statusItem.Tag?.ToString() != "All")
            {
                var tagValue = statusItem.Tag?.ToString();
                if (!string.IsNullOrEmpty(tagValue) && Enum.TryParse<PaymentStatus>(tagValue, out var status))
                {
                    if (payment.Status != status) return false;
                }
            }
            */

            return true;
        }

        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var statistics = await _paymentService.GetPaymentStatisticsAsync();

                // Update bottom bar statistics
                TotalPaymentsTextBlock.Text = $"إجمالي المدفوعات: {statistics.TotalPayments:N0}";
                TotalAmountTextBlock.Text = $"إجمالي المبلغ: {statistics.TotalAmount:N0} د.ع";

                // Calculate average
                var averageAmount = statistics.TotalPayments > 0 ? statistics.TotalAmount / statistics.TotalPayments : 0;
                AveragePaymentTextBlock.Text = $"متوسط المدفوعة: {averageAmount:N0} د.ع";

                // Update cash vs card statistics
                var cashAmount = Payments.Where(p => p.Method == PaymentMethod.Cash).Sum(p => p.Amount);
                var cardAmount = Payments.Where(p => p.Method == PaymentMethod.CreditCard).Sum(p => p.Amount);

                CashPaymentsTextBlock.Text = $"مدفوعات نقدية: {cashAmount:N0} د.ع";
                CardPaymentsTextBlock.Text = $"مدفوعات بطاقة: {cardAmount:N0} د.ع";

                System.Console.WriteLine("PaymentsPage: Statistics updated successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: UpdateStatisticsAsync error: {ex.Message}");
            }
        }

        private void FilterPayments()
        {
            // استخدام النظام الجديد للتصفية السريعة
            _paymentsView?.Refresh();
        }

        // Event Handlers
        private async void AddPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: AddPaymentButton_Click triggered");

                var addEditPaymentWindow = App.ServiceProvider.GetService<HR_InvoiceArchiver.Windows.AddEditPaymentWindow>();
                if (addEditPaymentWindow != null)
                {
                    System.Console.WriteLine("PaymentsPage: AddEditPaymentWindow created successfully");
                    addEditPaymentWindow.Owner = Window.GetWindow(this);
                    var result = addEditPaymentWindow.ShowDialog();
                    System.Console.WriteLine($"PaymentsPage: Dialog result: {result}");

                    // Refresh the payments list after closing
                    await LoadPaymentsAsync();
                    _toastService?.ShowSuccess("تم", "تم تحديث قائمة المدفوعات");
                }
                else
                {
                    System.Console.WriteLine("PaymentsPage: Failed to create AddEditPaymentWindow");
                    _toastService?.ShowError("خطأ", "فشل في إنشاء نافذة إضافة الدفعة");
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: Exception in AddPaymentButton_Click: {ex.Message}");
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء فتح نافذة إضافة الدفعة: {ex.Message}");
            }
        }

        private void AddMultiPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: AddMultiPaymentButton_Click triggered");

                var multiPaymentWindow = new Windows.MultiPaymentWindow();
                multiPaymentWindow.Owner = Window.GetWindow(this);
                multiPaymentWindow.WindowStartupLocation = WindowStartupLocation.CenterOwner;

                var result = multiPaymentWindow.ShowDialog();
                if (result == true && multiPaymentWindow.IsSaved)
                {
                    _toastService?.ShowSuccess("تم إنشاء الوصل المتعدد", "تم إنشاء الوصل المتعدد بنجاح");
                    // Refresh the payments list
                    _ = LoadPaymentsAsync();
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: Exception in AddMultiPaymentButton_Click: {ex.Message}");
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء فتح نافذة الدفع المتعدد: {ex.Message}");
            }
        }





        private void MultiPaymentContainer_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            // Prevent event bubbling to overlay
            e.Handled = true;
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: RefreshButton_Click triggered");
                RefreshButton.IsEnabled = false;
                _toastService?.ShowInfo("تحديث", "جاري تحديث قائمة المدفوعات...");

                await LoadPaymentsAsync();

                _toastService?.ShowSuccess("تم التحديث", "تم تحديث قائمة المدفوعات بنجاح");
                System.Console.WriteLine("PaymentsPage: Refresh completed successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: Exception in RefreshButton_Click: {ex.Message}");
                _toastService?.ShowError("خطأ", $"فشل في تحديث قائمة المدفوعات: {ex.Message}");
            }
            finally
            {
                RefreshButton.IsEnabled = true;
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _paymentsView?.Refresh();
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            _paymentsView?.Refresh();
            _toastService?.ShowInfo("البحث", "تم تطبيق البحث");
        }

        private void ClearSearchButton_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = "";
            _toastService?.ShowInfo("تم المسح", "تم مسح البحث");
        }

        private void ApplyFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            _paymentsView?.Refresh();
            _toastService?.ShowInfo("الفلاتر", "تم تطبيق الفلاتر");
        }



        private void ToggleFilters_Click(object sender, RoutedEventArgs e)
        {
            // Temporarily disabled - FiltersPanel not available
            /*
            if (FiltersPanel.Visibility == Visibility.Collapsed)
            {
                FiltersPanel.Visibility = Visibility.Visible;
                ToggleFiltersButton.Content = "إخفاء التصفية";
            }
            else
            {
                FiltersPanel.Visibility = Visibility.Collapsed;
                ToggleFiltersButton.Content = "تصفية متقدمة";
            }
            */
        }

        private void DateFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
                _paymentsView?.Refresh();
        }

        private void AmountFilter_Changed(object sender, TextChangedEventArgs e)
        {
            if (IsLoaded)
                _paymentsView?.Refresh();
        }

        private void PaymentMethodFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
                _paymentsView?.Refresh();
        }

        private void PaymentStatusFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
                _paymentsView?.Refresh();
        }

        private void ApplyFilters_Click(object sender, RoutedEventArgs e)
        {
            _paymentsView?.Refresh();
            _toastService?.ShowInfo("تم التطبيق", "تم تطبيق المرشحات");
        }

        private void ClearFilters_Click(object sender, RoutedEventArgs e)
        {
            // مسح جميع المرشحات
            SearchTextBox.Text = "";
            FromDatePicker.SelectedDate = null;
            ToDatePicker.SelectedDate = null;
            MinAmountTextBox.Text = string.Empty;
            MaxAmountTextBox.Text = string.Empty;

            // إعادة تعيين الفلتر الأصلي
            _paymentsView.Filter = FilterPayments;
            _paymentsView?.Refresh();

            _toastService?.ShowInfo("تم المسح", "تم مسح جميع المرشحات");
        }



        private void AdvancedSearchButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService?.ShowInfo("البحث المتقدم", "سيتم إضافة البحث المتقدم قريباً");
        }

        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Console.WriteLine("PaymentsPage: ExportButton_Click triggered");
                ExportButton.IsEnabled = false;

                _toastService?.ShowInfo("تصدير", "جاري تحضير ملف Excel...");

                // محاكاة عملية التصدير
                await Task.Delay(2000);

                // هنا يمكن إضافة منطق التصدير الفعلي
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "حفظ ملف Excel",
                    Filter = "Excel Files (*.xlsx)|*.xlsx|All Files (*.*)|*.*",
                    DefaultExt = "xlsx",
                    FileName = $"المدفوعات_{DateTime.Now:yyyy-MM-dd}.xlsx"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // هنا يتم التصدير الفعلي
                    _toastService?.ShowSuccess("تم التصدير", $"تم حفظ الملف في: {saveFileDialog.FileName}");
                    System.Console.WriteLine($"PaymentsPage: Export saved to: {saveFileDialog.FileName}");
                }
                else
                {
                    _toastService?.ShowInfo("تم الإلغاء", "تم إلغاء عملية التصدير");
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"PaymentsPage: Exception in ExportButton_Click: {ex.Message}");
                _toastService?.ShowError("خطأ", $"فشل في تصدير البيانات: {ex.Message}");
            }
            finally
            {
                ExportButton.IsEnabled = true;
            }
        }

        private void QuickFilterFullPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Clear search text
                SearchTextBox.Text = "";

                // Clear other filters
                FromDatePicker.SelectedDate = null;
                ToDatePicker.SelectedDate = null;
                MinAmountTextBox.Text = "";
                MaxAmountTextBox.Text = "";

                // Apply full payment filter
                _paymentsView.Filter = (item) =>
                {
                    if (item is Payment payment)
                    {
                        return payment.Status == PaymentStatus.FullPayment;
                    }
                    return false;
                };

                _paymentsView.Refresh();
                _toastService?.ShowInfo("تم التطبيق", "تم عرض التسديدات الكاملة فقط");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تطبيق الفلتر: {ex.Message}");
            }
        }

        private void QuickFilterPartialPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Clear search text
                SearchTextBox.Text = "";

                // Clear other filters
                FromDatePicker.SelectedDate = null;
                ToDatePicker.SelectedDate = null;
                MinAmountTextBox.Text = "";
                MaxAmountTextBox.Text = "";

                // Apply partial payment filter
                _paymentsView.Filter = (item) =>
                {
                    if (item is Payment payment)
                    {
                        return payment.Status == PaymentStatus.PartialPayment;
                    }
                    return false;
                };

                _paymentsView.Refresh();
                _toastService?.ShowInfo("تم التطبيق", "تم عرض التسديدات الجزئية فقط");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تطبيق الفلتر: {ex.Message}");
            }
        }

        private void QuickFilterWithDiscount_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Clear search text
                SearchTextBox.Text = "";

                // Clear other filters
                FromDatePicker.SelectedDate = null;
                ToDatePicker.SelectedDate = null;
                MinAmountTextBox.Text = "";
                MaxAmountTextBox.Text = "";

                // Apply payment with discount filter
                _paymentsView.Filter = (item) =>
                {
                    if (item is Payment payment)
                    {
                        return payment.Status == PaymentStatus.PaymentWithDiscount;
                    }
                    return false;
                };

                _paymentsView.Refresh();
                _toastService?.ShowInfo("تم التطبيق", "تم عرض التسديدات مع خصم فقط");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تطبيق الفلتر: {ex.Message}");
            }
        }

        private void QuickFilterWithRefund_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Clear search text
                SearchTextBox.Text = "";

                // Clear other filters
                FromDatePicker.SelectedDate = null;
                ToDatePicker.SelectedDate = null;
                MinAmountTextBox.Text = "";
                MaxAmountTextBox.Text = "";

                // Apply payment with refund filter
                _paymentsView.Filter = (item) =>
                {
                    if (item is Payment payment)
                    {
                        return payment.Status == PaymentStatus.PaymentWithRefund;
                    }
                    return false;
                };

                _paymentsView.Refresh();
                _toastService?.ShowInfo("تم التطبيق", "تم عرض التسديدات مع استرجاع فقط");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تطبيق الفلتر: {ex.Message}");
            }
        }

        // Payment Actions Event Handlers
        private void ViewReceiptAttachment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Payment payment)
                {
                    if (string.IsNullOrEmpty(payment.AttachmentPath))
                    {
                        _toastService?.ShowWarning("تنبيه", "لا يوجد مرفق لهذا الوصل");
                        return;
                    }

                    OpenPaymentAttachment(payment);
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في عرض مرفق الوصل: {ex.Message}");
            }
        }

        private void ViewPaymentDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Payment payment)
                {
                    ShowPaymentDetailsOverlay(payment);
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في عرض تفاصيل الدفعة: {ex.Message}");
            }
        }



        private async void EditPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Payment payment)
                {
                    // Create new instance with payment data for editing
                    var addEditPaymentWindow = new Windows.AddEditPaymentWindow(payment);
                    addEditPaymentWindow.Owner = Window.GetWindow(this);

                    var result = addEditPaymentWindow.ShowDialog();

                    // Refresh the payments list if changes were saved
                    if (result == true)
                    {
                        await LoadPaymentsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في فتح نافذة تعديل الدفعة: {ex.Message}");
            }
        }

        private async void DeletePayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Payment payment)
                {
                    // استخدام نافذة التأكيد الجديدة
                    var parentWindow = Window.GetWindow(this);
                    bool confirmed = Controls.ConfirmationDialog.ShowPaymentDeletionConfirmation(parentWindow, payment);

                    if (confirmed)
                    {
                        // إظهار مؤشر التحميل
                        _toastService?.ShowInfo("جاري الحذف", "جاري حذف المدفوعة...");

                        await _paymentService.DeletePaymentAsync(payment.Id);

                        _toastService?.ShowSuccess("تم الحذف بنجاح",
                            $"تم حذف الوصل رقم {payment.ReceiptNumber} وجميع المرفقات المرتبطة به");

                        await LoadPaymentsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في الحذف", $"فشل في حذف الدفعة: {ex.Message}");
            }
        }

        // Helper Methods
        private void OpenPaymentAttachment(Payment payment)
        {
            try
            {
                if (string.IsNullOrEmpty(payment.AttachmentPath))
                {
                    _toastService?.ShowWarning("تنبيه", "لا يوجد مرفق لهذا الوصل");
                    return;
                }

                Utils.FileHelper.OpenAttachment(payment.AttachmentPath, "Payments");
                _toastService?.ShowSuccess("تم فتح المرفق", $"تم فتح مرفق الوصل رقم {payment.ReceiptNumber}");
            }
            catch (FileNotFoundException)
            {
                _toastService?.ShowError("ملف غير موجود", "مرفق الوصل غير موجود في المسار المحدد");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في فتح الملف", $"فشل في فتح مرفق الوصل: {ex.Message}");
            }
        }



        private void ShowPaymentDetailsOverlay(Payment payment)
        {
            try
            {
                // Temporarily disabled - PaymentDetailsContainer not available
                /*
                // Clear existing content
                PaymentDetailsContainer.Children.Clear();

                // Create new PaymentDetailsControl
                var paymentDetailsControl = new Controls.PaymentDetailsControl();
                paymentDetailsControl.FormClosed += PaymentDetailsControl_FormClosed;
                paymentDetailsControl.LoadPaymentDetails(payment);

                // Add to container
                PaymentDetailsContainer.Children.Add(paymentDetailsControl);

                // Show overlay with animation
                PaymentDetailsOverlay.Visibility = Visibility.Visible;
                */

                // Show simple message instead
                MessageBox.Show($"تفاصيل المدفوعة رقم {payment.ReceiptNumber}", "تفاصيل المدفوعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في عرض تفاصيل الدفعة: {ex.Message}");
            }
        }

        private void PaymentDetailsControl_FormClosed(object? sender, EventArgs e)
        {
            // Temporarily disabled
            /*
            // Hide overlay with animation
            var fadeOut = new System.Windows.Media.Animation.DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = TimeSpan.FromMilliseconds(300)
            };

            fadeOut.Completed += (s, args) =>
            {
                PaymentDetailsOverlay.Visibility = Visibility.Collapsed;
                PaymentDetailsContainer.Children.Clear();
            };

            PaymentDetailsOverlay.BeginAnimation(UIElement.OpacityProperty, fadeOut);
            */
        }

        private void ShowPaymentDetailsDialog_Old(Payment payment)
        {
            try
            {
                var detailsWindow = new Window
                {
                    Title = $"تفاصيل الدفعة - {payment.ReceiptNumber}",
                    Width = 600,
                    Height = 700,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = Window.GetWindow(this),
                    ResizeMode = ResizeMode.CanResize,
                    WindowStyle = WindowStyle.SingleBorderWindow,
                    Background = new SolidColorBrush(Color.FromRgb(248, 249, 250))
                };

                var mainGrid = new Grid();
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Header
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) }); // Content
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Footer

                // Header Section
                var headerBorder = new Border
                {
                    Background = new LinearGradientBrush(
                        Color.FromRgb(0, 123, 255),
                        Color.FromRgb(0, 86, 179),
                        new Point(0, 0),
                        new Point(1, 1)),
                    Padding = new Thickness(25, 20, 25, 20),
                    CornerRadius = new CornerRadius(0, 0, 0, 0)
                };

                var headerPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal
                };

                var headerIcon = new TextBlock
                {
                    Text = "💳",
                    FontSize = 32,
                    Margin = new Thickness(0, 0, 15, 0),
                    VerticalAlignment = VerticalAlignment.Center
                };

                var headerTextPanel = new StackPanel();
                var headerTitle = new TextBlock
                {
                    Text = $"وصل دفع رقم: {payment.ReceiptNumber}",
                    FontSize = 20,
                    FontWeight = FontWeights.Bold,
                    Foreground = Brushes.White
                };

                var headerSubtitle = new TextBlock
                {
                    Text = $"المورد: {payment.SupplierName}",
                    FontSize = 14,
                    Foreground = new SolidColorBrush(Color.FromRgb(220, 220, 220)),
                    Margin = new Thickness(0, 5, 0, 0)
                };

                headerTextPanel.Children.Add(headerTitle);
                headerTextPanel.Children.Add(headerSubtitle);
                headerPanel.Children.Add(headerIcon);
                headerPanel.Children.Add(headerTextPanel);
                headerBorder.Child = headerPanel;

                // Content Section
                var contentScrollViewer = new ScrollViewer
                {
                    VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                    Padding = new Thickness(25, 20, 25, 20)
                };

                var contentPanel = new StackPanel();

                // Payment Information Card
                var paymentCard = CreateInfoCard("معلومات الدفعة", new[]
                {
                    new { Icon = "💰", Label = "المبلغ المدفوع", Value = $"{payment.Amount:N0} د.ع", Color = "#28A745" },
                    new { Icon = "📅", Label = "تاريخ الدفع", Value = payment.PaymentDate.ToString("dd/MM/yyyy"), Color = "#007BFF" },
                    new { Icon = "💳", Label = "طريقة الدفع", Value = payment.PaymentMethodText, Color = "#6F42C1" },
                    new { Icon = "📊", Label = "حالة التسديد", Value = payment.StatusText, Color = "#17A2B8" }
                });

                contentPanel.Children.Add(paymentCard);

                // Financial Details Card
                if (payment.DiscountAmount > 0 || payment.RefundValue > 0)
                {
                    var financialCard = CreateInfoCard("التفاصيل المالية", new[]
                    {
                        new { Icon = "💸", Label = "مبلغ الخصم", Value = $"{payment.DiscountAmount:N0} د.ع", Color = "#FFC107" },
                        new { Icon = "↩️", Label = "قيمة الاسترجاع", Value = $"{payment.RefundValue:N0} د.ع", Color = "#DC3545" },
                        new { Icon = "🧮", Label = "إجمالي التسوية", Value = $"{payment.TotalSettlement:N0} د.ع", Color = "#28A745" }
                    });

                    contentPanel.Children.Add(financialCard);
                }

                // Invoice Information Card
                var invoiceCard = CreateInfoCard("معلومات الفاتورة", new[]
                {
                    new { Icon = "📄", Label = "رقم الفاتورة", Value = payment.InvoiceNumber, Color = "#007BFF" },
                    new { Icon = "💼", Label = "اسم المورد", Value = payment.SupplierName, Color = "#6C757D" },
                    new { Icon = "💰", Label = "المبلغ المتبقي", Value = $"{payment.Invoice?.RemainingAmount:N0} د.ع", Color = "#FD7E14" }
                });

                contentPanel.Children.Add(invoiceCard);

                // Notes Card
                if (!string.IsNullOrEmpty(payment.Notes))
                {
                    var notesCard = CreateInfoCard("ملاحظات", new[]
                    {
                        new { Icon = "📝", Label = "التفاصيل", Value = payment.Notes, Color = "#6C757D" }
                    });

                    contentPanel.Children.Add(notesCard);
                }

                // System Information Card
                var systemCard = CreateInfoCard("معلومات النظام", new[]
                {
                    new { Icon = "🕒", Label = "تاريخ الإنشاء", Value = payment.CreatedDate.ToString("dd/MM/yyyy HH:mm"), Color = "#6C757D" },
                    new { Icon = "✏️", Label = "آخر تحديث", Value = payment.UpdatedDate?.ToString("dd/MM/yyyy HH:mm") ?? "لم يتم التحديث", Color = "#6C757D" }
                });

                contentPanel.Children.Add(systemCard);

                contentScrollViewer.Content = contentPanel;

                // Footer Section
                var footerBorder = new Border
                {
                    Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                    BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                    BorderThickness = new Thickness(0, 1, 0, 0),
                    Padding = new Thickness(25, 15, 25, 15)
                };

                var footerPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    HorizontalAlignment = HorizontalAlignment.Right
                };

                // Attachment Button
                if (!string.IsNullOrEmpty(payment.AttachmentPath))
                {
                    var attachmentButton = new Button
                    {
                        Content = "📎 عرض المرفق",
                        Padding = new Thickness(20, 10, 20, 10),
                        Margin = new Thickness(0, 0, 10, 0),
                        Background = new SolidColorBrush(Color.FromRgb(255, 107, 53)),
                        Foreground = Brushes.White,
                        BorderThickness = new Thickness(0),
                        FontWeight = FontWeights.SemiBold
                    };

                    attachmentButton.Click += (s, e) =>
                    {
                        OpenPaymentAttachment(payment);
                    };

                    footerPanel.Children.Add(attachmentButton);
                }

                // Close Button
                var closeButton = new Button
                {
                    Content = "إغلاق",
                    Padding = new Thickness(20, 10, 20, 10),
                    Background = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                    Foreground = Brushes.White,
                    BorderThickness = new Thickness(0),
                    FontWeight = FontWeights.SemiBold
                };

                closeButton.Click += (s, e) => detailsWindow.Close();
                footerPanel.Children.Add(closeButton);
                footerBorder.Child = footerPanel;

                // Add to main grid
                Grid.SetRow(headerBorder, 0);
                Grid.SetRow(contentScrollViewer, 1);
                Grid.SetRow(footerBorder, 2);

                mainGrid.Children.Add(headerBorder);
                mainGrid.Children.Add(contentScrollViewer);
                mainGrid.Children.Add(footerBorder);

                detailsWindow.Content = mainGrid;
                detailsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في عرض تفاصيل الدفعة: {ex.Message}");
            }
        }

        private Border CreateInfoCard(string title, dynamic[] items)
        {
            var card = new Border
            {
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(0, 0, 0, 20),
                Padding = new Thickness(0)
            };

            var cardPanel = new StackPanel();

            // Card Header
            var headerBorder = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
                BorderThickness = new Thickness(0, 0, 0, 1),
                Padding = new Thickness(20, 15, 20, 15)
            };

            var headerText = new TextBlock
            {
                Text = title,
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64))
            };

            headerBorder.Child = headerText;
            cardPanel.Children.Add(headerBorder);

            // Card Content
            var contentPanel = new StackPanel
            {
                Margin = new Thickness(20, 15, 20, 15)
            };

            foreach (var item in items)
            {
                var itemPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    Margin = new Thickness(0, 0, 0, 12)
                };

                // Icon
                var iconText = new TextBlock
                {
                    Text = item.Icon,
                    FontSize = 18,
                    Margin = new Thickness(0, 0, 12, 0),
                    VerticalAlignment = VerticalAlignment.Center
                };

                // Label and Value Container
                var textContainer = new StackPanel
                {
                    VerticalAlignment = VerticalAlignment.Center
                };

                var labelText = new TextBlock
                {
                    Text = item.Label,
                    FontSize = 12,
                    FontWeight = FontWeights.SemiBold,
                    Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                    Margin = new Thickness(0, 0, 0, 2)
                };

                var valueText = new TextBlock
                {
                    Text = item.Value,
                    FontSize = 14,
                    FontWeight = FontWeights.SemiBold,
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(item.Color)),
                    TextWrapping = TextWrapping.Wrap
                };

                textContainer.Children.Add(labelText);
                textContainer.Children.Add(valueText);

                itemPanel.Children.Add(iconText);
                itemPanel.Children.Add(textContainer);

                contentPanel.Children.Add(itemPanel);
            }

            cardPanel.Children.Add(contentPanel);
            card.Child = cardPanel;

            return card;
        }
    }
}
