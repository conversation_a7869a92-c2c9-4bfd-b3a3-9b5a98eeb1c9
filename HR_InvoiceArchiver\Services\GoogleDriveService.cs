using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Drive.v3;
using Google.Apis.Drive.v3.Data;
using Google.Apis.Services;
using Google.Apis.Util.Store;
using HR_InvoiceArchiver.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// خدمة Google Drive للتخزين السحابي
    /// </summary>
    public class GoogleDriveService : ICloudStorageService
    {
        private DriveService? _driveService;
        private UserCredential? _credential;
        private string _applicationName = "HR Invoice Archiver";
        private string _credentialsPath = GetCredentialsPath();
        private string _tokenPath = "token.json";
        private string _rootFolderId = "";
        private readonly ILogger<GoogleDriveService>? _logger;
        private readonly CloudPerformanceMonitorService? _performanceMonitor;
        private readonly CloudFileEncryptionService? _encryptionService;

        // الصلاحيات المطلوبة
        private readonly string[] _scopes = { DriveService.Scope.DriveFile };

        public event EventHandler<ConnectionStatusChangedEventArgs>? ConnectionStatusChanged;
        public event EventHandler<FileUploadProgressEventArgs>? FileUploadProgress;
        public event EventHandler<FileDownloadProgressEventArgs>? FileDownloadProgress;

        public GoogleDriveService(
            ILogger<GoogleDriveService>? logger = null,
            CloudPerformanceMonitorService? performanceMonitor = null,
            CloudFileEncryptionService? encryptionService = null)
        {
            _logger = logger;
            _performanceMonitor = performanceMonitor;
            _encryptionService = encryptionService;
        }

        /// <summary>
        /// الحصول على مسار ملف الاعتمادات
        /// </summary>
        private static string GetCredentialsPath()
        {
            var appDirectory = System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
            return System.IO.Path.Combine(appDirectory!, "credentials.json");
        }

        /// <summary>
        /// التحقق من حالة الاتصال
        /// </summary>
        public async Task<bool> IsConnectedAsync()
        {
            try
            {
                if (_driveService == null)
                    return false;

                // اختبار الاتصال بطلب معلومات المستخدم
                var request = _driveService.About.Get();
                request.Fields = "user";
                var about = await request.ExecuteAsync();
                
                return about?.User != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// تسجيل الدخول إلى Google Drive
        /// </summary>
        public async Task<bool> AuthenticateAsync()
        {
            try
            {
                // التحقق من وجود ملف الاعتمادات
                if (!System.IO.File.Exists(_credentialsPath))
                {
                    throw new FileNotFoundException("ملف الاعتمادات غير موجود. يرجى إضافة ملف credentials.json");
                }

                // قراءة ملف الاعتمادات
                using var stream = new FileStream(_credentialsPath, FileMode.Open, FileAccess.Read);
                
                // تسجيل الدخول
                _credential = await GoogleWebAuthorizationBroker.AuthorizeAsync(
                    GoogleClientSecrets.FromStream(stream).Secrets,
                    _scopes,
                    "user",
                    CancellationToken.None,
                    new FileDataStore(_tokenPath, true));

                // إنشاء خدمة Drive
                _driveService = new DriveService(new BaseClientService.Initializer()
                {
                    HttpClientInitializer = _credential,
                    ApplicationName = _applicationName,
                });

                // إنشاء مجلد التطبيق الرئيسي
                await EnsureRootFolderExistsAsync();

                // إشعار بتغيير حالة الاتصال
                ConnectionStatusChanged?.Invoke(this, new ConnectionStatusChangedEventArgs 
                { 
                    IsConnected = true, 
                    Message = "تم تسجيل الدخول بنجاح" 
                });

                return true;
            }
            catch (Exception ex)
            {
                ConnectionStatusChanged?.Invoke(this, new ConnectionStatusChangedEventArgs 
                { 
                    IsConnected = false, 
                    Message = $"فشل في تسجيل الدخول: {ex.Message}" 
                });
                
                return false;
            }
        }

        /// <summary>
        /// تسجيل الخروج
        /// </summary>
        public async Task<bool> LogoutAsync()
        {
            try
            {
                if (_credential != null)
                {
                    await _credential.RevokeTokenAsync(CancellationToken.None);
                }

                _driveService?.Dispose();
                _driveService = null;
                _credential = null;

                // حذف ملف الرمز المميز
                if (Directory.Exists(_tokenPath))
                {
                    Directory.Delete(_tokenPath, true);
                }

                ConnectionStatusChanged?.Invoke(this, new ConnectionStatusChangedEventArgs 
                { 
                    IsConnected = false, 
                    Message = "تم تسجيل الخروج بنجاح" 
                });

                return true;
            }
            catch (Exception ex)
            {
                ConnectionStatusChanged?.Invoke(this, new ConnectionStatusChangedEventArgs 
                { 
                    IsConnected = false, 
                    Message = $"خطأ في تسجيل الخروج: {ex.Message}" 
                });
                
                return false;
            }
        }

        /// <summary>
        /// رفع ملف إلى Google Drive مع التشفير ومراقبة الأداء
        /// </summary>
        public async Task<string?> UploadFileAsync(string localFilePath, string cloudFileName, string folderPath = "")
        {
            // التحقق من صحة المدخلات
            if (string.IsNullOrWhiteSpace(localFilePath))
                throw new ArgumentException("مسار الملف لا يمكن أن يكون فارغاً", nameof(localFilePath));

            if (string.IsNullOrWhiteSpace(cloudFileName))
                throw new ArgumentException("اسم الملف السحابي لا يمكن أن يكون فارغاً", nameof(cloudFileName));

            using var performanceTracker = _performanceMonitor?.StartOperation("Upload", cloudFileName, new FileInfo(localFilePath).Length);
            string? encryptedFilePath = null;

            try
            {
                if (_driveService == null)
                    throw new InvalidOperationException("لم يتم تسجيل الدخول");

                if (!System.IO.File.Exists(localFilePath))
                    throw new FileNotFoundException($"الملف المحلي غير موجود: {localFilePath}");

                _logger?.LogInformation($"بدء رفع الملف: {cloudFileName}");

                // تشفير الملف إذا كانت الخدمة متوفرة
                var fileToUpload = localFilePath;
                if (_encryptionService != null)
                {
                    _logger?.LogDebug($"تشفير الملف: {cloudFileName}");
                    encryptedFilePath = await _encryptionService.EncryptFileForCloudAsync(localFilePath);
                    fileToUpload = encryptedFilePath;
                    cloudFileName += ".encrypted"; // إضافة امتداد للملف المشفر
                }

                // تحديد المجلد الهدف
                var parentFolderId = string.IsNullOrEmpty(folderPath) ? _rootFolderId :
                    await GetOrCreateFolderAsync(folderPath);

                // إعداد معلومات الملف
                var fileMetadata = new Google.Apis.Drive.v3.Data.File()
                {
                    Name = cloudFileName,
                    Parents = new List<string> { parentFolderId },
                    Description = $"Uploaded on {DateTime.Now:yyyy-MM-dd HH:mm:ss}"
                };

                // رفع الملف
                using var stream = new FileStream(fileToUpload, FileMode.Open, FileAccess.Read);
                var request = _driveService.Files.Create(fileMetadata, stream, GetMimeType(localFilePath));

                // تتبع التقدم (إشعار بداية الرفع)
                FileUploadProgress?.Invoke(this, new FileUploadProgressEventArgs
                {
                    FileName = cloudFileName,
                    BytesUploaded = 0,
                    TotalBytes = stream.Length
                });

                var result = await request.UploadAsync();

                if (result.Status == Google.Apis.Upload.UploadStatus.Completed)
                {
                    var fileId = request.ResponseBody?.Id;
                    _logger?.LogInformation($"تم رفع الملف بنجاح: {cloudFileName} (ID: {fileId})");

                    // إشعار انتهاء الرفع
                    FileUploadProgress?.Invoke(this, new FileUploadProgressEventArgs
                    {
                        FileName = cloudFileName,
                        BytesUploaded = stream.Length,
                        TotalBytes = stream.Length
                    });

                    return fileId;
                }
                else
                {
                    var errorMessage = $"فشل في رفع الملف: {result.Exception?.Message ?? "خطأ غير معروف"}";
                    performanceTracker?.MarkAsFailure(errorMessage);
                    throw new Exception(errorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في رفع الملف: {cloudFileName}");
                performanceTracker?.MarkAsFailure(ex.Message);
                throw new Exception($"خطأ في رفع الملف: {ex.Message}", ex);
            }
            finally
            {
                // تنظيف الملف المشفر المؤقت
                if (!string.IsNullOrEmpty(encryptedFilePath) && System.IO.File.Exists(encryptedFilePath))
                {
                    try
                    {
                        System.IO.File.Delete(encryptedFilePath);
                        _logger?.LogDebug($"تم حذف الملف المشفر المؤقت: {encryptedFilePath}");
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning(ex, $"فشل في حذف الملف المشفر المؤقت: {encryptedFilePath}");
                    }
                }
            }
        }

        /// <summary>
        /// تحميل ملف من Google Drive
        /// </summary>
        public async Task<bool> DownloadFileAsync(string cloudFileId, string localFilePath)
        {
            try
            {
                if (_driveService == null)
                    throw new InvalidOperationException("لم يتم تسجيل الدخول");

                var request = _driveService.Files.Get(cloudFileId);
                using var stream = new FileStream(localFilePath, FileMode.Create, FileAccess.Write);
                
                // تتبع التقدم
                var progress = new Progress<Google.Apis.Download.IDownloadProgress>(p =>
                {
                    FileDownloadProgress?.Invoke(this, new FileDownloadProgressEventArgs
                    {
                        FileName = Path.GetFileName(localFilePath),
                        BytesDownloaded = p.BytesDownloaded,
                        TotalBytes = stream.Length
                    });
                });

                var result = await request.DownloadAsync(stream);
                
                return result.Status == Google.Apis.Download.DownloadStatus.Completed;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحميل الملف: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف ملف من Google Drive
        /// </summary>
        public async Task<bool> DeleteFileAsync(string cloudFileId)
        {
            try
            {
                if (_driveService == null)
                    throw new InvalidOperationException("لم يتم تسجيل الدخول");

                await _driveService.Files.Delete(cloudFileId).ExecuteAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف الملف: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من وجود ملف
        /// </summary>
        public async Task<bool> FileExistsAsync(string cloudFileId)
        {
            try
            {
                if (_driveService == null)
                    return false;

                var request = _driveService.Files.Get(cloudFileId);
                request.Fields = "id";
                var file = await request.ExecuteAsync();
                
                return file != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على معلومات الملف
        /// </summary>
        public async Task<CloudFileInfo?> GetFileInfoAsync(string cloudFileId)
        {
            try
            {
                if (_driveService == null)
                    return null;

                var request = _driveService.Files.Get(cloudFileId);
                request.Fields = "id,name,size,createdTime,modifiedTime,mimeType,webViewLink,webContentLink";
                var file = await request.ExecuteAsync();

                if (file == null)
                    return null;

                return new CloudFileInfo
                {
                    Id = file.Id,
                    Name = file.Name,
                    Size = file.Size ?? 0,
                    CreatedTime = file.CreatedTimeDateTimeOffset?.DateTime ?? DateTime.MinValue,
                    ModifiedTime = file.ModifiedTimeDateTimeOffset?.DateTime ?? DateTime.MinValue,
                    MimeType = file.MimeType ?? "",
                    WebViewLink = file.WebViewLink ?? "",
                    WebContentLink = file.WebContentLink ?? ""
                };
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// الحصول على قائمة الملفات في مجلد
        /// </summary>
        public async Task<List<CloudFileInfo>> GetFilesInFolderAsync(string folderPath = "")
        {
            var files = new List<CloudFileInfo>();
            
            try
            {
                if (_driveService == null)
                    return files;

                var folderId = string.IsNullOrEmpty(folderPath) ? _rootFolderId : 
                    await GetOrCreateFolderAsync(folderPath);

                var request = _driveService.Files.List();
                request.Q = $"'{folderId}' in parents and trashed=false";
                request.Fields = "files(id,name,size,createdTime,modifiedTime,mimeType,webViewLink,webContentLink)";

                var result = await request.ExecuteAsync();

                if (result.Files != null)
                {
                    foreach (var file in result.Files)
                    {
                        files.Add(new CloudFileInfo
                        {
                            Id = file.Id,
                            Name = file.Name,
                            Size = file.Size ?? 0,
                            CreatedTime = file.CreatedTimeDateTimeOffset?.DateTime ?? DateTime.MinValue,
                            ModifiedTime = file.ModifiedTimeDateTimeOffset?.DateTime ?? DateTime.MinValue,
                            MimeType = file.MimeType ?? "",
                            WebViewLink = file.WebViewLink ?? "",
                            WebContentLink = file.WebContentLink ?? ""
                        });
                    }
                }
            }
            catch
            {
                // إرجاع قائمة فارغة في حالة الخطأ
            }

            return files;
        }

        /// <summary>
        /// إنشاء مجلد
        /// </summary>
        public async Task<string?> CreateFolderAsync(string folderName, string parentFolderId = "")
        {
            try
            {
                if (_driveService == null)
                    throw new InvalidOperationException("لم يتم تسجيل الدخول");

                var parent = string.IsNullOrEmpty(parentFolderId) ? _rootFolderId : parentFolderId;

                var folderMetadata = new Google.Apis.Drive.v3.Data.File()
                {
                    Name = folderName,
                    MimeType = "application/vnd.google-apps.folder",
                    Parents = new List<string> { parent }
                };

                var request = _driveService.Files.Create(folderMetadata);
                var folder = await request.ExecuteAsync();

                return folder.Id;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء المجلد: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على معلومات التخزين
        /// </summary>
        public async Task<StorageInfo> GetStorageInfoAsync()
        {
            try
            {
                if (_driveService == null)
                    throw new InvalidOperationException("لم يتم تسجيل الدخول");

                var request = _driveService.About.Get();
                request.Fields = "storageQuota";
                var about = await request.ExecuteAsync();

                var quota = about.StorageQuota;
                
                return new StorageInfo
                {
                    TotalSpace = quota?.Limit ?? 0,
                    UsedSpace = quota?.Usage ?? 0
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على معلومات التخزين: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على معلومات المستخدم
        /// </summary>
        public async Task<UserInfo?> GetUserInfoAsync()
        {
            try
            {
                if (_driveService == null)
                    return null;

                var request = _driveService.About.Get();
                request.Fields = "user";
                var about = await request.ExecuteAsync();

                var user = about.User;
                if (user == null)
                    return null;

                return new UserInfo
                {
                    Id = user.PermissionId ?? "",
                    Name = user.DisplayName ?? "",
                    Email = user.EmailAddress ?? "",
                    PhotoUrl = user.PhotoLink ?? ""
                };
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// مزامنة الملفات مع Google Drive
        /// </summary>
        public async Task<SyncResult> SyncFilesAsync()
        {
            var result = new SyncResult();
            var startTime = DateTime.Now;

            try
            {
                if (_driveService == null)
                {
                    result.Success = false;
                    result.Errors.Add("لم يتم تسجيل الدخول إلى Google Drive");
                    return result;
                }

                _logger?.LogInformation("بدء مزامنة الملفات مع Google Drive");

                // التحقق من الاتصال
                if (!await IsConnectedAsync())
                {
                    result.Success = false;
                    result.Errors.Add("لا يوجد اتصال بـ Google Drive");
                    return result;
                }

                // الحصول على قائمة الملفات المحلية التي تحتاج مزامنة
                var localFiles = await GetLocalFilesNeedingSyncAsync();
                result.FilesProcessed = localFiles.Count;

                _logger?.LogInformation($"تم العثور على {localFiles.Count} ملف يحتاج مزامنة");

                // مزامنة كل ملف
                foreach (var fileInfo in localFiles)
                {
                    try
                    {
                        var cloudFileId = await UploadFileWithRetryAsync(
                            fileInfo.LocalPath,
                            fileInfo.CloudFileName,
                            fileInfo.FolderPath);

                        if (!string.IsNullOrEmpty(cloudFileId))
                        {
                            result.FilesUploaded++;
                            await UpdateFileSyncStatusAsync(fileInfo.Id, fileInfo.Type, cloudFileId, CloudSyncStatus.Synced);
                            _logger?.LogInformation($"تم رفع الملف بنجاح: {fileInfo.CloudFileName}");
                        }
                        else
                        {
                            result.Errors.Add($"فشل في رفع الملف: {fileInfo.CloudFileName}");
                            await UpdateFileSyncStatusAsync(fileInfo.Id, fileInfo.Type, null, CloudSyncStatus.Failed);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, $"خطأ في رفع الملف: {fileInfo.CloudFileName}");
                        result.Errors.Add($"خطأ في رفع {fileInfo.CloudFileName}: {ex.Message}");
                        await UpdateFileSyncStatusAsync(fileInfo.Id, fileInfo.Type, null, CloudSyncStatus.Failed);
                    }
                }

                result.Success = result.Errors.Count == 0;
                _logger?.LogInformation($"انتهت المزامنة: {result.FilesUploaded}/{result.FilesProcessed} ملف تم رفعه بنجاح");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في عملية المزامنة");
                result.Success = false;
                result.Errors.Add($"خطأ في المزامنة: {ex.Message}");
            }

            result.Duration = DateTime.Now - startTime;
            return result;
        }

        /// <summary>
        /// رفع ملف مع إعادة المحاولة
        /// </summary>
        private async Task<string?> UploadFileWithRetryAsync(string localPath, string cloudFileName, string folderPath, int maxRetries = 3)
        {
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    var result = await UploadFileAsync(localPath, cloudFileName, folderPath);
                    if (!string.IsNullOrEmpty(result))
                    {
                        if (attempt > 1)
                        {
                            _logger?.LogInformation($"نجح رفع الملف في المحاولة {attempt}: {cloudFileName}");
                        }
                        return result;
                    }
                }
                catch (Exception ex) when (attempt < maxRetries)
                {
                    _logger?.LogWarning(ex, $"فشلت المحاولة {attempt}/{maxRetries} لرفع الملف: {cloudFileName}");
                    await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, attempt))); // Exponential backoff
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, $"فشل رفع الملف نهائياً بعد {attempt} محاولات: {cloudFileName}");
                    throw;
                }
            }

            return null;
        }

        /// <summary>
        /// الحصول على الملفات المحلية التي تحتاج مزامنة
        /// </summary>
        private async Task<List<LocalFileInfo>> GetLocalFilesNeedingSyncAsync()
        {
            var files = new List<LocalFileInfo>();

            try
            {
                // هذا يتطلب الوصول إلى قاعدة البيانات
                // سيتم تطبيقه في CloudSyncService بدلاً من هنا
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على الملفات المحلية");
            }

            return files;
        }

        /// <summary>
        /// تحديث حالة مزامنة الملف في قاعدة البيانات
        /// </summary>
        private async Task UpdateFileSyncStatusAsync(int fileId, string fileType, string? cloudFileId, CloudSyncStatus status)
        {
            try
            {
                // هذا يتطلب الوصول إلى قاعدة البيانات
                // سيتم تطبيقه في CloudSyncService
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث حالة المزامنة للملف {fileId}");
            }
        }

        #region Helper Methods

        /// <summary>
        /// التأكد من وجود المجلد الرئيسي
        /// </summary>
        private async Task EnsureRootFolderExistsAsync()
        {
            try
            {
                if (_driveService == null)
                    return;

                // البحث عن مجلد التطبيق
                var request = _driveService.Files.List();
                request.Q = $"name='{_applicationName}' and mimeType='application/vnd.google-apps.folder' and trashed=false";
                request.Fields = "files(id,name)";

                var result = await request.ExecuteAsync();

                if (result.Files != null && result.Files.Count > 0)
                {
                    _rootFolderId = result.Files[0].Id;
                }
                else
                {
                    // إنشاء المجلد الرئيسي
                    _rootFolderId = await CreateFolderAsync(_applicationName) ?? "";
                }
            }
            catch
            {
                _rootFolderId = "";
            }
        }

        /// <summary>
        /// الحصول على مجلد أو إنشاؤه
        /// </summary>
        private async Task<string> GetOrCreateFolderAsync(string folderPath)
        {
            var pathParts = folderPath.Split('/', StringSplitOptions.RemoveEmptyEntries);
            var currentParentId = _rootFolderId;

            foreach (var part in pathParts)
            {
                var folderId = await FindFolderAsync(part, currentParentId);
                if (string.IsNullOrEmpty(folderId))
                {
                    folderId = await CreateFolderAsync(part, currentParentId);
                }
                currentParentId = folderId ?? currentParentId;
            }

            return currentParentId;
        }

        /// <summary>
        /// البحث عن مجلد
        /// </summary>
        private async Task<string?> FindFolderAsync(string folderName, string parentId)
        {
            try
            {
                if (_driveService == null)
                    return null;

                var request = _driveService.Files.List();
                request.Q = $"name='{folderName}' and '{parentId}' in parents and mimeType='application/vnd.google-apps.folder' and trashed=false";
                request.Fields = "files(id)";

                var result = await request.ExecuteAsync();

                return result.Files?.Count > 0 ? result.Files[0].Id : null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// تحديد نوع MIME للملف
        /// </summary>
        private static string GetMimeType(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            
            return extension switch
            {
                ".pdf" => "application/pdf",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".tiff" or ".tif" => "image/tiff",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".txt" => "text/plain",
                ".rtf" => "application/rtf",
                _ => "application/octet-stream"
            };
        }

        #endregion

        public void Dispose()
        {
            _driveService?.Dispose();
        }
    }

    /// <summary>
    /// معلومات الملف المحلي للمزامنة
    /// </summary>
    public class LocalFileInfo
    {
        public int Id { get; set; }
        public string Type { get; set; } = string.Empty; // "Invoice" or "Payment"
        public string LocalPath { get; set; } = string.Empty;
        public string CloudFileName { get; set; } = string.Empty;
        public string FolderPath { get; set; } = string.Empty;
        public CloudSyncStatus SyncStatus { get; set; }
        public DateTime? LastModified { get; set; }
    }
}
