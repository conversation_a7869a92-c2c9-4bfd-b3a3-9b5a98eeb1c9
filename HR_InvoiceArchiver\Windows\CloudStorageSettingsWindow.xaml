<Window x:Class="HR_InvoiceArchiver.Windows.CloudStorageSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إعدادات التخزين السحابي"
        Height="600" Width="500"
        FlowDirection="RightToLeft"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <!-- Modern Card Style -->
        <Style x:Key="SettingsCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
        </Style>

        <!-- Header Style -->
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <!-- Setting Item Style -->
        <Style x:Key="SettingItemStyle" TargetType="StackPanel">
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <ScrollViewer VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- Header -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                    <materialDesign:PackIcon Kind="Settings" 
                                           Width="32" Height="32" 
                                           Foreground="#2196F3"
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="إعدادات التخزين السحابي" 
                              FontSize="24" FontWeight="Bold"
                              Margin="15,0,0,0"
                              VerticalAlignment="Center"/>
                </StackPanel>

                <!-- General Settings -->
                <materialDesign:Card Style="{StaticResource SettingsCardStyle}">
                    <StackPanel>
                        <TextBlock Text="الإعدادات العامة" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <!-- Enable Cloud Sync -->
                        <StackPanel Style="{StaticResource SettingItemStyle}">
                            <CheckBox x:Name="EnableCloudSyncCheckBox" 
                                     Content="تفعيل التخزين السحابي"
                                     IsChecked="True"
                                     FontSize="14"/>
                            <TextBlock Text="تفعيل النسخ الاحتياطي التلقائي للملفات في السحابة"
                                      FontSize="12" Foreground="#666"
                                      Margin="25,5,0,0"/>
                        </StackPanel>

                        <!-- Auto Sync -->
                        <StackPanel Style="{StaticResource SettingItemStyle}">
                            <CheckBox x:Name="AutoSyncCheckBox" 
                                     Content="المزامنة التلقائية"
                                     IsChecked="True"
                                     FontSize="14"/>
                            <TextBlock Text="مزامنة الملفات الجديدة تلقائياً بدون تدخل المستخدم"
                                      FontSize="12" Foreground="#666"
                                      Margin="25,5,0,0"/>
                        </StackPanel>

                        <!-- Sync Interval -->
                        <StackPanel Style="{StaticResource SettingItemStyle}">
                            <TextBlock Text="فترة المزامنة (دقائق)" FontSize="14" FontWeight="SemiBold"/>
                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <Slider x:Name="SyncIntervalSlider" 
                                       Width="200"
                                       Minimum="5" Maximum="1440" 
                                       Value="30"
                                       TickFrequency="5"
                                       IsSnapToTickEnabled="True"
                                       VerticalAlignment="Center"/>
                                <TextBlock x:Name="SyncIntervalText" 
                                          Text="30 دقيقة"
                                          FontSize="14" FontWeight="Bold"
                                          Margin="15,0,0,0"
                                          VerticalAlignment="Center"/>
                            </StackPanel>
                            <TextBlock Text="تحديد المدة بين عمليات المزامنة التلقائية"
                                      FontSize="12" Foreground="#666"
                                      Margin="0,5,0,0"/>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- File Settings -->
                <materialDesign:Card Style="{StaticResource SettingsCardStyle}">
                    <StackPanel>
                        <TextBlock Text="إعدادات الملفات" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <!-- Max File Size -->
                        <StackPanel Style="{StaticResource SettingItemStyle}">
                            <TextBlock Text="الحد الأقصى لحجم الملف (ميجابايت)" FontSize="14" FontWeight="SemiBold"/>
                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <Slider x:Name="MaxFileSizeSlider" 
                                       Width="200"
                                       Minimum="1" Maximum="100" 
                                       Value="50"
                                       TickFrequency="5"
                                       IsSnapToTickEnabled="True"
                                       VerticalAlignment="Center"/>
                                <TextBlock x:Name="MaxFileSizeText" 
                                          Text="50 MB"
                                          FontSize="14" FontWeight="Bold"
                                          Margin="15,0,0,0"
                                          VerticalAlignment="Center"/>
                            </StackPanel>
                            <TextBlock Text="الملفات الأكبر من هذا الحجم لن يتم رفعها تلقائياً"
                                      FontSize="12" Foreground="#666"
                                      Margin="0,5,0,0"/>
                        </StackPanel>

                        <!-- Compression -->
                        <StackPanel Style="{StaticResource SettingItemStyle}">
                            <CheckBox x:Name="EnableCompressionCheckBox" 
                                     Content="ضغط الملفات الكبيرة"
                                     IsChecked="True"
                                     FontSize="14"/>
                            <TextBlock Text="ضغط الملفات أكبر من 10 ميجابايت لتوفير المساحة"
                                      FontSize="12" Foreground="#666"
                                      Margin="25,5,0,0"/>
                        </StackPanel>

                        <!-- Encryption -->
                        <StackPanel Style="{StaticResource SettingItemStyle}">
                            <CheckBox x:Name="EnableEncryptionCheckBox" 
                                     Content="تشفير الملفات"
                                     IsChecked="True"
                                     IsEnabled="False"
                                     FontSize="14"/>
                            <TextBlock Text="تشفير جميع الملفات بـ AES-256 قبل الرفع (مفعل دائماً)"
                                      FontSize="12" Foreground="#666"
                                      Margin="25,5,0,0"/>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Notification Settings -->
                <materialDesign:Card Style="{StaticResource SettingsCardStyle}">
                    <StackPanel>
                        <TextBlock Text="إعدادات الإشعارات" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <!-- Success Notifications -->
                        <StackPanel Style="{StaticResource SettingItemStyle}">
                            <CheckBox x:Name="ShowSuccessNotificationsCheckBox" 
                                     Content="إشعارات النجاح"
                                     IsChecked="True"
                                     FontSize="14"/>
                            <TextBlock Text="عرض إشعار عند نجاح عملية المزامنة"
                                      FontSize="12" Foreground="#666"
                                      Margin="25,5,0,0"/>
                        </StackPanel>

                        <!-- Error Notifications -->
                        <StackPanel Style="{StaticResource SettingItemStyle}">
                            <CheckBox x:Name="ShowErrorNotificationsCheckBox" 
                                     Content="إشعارات الأخطاء"
                                     IsChecked="True"
                                     FontSize="14"/>
                            <TextBlock Text="عرض إشعار عند فشل عملية المزامنة"
                                      FontSize="12" Foreground="#666"
                                      Margin="25,5,0,0"/>
                        </StackPanel>

                        <!-- Progress Notifications -->
                        <StackPanel Style="{StaticResource SettingItemStyle}">
                            <CheckBox x:Name="ShowProgressNotificationsCheckBox" 
                                     Content="إشعارات التقدم"
                                     IsChecked="False"
                                     FontSize="14"/>
                            <TextBlock Text="عرض إشعارات أثناء عملية المزامنة"
                                      FontSize="12" Foreground="#666"
                                      Margin="25,5,0,0"/>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                    <Button x:Name="SaveButton" 
                           Content="حفظ الإعدادات"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Background="#4CAF50"
                           Width="120" Height="40"
                           Margin="0,0,15,0"
                           Click="SaveButton_Click"/>
                    
                    <Button x:Name="CancelButton" 
                           Content="إلغاء"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Width="120" Height="40"
                           Margin="15,0,0,0"
                           Click="CancelButton_Click"/>
                </StackPanel>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
