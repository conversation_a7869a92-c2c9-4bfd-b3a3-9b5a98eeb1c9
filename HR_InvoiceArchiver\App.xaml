<Application x:Class="HR_InvoiceArchiver.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:HR_InvoiceArchiver"
             xmlns:converters="clr-namespace:HR_InvoiceArchiver.Converters"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Modern Material Design Theme with Blue Primary -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Green" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesign2.Defaults.xaml" />

                <!-- Custom Modern Styles -->
                <ResourceDictionary Source="pack://application:,,,/HR_InvoiceArchiver;component/Resources/ModernStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- إضافة المحولات المطلوبة -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

            <!-- محولات إضافية للتأكد من عدم وجود أخطاء -->
            <local:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
            <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>

            <!-- محولات النصوص العربية -->
            <converters:StatusToTextConverter x:Key="StatusToTextConverter"/>
            <converters:PaymentMethodConverter x:Key="PaymentMethodConverter"/>
            <converters:GreaterThanZeroConverter x:Key="GreaterThanZeroConverter"/>

            <!-- Global Color Resources -->
            <SolidColorBrush x:Key="PrimaryColor" Color="#3B82F6"/>
            <SolidColorBrush x:Key="SecondaryColor" Color="#10B981"/>
            <SolidColorBrush x:Key="AccentColor" Color="#10B981"/>
            <SolidColorBrush x:Key="ErrorColor" Color="#EF4444"/>
            <SolidColorBrush x:Key="BackgroundColor" Color="#F8FAFC"/>
            <SolidColorBrush x:Key="HeaderColor" Color="#FFFFFF"/>

            <!-- Enhanced Modern Gradient for Sidebar -->
            <LinearGradientBrush x:Key="SidebarGradient" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#667eea" Offset="0"/>
                <GradientStop Color="#764ba2" Offset="0.3"/>
                <GradientStop Color="#1E40AF" Offset="0.7"/>
                <GradientStop Color="#3B82F6" Offset="1"/>
            </LinearGradientBrush>

            <!-- Sidebar Overlay Pattern -->
            <LinearGradientBrush x:Key="SidebarOverlay" StartPoint="0,0" EndPoint="1,0">
                <GradientStop Color="#20FFFFFF" Offset="0"/>
                <GradientStop Color="#10FFFFFF" Offset="0.5"/>
                <GradientStop Color="#05FFFFFF" Offset="1"/>
            </LinearGradientBrush>

            <!-- Selected Button Gradient -->
            <LinearGradientBrush x:Key="SelectedButtonGradient" StartPoint="0,0" EndPoint="1,0">
                <GradientStop Color="#10B981" Offset="0"/>
                <GradientStop Color="#059669" Offset="1"/>
            </LinearGradientBrush>

        </ResourceDictionary>
    </Application.Resources>
</Application>
