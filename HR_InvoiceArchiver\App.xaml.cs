using System.Windows;
using System.Windows.Controls;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using HR_InvoiceArchiver.Data;
using HR_InvoiceArchiver.Data.Repositories;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Pages;
using HR_InvoiceArchiver.Windows;
using HR_InvoiceArchiver.Utils;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver;

public partial class App : Application
{
    public static ServiceProvider ServiceProvider { get; private set; } = null!;

    protected override void OnStartup(StartupEventArgs e)
    {
        try
        {
            var services = new ServiceCollection();
            ConfigureServices(services);

            ServiceProvider = services.BuildServiceProvider();

            // Initialize database - MINIMAL
            try
            {
                using (var scope = ServiceProvider.CreateScope())
                {
                    var context = scope.ServiceProvider.GetRequiredService<DatabaseContext>();

                    // إنشاء قاعدة البيانات فقط إذا لم تكن موجودة (بدون حذف)
                    context.Database.EnsureCreated();

                    // Only seed if completely empty
                    if (!context.Suppliers.Any())
                    {
                        var supplier = new Supplier
                        {
                            Name = "مورد تجريبي",
                            ContactPerson = "أحمد محمد",
                            Phone = "07901234567",
                            Email = "<EMAIL>",
                            Address = "بغداد",
                            CreatedDate = DateTime.Now
                        };
                        context.Suppliers.Add(supplier);
                        context.SaveChanges();
                    }

                    // Add sample offers if empty
                    if (!context.Offers.Any())
                    {
                        var sampleOffers = new[]
                        {
                            new Offer
                            {
                                ScientificOffice = "مكتب الأمل العلمي",
                                RepresentativeName = "أحمد محمد",
                                RepresentativePhone = "07901234567",
                                ScientificName = "باراسيتامول",
                                TradeName = "بانادول",
                                Price = 2500,
                                BonusOrDiscount = "خصم 5%",
                                Notes = "عرض تجريبي",
                                CreatedAt = DateTime.Now.AddDays(-5),
                                AttachmentPath = null
                            },
                            new Offer
                            {
                                ScientificOffice = "مكتب النور العلمي",
                                RepresentativeName = "فاطمة علي",
                                RepresentativePhone = "07801234567",
                                ScientificName = "أموكسيسيلين",
                                TradeName = "أموكسيل",
                                Price = 3200,
                                BonusOrDiscount = "بونص 10%",
                                Notes = "عرض تجريبي",
                                CreatedAt = DateTime.Now.AddDays(-3),
                                AttachmentPath = null
                            },
                            new Offer
                            {
                                ScientificOffice = "مكتب الشفاء العلمي",
                                RepresentativeName = "محمد حسن",
                                RepresentativePhone = "07701234567",
                                ScientificName = "باراسيتامول",
                                TradeName = "أدول",
                                Price = 2200,
                                BonusOrDiscount = "لا يوجد",
                                Notes = "عرض تجريبي - أفضل سعر",
                                CreatedAt = DateTime.Now.AddDays(-1),
                                AttachmentPath = null
                            }
                        };

                        context.Offers.AddRange(sampleOffers);
                        context.SaveChanges();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Database initialization error: {ex}");
            }

            // Initialize global exception handler
            var exceptionHandler = ServiceProvider.GetRequiredService<IGlobalExceptionHandler>();
            exceptionHandler.Initialize();

            base.OnStartup(e);

            // تجاوز نافذة تسجيل الدخول مؤقتاً وإظهار النافذة الرئيسية مباشرة
            try
            {
                var mainWindow = ServiceProvider.GetRequiredService<MainWindow>();
                mainWindow.Show();
            }
            catch (Exception mainWindowEx)
            {
                MessageBox.Show($"خطأ في إنشاء النافذة الرئيسية: {mainWindowEx.Message}\n\nسأحاول إنشاء نافذة بديلة...", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);

                // إنشاء نافذة بديلة بسيطة في حالة فشل النافذة الرئيسية
                try
                {
                    var fallbackWindow = new Window
                    {
                        Title = "HR Invoice Archiver - Fallback Mode",
                        Width = 800,
                        Height = 600,
                        WindowStartupLocation = WindowStartupLocation.CenterScreen,
                        Content = new TextBlock
                        {
                            Text = $"النافذة الرئيسية لا تعمل حالياً\n\nالخطأ: {mainWindowEx.Message}\n\nيرجى التحقق من إعدادات Material Design",
                            FontSize = 16,
                            HorizontalAlignment = HorizontalAlignment.Center,
                            VerticalAlignment = VerticalAlignment.Center,
                            TextAlignment = TextAlignment.Center,
                            FlowDirection = FlowDirection.RightToLeft,
                            TextWrapping = TextWrapping.Wrap,
                            Margin = new Thickness(20)
                        }
                    };
                    fallbackWindow.Show();
                }
                catch (Exception fallbackEx)
                {
                    MessageBox.Show($"فشل في إنشاء النافذة البديلة: {fallbackEx.Message}", "خطأ حرج", MessageBoxButton.OK, MessageBoxImage.Error);
                    Shutdown();
                    return;
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في بدء التطبيق: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
        }
    }

    private void ConfigureServices(IServiceCollection services)
    {
        // Logging
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Information);
        });

        // Database
        services.AddDbContext<DatabaseContext>();

        // Repositories
        services.AddScoped<IInvoiceRepository, InvoiceRepository>();
        services.AddScoped<ISupplierRepository, SupplierRepository>();
        services.AddScoped<IPaymentRepository, PaymentRepository>();
        services.AddScoped<IOfferRepository, OfferRepository>();

        // Services
        services.AddSingleton<INavigationService, NavigationService>();
        services.AddScoped<IInvoiceService, InvoiceService>();
        services.AddScoped<ISupplierService, SupplierService>();
        services.AddScoped<IPaymentService, PaymentService>();
        services.AddScoped<IOfferService, OfferService>();
        services.AddScoped<IOfferAttachmentService, OfferAttachmentService>();
        services.AddScoped<IAdvancedOfferAnalysisService, AdvancedOfferAnalysisService>();
        services.AddScoped<IOfferReportService, OfferReportService>();
        services.AddScoped<IToastService, ToastService>();

        services.AddScoped<ISuccessNotificationService, SuccessNotificationService>();
        services.AddScoped<IDashboardService, DashboardService>();
        services.AddScoped<IValidationService, ValidationService>();
        services.AddSingleton<IGlobalExceptionHandler, GlobalExceptionHandler>();
        services.AddScoped<IRetryService, RetryService>();

        // Enhanced Services - الخدمات المحسنة
        services.AddSingleton<ILoggingService, LoggingService>();
        services.AddScoped<IEnhancedErrorHandlingService, EnhancedErrorHandlingService>();
        services.AddSingleton<ISettingsService, SettingsService>();

        // Security Services - خدمات الأمان
        services.AddSingleton<IEncryptionService, EncryptionService>();
        services.AddSingleton<ISecurityService, SecurityService>();

        // Import/Export Services - خدمات التصدير والاستيراد (متوفرة في SettingsPage)
        services.AddSingleton<IImportExportService, ImportExportService>();

        // Backup/Restore Services - خدمات النسخ الاحتياطي والاستعادة (متوفرة في SettingsPage)
        services.AddSingleton<IBackupRestoreService, BackupRestoreService>();

        // Performance Services - خدمات تحسين الأداء
        services.AddSingleton<ICacheService, CacheService>();

        // Cloud Services
        services.AddScoped<ICloudStorageService, GoogleDriveService>();
        services.AddSingleton<CloudSyncService>();
        services.AddHostedService<CloudSyncService>(provider => provider.GetRequiredService<CloudSyncService>());
        services.AddScoped<CloudFileEncryptionService>();
        services.AddSingleton<CloudPerformanceMonitorService>();

        // Pages - الصفحات الأساسية فقط
        services.AddTransient<DashboardPage>();
        services.AddTransient<InvoicesPage>();
        services.AddTransient<SuppliersPage>();
        services.AddTransient<PaymentsPage>();
        services.AddTransient<OffersPage>();
        services.AddTransient<ReportsPage>();
        services.AddTransient<ChartsPage>();
        services.AddTransient<SearchPage>();
        services.AddTransient<SettingsPage>();
        services.AddTransient<CloudStoragePage>();

        // Windows - فقط النوافذ الموجودة
        services.AddTransient<MainWindow>();
        services.AddTransient<AddEditInvoiceWindow>();
        services.AddTransient<AddEditPaymentWindow>();
        services.AddTransient<AddEditOfferWindow>();
        services.AddTransient<AddEditSupplierWindow>();

        // Initialize attachment directories
        try
        {
            FileHelper.InitializeAttachmentDirectories();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"تحذير: فشل في إنشاء مجلدات المرفقات: {ex.Message}", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    protected override void OnExit(ExitEventArgs e)
    {
        ServiceProvider?.Dispose();
        base.OnExit(e);
    }
}