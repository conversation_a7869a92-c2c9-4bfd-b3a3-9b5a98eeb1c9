<Window x:Class="HR_InvoiceArchiver.Windows.SupplierStatementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:converters="clr-namespace:HR_InvoiceArchiver.Converters"
        Title="كشف حساب المورد"
        Height="900" Width="1400"
        WindowStartupLocation="CenterOwner"
        Background="Transparent"
        FontFamily="{DynamicResource MaterialDesignFont}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize"
        MinHeight="800" MinWidth="1200"
        WindowStyle="None"
        AllowsTransparency="True">

    <Window.Resources>
        <!-- Converters -->
        <converters:StringNullOrEmptyConverter x:Key="StringNullOrEmptyConverter"/>

        <!-- Modern Gradient Brushes -->
        <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#10B981" Offset="0"/>
            <GradientStop Color="#059669" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#40000000" BlurRadius="20" ShadowDepth="8" Direction="270"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#40000000" BlurRadius="8" ShadowDepth="2" Direction="270"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="White" CornerRadius="15">
        <Border.Effect>
            <DropShadowEffect Color="#40000000" BlurRadius="30" ShadowDepth="15" Direction="270"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Custom Title Bar -->
            <Border Grid.Row="0"
                    Background="{StaticResource PrimaryGradientBrush}"
                    CornerRadius="15,15,0,0"
                    Height="70"
                    MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Window Title -->
                    <StackPanel Grid.Column="0"
                              Orientation="Horizontal"
                              VerticalAlignment="Center"
                              Margin="25,0,0,0">
                        <materialDesign:PackIcon Kind="FileDocumentEdit"
                                               Width="24" Height="24"
                                               Foreground="White"
                                               VerticalAlignment="Center"
                                               Margin="0,0,12,0"/>
                        <TextBlock x:Name="WindowTitleTextBlock"
                                 Text="كشف حساب المورد"
                                 FontSize="20"
                                 FontWeight="Bold"
                                 Foreground="White"
                                 VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- Window Controls -->
                    <Button Grid.Column="1"
                            x:Name="MinimizeButton"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="40" Height="40"
                            Foreground="White"
                            Click="MinimizeButton_Click"
                            Margin="5,0">
                        <materialDesign:PackIcon Kind="WindowMinimize" Width="16" Height="16"/>
                    </Button>

                    <Button Grid.Column="2"
                            x:Name="CloseButton"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="40" Height="40"
                            Foreground="White"
                            Click="CloseButton_Click"
                            Margin="5,0,15,0">
                        <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                    </Button>
                </Grid>
            </Border>

            <!-- Main Content -->
            <Grid Grid.Row="1" Margin="20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Supplier Info Header -->
                <materialDesign:Card Grid.Row="0" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,25">
                    <Border Background="{StaticResource SecondaryGradientBrush}" CornerRadius="8" Padding="30">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Supplier Icon -->
                            <Border Grid.Column="0" Background="#26FFFFFF" CornerRadius="25"
                                   Width="80" Height="80" Margin="0,0,25,0">
                                <materialDesign:PackIcon Kind="AccountCircle" Width="40" Height="40"
                                                       Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>

                            <!-- Supplier Details -->
                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                <TextBlock x:Name="SupplierNameTextBlock" Text="اسم المورد" FontSize="28" FontWeight="Bold" Foreground="White"/>
                                <TextBlock x:Name="SupplierDetailsTextBlock" Text="تفاصيل المورد" FontSize="16"
                                         Foreground="#E0E7FF" Margin="0,6,0,0"/>
                            </StackPanel>

                            <!-- Action Buttons -->
                            <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                                <Button x:Name="PrintButton" Click="PrintButton_Click"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="White" Foreground="{StaticResource SecondaryGradientBrush}"
                                       Margin="8,0" MinWidth="120" Height="45">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Printer" Width="18" Height="18" Margin="0,0,8,0"/>
                                        <TextBlock Text="طباعة" FontSize="14" FontWeight="Medium"/>
                                    </StackPanel>
                                </Button>

                                <Button x:Name="ExportButton" Click="ExportButton_Click"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="White" Foreground="{StaticResource SecondaryGradientBrush}"
                                       Margin="8,0" MinWidth="120" Height="45">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FileExcel" Width="18" Height="18" Margin="0,0,8,0"/>
                                        <TextBlock Text="تصدير" FontSize="14" FontWeight="Medium"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </Grid>
                    </Border>
                </materialDesign:Card>

                <!-- Date Range Filter -->
                <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,25">
                    <Border Background="White" CornerRadius="8" Padding="25">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="📅 من تاريخ:" FontSize="16" FontWeight="Medium"
                                     Foreground="#2D3748" VerticalAlignment="Center" Margin="0,0,15,0"/>

                            <DatePicker Grid.Column="1" x:Name="FromDatePicker"
                                      materialDesign:HintAssist.Hint="تاريخ البداية"
                                      Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                      FontSize="14" Height="45" Margin="0,0,25,0"/>

                            <TextBlock Grid.Column="2" Text="📅 إلى تاريخ:" FontSize="16" FontWeight="Medium"
                                     Foreground="#2D3748" VerticalAlignment="Center" Margin="0,0,15,0"/>

                            <DatePicker Grid.Column="3" x:Name="ToDatePicker"
                                      materialDesign:HintAssist.Hint="تاريخ النهاية"
                                      Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                      FontSize="14" Height="45" Margin="0,0,25,0"/>

                            <Button Grid.Column="4" x:Name="FilterButton" Click="FilterButton_Click"
                                   Style="{StaticResource ModernButtonStyle}"
                                   Background="{StaticResource PrimaryGradientBrush}"
                                   MinWidth="120" Height="45">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Filter" Width="18" Height="18" Margin="0,0,8,0"/>
                                    <TextBlock Text="تطبيق" FontSize="14" FontWeight="Medium"/>
                                </StackPanel>
                            </Button>
                        </Grid>
                    </Border>
                </materialDesign:Card>

                <!-- Summary Statistics -->
                <UniformGrid Grid.Row="2" Columns="4" Margin="0,0,0,25">
                    <!-- Total Invoices -->
                    <materialDesign:Card Style="{StaticResource ModernCardStyle}">
                        <Border Background="White" CornerRadius="8" Padding="25">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,18">
                                    <Border Background="#EBF4FF" CornerRadius="15" Width="55" Height="55" Margin="0,0,15,0">
                                        <materialDesign:PackIcon Kind="FileDocument" Width="28" Height="28"
                                                               Foreground="#3B82F6" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel VerticalAlignment="Center">
                                        <TextBlock Text="إجمالي الفواتير" FontSize="16" Foreground="#1E293B" FontWeight="Bold"/>
                                        <TextBlock Text="Total Invoices" FontSize="12" Foreground="#64748B" Margin="0,2,0,0"/>
                                    </StackPanel>
                                </StackPanel>

                                <TextBlock Grid.Row="1" x:Name="TotalInvoicesText" Text="0" FontSize="36" FontWeight="Bold"
                                         Foreground="#3B82F6" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                                <Border Grid.Row="2" Background="#F0F9FF" CornerRadius="10" Padding="15,8" HorizontalAlignment="Center">
                                    <TextBlock Text="فاتورة" FontSize="12" Foreground="#3B82F6" FontWeight="SemiBold"/>
                                </Border>
                            </Grid>
                        </Border>
                    </materialDesign:Card>

                    <!-- Total Amount -->
                    <materialDesign:Card Style="{StaticResource ModernCardStyle}">
                        <Border Background="White" CornerRadius="8" Padding="25">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,18">
                                    <Border Background="#ECFDF5" CornerRadius="15" Width="55" Height="55" Margin="0,0,15,0">
                                        <materialDesign:PackIcon Kind="CashMultiple" Width="28" Height="28"
                                                               Foreground="#10B981" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel VerticalAlignment="Center">
                                        <TextBlock Text="إجمالي المبلغ" FontSize="16" Foreground="#1E293B" FontWeight="Bold"/>
                                        <TextBlock Text="Total Amount" FontSize="12" Foreground="#64748B" Margin="0,2,0,0"/>
                                    </StackPanel>
                                </StackPanel>

                                <TextBlock Grid.Row="1" x:Name="TotalAmountText" Text="0 د.ع" FontSize="36" FontWeight="Bold"
                                         Foreground="#10B981" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                                <Border Grid.Row="2" Background="#F0FDF4" CornerRadius="10" Padding="15,8" HorizontalAlignment="Center">
                                    <TextBlock Text="دينار عراقي" FontSize="12" Foreground="#10B981" FontWeight="SemiBold"/>
                                </Border>
                            </Grid>
                        </Border>
                    </materialDesign:Card>

                    <!-- Paid Amount -->
                    <materialDesign:Card Style="{StaticResource ModernCardStyle}">
                        <Border Background="White" CornerRadius="8" Padding="25">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,18">
                                    <Border Background="#F0F9FF" CornerRadius="15" Width="55" Height="55" Margin="0,0,15,0">
                                        <materialDesign:PackIcon Kind="CashCheck" Width="28" Height="28"
                                                               Foreground="#0EA5E9" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel VerticalAlignment="Center">
                                        <TextBlock Text="المبلغ المسدد" FontSize="16" Foreground="#1E293B" FontWeight="Bold"/>
                                        <TextBlock Text="Paid Amount" FontSize="12" Foreground="#64748B" Margin="0,2,0,0"/>
                                    </StackPanel>
                                </StackPanel>

                                <TextBlock Grid.Row="1" x:Name="PaidAmountText" Text="0 د.ع" FontSize="36" FontWeight="Bold"
                                         Foreground="#0EA5E9" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                                <Border Grid.Row="2" Background="#F0F9FF" CornerRadius="10" Padding="15,8" HorizontalAlignment="Center">
                                    <TextBlock Text="دينار عراقي" FontSize="12" Foreground="#0EA5E9" FontWeight="SemiBold"/>
                                </Border>
                            </Grid>
                        </Border>
                    </materialDesign:Card>

                    <!-- Outstanding Amount -->
                    <materialDesign:Card Style="{StaticResource ModernCardStyle}">
                        <Border Background="White" CornerRadius="8" Padding="25">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,18">
                                    <Border Background="#FEF3F2" CornerRadius="15" Width="55" Height="55" Margin="0,0,15,0">
                                        <materialDesign:PackIcon Kind="CashMinus" Width="28" Height="28"
                                                               Foreground="#DC2626" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel VerticalAlignment="Center">
                                        <TextBlock Text="المبلغ المستحق" FontSize="16" Foreground="#1E293B" FontWeight="Bold"/>
                                        <TextBlock Text="Outstanding" FontSize="12" Foreground="#64748B" Margin="0,2,0,0"/>
                                    </StackPanel>
                                </StackPanel>

                                <TextBlock Grid.Row="1" x:Name="OutstandingAmountText" Text="0 د.ع" FontSize="36" FontWeight="Bold"
                                         Foreground="#DC2626" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                                <Border Grid.Row="2" Background="#FEF2F2" CornerRadius="10" Padding="15,8" HorizontalAlignment="Center">
                                    <TextBlock Text="دينار عراقي" FontSize="12" Foreground="#DC2626" FontWeight="SemiBold"/>
                                </Border>
                            </Grid>
                        </Border>
                    </materialDesign:Card>
                </UniformGrid>

                <!-- Invoices DataGrid -->
                <materialDesign:Card Grid.Row="3" Style="{StaticResource ModernCardStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Table Header -->
                        <Border Grid.Row="0"
                                Background="{StaticResource PrimaryGradientBrush}"
                                CornerRadius="8,8,0,0"
                                Padding="25,18">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="TableLarge"
                                                       Width="28" Height="28"
                                                       Foreground="White"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,12,0"/>
                                <TextBlock Text="فواتير المورد"
                                           FontSize="20"
                                           FontWeight="Bold"
                                           Foreground="White"
                                           VerticalAlignment="Center"/>
                                <TextBlock x:Name="InvoicesCountTextBlock"
                                           Text="(0 فاتورة)"
                                           FontSize="16"
                                           Foreground="White"
                                           Opacity="0.8"
                                           VerticalAlignment="Center"
                                           Margin="12,0,0,0"/>
                            </StackPanel>
                        </Border>

                        <!-- DataGrid -->
                        <DataGrid x:Name="InvoicesDataGrid"
                                  Grid.Row="1"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  IsReadOnly="True"
                                  SelectionMode="Single"
                                  GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column"
                                  materialDesign:DataGridAssist.CellPadding="12"
                                  materialDesign:DataGridAssist.ColumnHeaderPadding="12"
                                  FontSize="13"
                                  Background="White"
                                  RowBackground="White"
                                  AlternatingRowBackground="#F8F9FA">

                            <DataGrid.ColumnHeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="Background" Value="#F8F9FA"/>
                                    <Setter Property="Foreground" Value="#2D3748"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="15"/>
                                    <Setter Property="Height" Value="55"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="VerticalContentAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="10"/>
                                </Style>
                            </DataGrid.ColumnHeaderStyle>

                            <DataGrid.RowStyle>
                                <Style TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
                                    <Setter Property="Height" Value="60"/>
                                    <Setter Property="VerticalContentAlignment" Value="Center"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#E6F3FF"/>
                                        </Trigger>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter Property="Background" Value="#CCE7FF"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.RowStyle>

                            <DataGrid.CellStyle>
                                <Style TargetType="DataGridCell" BasedOn="{StaticResource MaterialDesignDataGridCell}">
                                    <Setter Property="VerticalContentAlignment" Value="Center"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="10,6"/>
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="FontWeight" Value="Medium"/>
                                </Style>
                            </DataGrid.CellStyle>

                            <DataGrid.Columns>
                                <DataGridTextColumn Header="📄 رقم الفاتورة"
                                                    Binding="{Binding InvoiceNumber}"
                                                    Width="140">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="Foreground" Value="#667eea"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <DataGridTextColumn Header="📅 تاريخ الفاتورة"
                                                    Binding="{Binding InvoiceDate, StringFormat='{}{0:yyyy/MM/dd}'}"
                                                    Width="150">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="FontWeight" Value="Medium"/>
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <DataGridTextColumn Header="💰 المبلغ الإجمالي"
                                                    Binding="{Binding Amount, StringFormat='{}{0:N0} د.ع'}"
                                                    Width="160">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="Foreground" Value="#10B981"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <DataGridTextColumn Header="💵 المبلغ المسدد"
                                                    Binding="{Binding PaidAmount, StringFormat='{}{0:N0} د.ع'}"
                                                    Width="160">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="Foreground" Value="#0EA5E9"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <DataGridTextColumn Header="🔴 المبلغ المستحق"
                                                    Binding="{Binding RemainingAmount, StringFormat='{}{0:N0} د.ع'}"
                                                    Width="160">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                                            <Setter Property="FontSize" Value="13"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="Foreground" Value="#DC2626"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <DataGridTextColumn Header="📊 الحالة"
                                                    Binding="{Binding Status}"
                                                    Width="140">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                                            <Setter Property="FontSize" Value="12"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <DataGridTextColumn Header="📝 الوصف"
                                                    Binding="{Binding Description}"
                                                    Width="250">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                                            <Setter Property="FontSize" Value="12"/>
                                            <Setter Property="FontWeight" Value="Medium"/>
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="TextWrapping" Value="Wrap"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </materialDesign:Card>

                <!-- Bottom Summary -->
                <materialDesign:Card Grid.Row="4" Style="{StaticResource ModernCardStyle}" Margin="0,25,0,0">
                    <Border Background="{StaticResource PrimaryGradientBrush}" CornerRadius="8" Padding="25">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Summary Text -->
                            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                <TextBlock Text="📊 ملخص كشف الحساب:" FontSize="18" FontWeight="Bold"
                                         Foreground="White" Margin="0,0,25,0"/>
                                <TextBlock x:Name="SummaryTextBlock" Text="إجمالي: 0 فاتورة | المبلغ: 0 د.ع | المسدد: 0 د.ع | المستحق: 0 د.ع"
                                         FontSize="16" Foreground="White" Opacity="0.9"/>
                            </StackPanel>

                            <!-- Last Updated -->
                            <TextBlock Grid.Column="1" Text="آخر تحديث: الآن" FontSize="14"
                                     Foreground="White" Opacity="0.8" VerticalAlignment="Center"/>
                        </Grid>
                    </Border>
                </materialDesign:Card>
            </Grid>

            <!-- Loading Panel -->
            <Border x:Name="LoadingPanel"
                    Grid.RowSpan="2"
                    Background="#CC000000"
                    Visibility="Collapsed">
                <materialDesign:Card HorizontalAlignment="Center" VerticalAlignment="Center"
                                   Padding="40,30" Style="{StaticResource ModernCardStyle}">
                    <StackPanel HorizontalAlignment="Center">
                        <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                    Width="50" Height="50"
                                    IsIndeterminate="True"
                                    Foreground="{StaticResource PrimaryGradientBrush}"/>
                        <TextBlock Text="جاري تحميل كشف الحساب..."
                                  FontSize="14" FontWeight="Medium"
                                  Foreground="#2D3748"
                                  HorizontalAlignment="Center"
                                  Margin="0,15,0,0"/>
                    </StackPanel>
                </materialDesign:Card>
            </Border>
        </Grid>
    </Border>
</Window>
