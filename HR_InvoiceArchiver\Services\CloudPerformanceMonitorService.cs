using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// خدمة مراقبة أداء التخزين السحابي
    /// </summary>
    public class CloudPerformanceMonitorService
    {
        private readonly ILogger<CloudPerformanceMonitorService>? _logger;
        private readonly ConcurrentDictionary<string, PerformanceMetric> _metrics;
        private readonly ConcurrentQueue<OperationLog> _operationLogs;
        private readonly object _lockObject = new();

        // إحصائيات الأداء
        public CloudPerformanceStats CurrentStats { get; private set; }

        public CloudPerformanceMonitorService(ILogger<CloudPerformanceMonitorService>? logger = null)
        {
            _logger = logger;
            _metrics = new ConcurrentDictionary<string, PerformanceMetric>();
            _operationLogs = new ConcurrentQueue<OperationLog>();
            CurrentStats = new CloudPerformanceStats();
        }

        /// <summary>
        /// بدء مراقبة عملية
        /// </summary>
        public PerformanceTracker StartOperation(string operationType, string fileName = "", long fileSize = 0)
        {
            // التحقق من صحة المدخلات
            if (string.IsNullOrWhiteSpace(operationType))
                throw new ArgumentException("نوع العملية لا يمكن أن يكون فارغاً", nameof(operationType));

            var tracker = new PerformanceTracker(operationType, fileName, fileSize, this);

            _logger?.LogDebug($"بدء مراقبة العملية: {operationType} - {fileName}");

            return tracker;
        }

        /// <summary>
        /// إنهاء مراقبة عملية
        /// </summary>
        internal void EndOperation(PerformanceTracker tracker)
        {
            try
            {
                var duration = tracker.ElapsedTime;
                var operationLog = new OperationLog
                {
                    OperationType = tracker.OperationType,
                    FileName = tracker.FileName,
                    FileSize = tracker.FileSize,
                    Duration = duration,
                    Success = tracker.Success,
                    ErrorMessage = tracker.ErrorMessage,
                    Timestamp = DateTime.Now
                };

                // إضافة إلى سجل العمليات
                _operationLogs.Enqueue(operationLog);

                // تنظيف السجل إذا تجاوز الحد الأقصى
                while (_operationLogs.Count > 1000)
                {
                    _operationLogs.TryDequeue(out _);
                }

                // تحديث المقاييس
                UpdateMetrics(operationLog);

                // تحديث الإحصائيات
                UpdateCurrentStats();

                _logger?.LogInformation($"انتهت العملية: {tracker.OperationType} - {tracker.FileName} في {duration.TotalSeconds:F2} ثانية");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إنهاء مراقبة العملية");
            }
        }

        /// <summary>
        /// تحديث المقاييس
        /// </summary>
        private void UpdateMetrics(OperationLog log)
        {
            lock (_lockObject)
            {
                var key = log.OperationType;
                
                if (_metrics.TryGetValue(key, out var metric))
                {
                    metric.TotalOperations++;
                    metric.TotalDuration += log.Duration;
                    metric.TotalDataSize += log.FileSize;
                    
                    if (log.Success)
                    {
                        metric.SuccessfulOperations++;
                    }
                    else
                    {
                        metric.FailedOperations++;
                    }

                    // تحديث أسرع وأبطأ عملية
                    if (log.Duration < metric.FastestOperation || metric.FastestOperation == TimeSpan.Zero)
                    {
                        metric.FastestOperation = log.Duration;
                    }

                    if (log.Duration > metric.SlowestOperation)
                    {
                        metric.SlowestOperation = log.Duration;
                    }
                }
                else
                {
                    _metrics[key] = new PerformanceMetric
                    {
                        OperationType = key,
                        TotalOperations = 1,
                        SuccessfulOperations = log.Success ? 1 : 0,
                        FailedOperations = log.Success ? 0 : 1,
                        TotalDuration = log.Duration,
                        TotalDataSize = log.FileSize,
                        FastestOperation = log.Duration,
                        SlowestOperation = log.Duration
                    };
                }
            }
        }

        /// <summary>
        /// تحديث الإحصائيات الحالية
        /// </summary>
        private void UpdateCurrentStats()
        {
            lock (_lockObject)
            {
                var recentLogs = _operationLogs.Where(log => log.Timestamp > DateTime.Now.AddHours(-1)).ToList();
                
                CurrentStats = new CloudPerformanceStats
                {
                    TotalOperations = _operationLogs.Count,
                    RecentOperations = recentLogs.Count,
                    SuccessRate = CalculateSuccessRate(),
                    AverageUploadSpeed = CalculateAverageSpeed("Upload"),
                    AverageDownloadSpeed = CalculateAverageSpeed("Download"),
                    TotalDataTransferred = _metrics.Values.Sum(m => m.TotalDataSize),
                    AverageResponseTime = CalculateAverageResponseTime(),
                    LastUpdateTime = DateTime.Now
                };
            }
        }

        /// <summary>
        /// حساب معدل النجاح
        /// </summary>
        private double CalculateSuccessRate()
        {
            var totalOps = _metrics.Values.Sum(m => m.TotalOperations);
            var successfulOps = _metrics.Values.Sum(m => m.SuccessfulOperations);
            
            return totalOps > 0 ? (double)successfulOps / totalOps * 100 : 0;
        }

        /// <summary>
        /// حساب متوسط السرعة
        /// </summary>
        private double CalculateAverageSpeed(string operationType)
        {
            if (_metrics.TryGetValue(operationType, out var metric) && metric.TotalDuration.TotalSeconds > 0)
            {
                return metric.TotalDataSize / metric.TotalDuration.TotalSeconds; // bytes per second
            }
            return 0;
        }

        /// <summary>
        /// حساب متوسط وقت الاستجابة
        /// </summary>
        private double CalculateAverageResponseTime()
        {
            var totalOps = _metrics.Values.Sum(m => m.TotalOperations);
            var totalDuration = _metrics.Values.Sum(m => m.TotalDuration.TotalMilliseconds);
            
            return totalOps > 0 ? totalDuration / totalOps : 0;
        }

        /// <summary>
        /// الحصول على تقرير الأداء
        /// </summary>
        public CloudPerformanceReport GetPerformanceReport(TimeSpan? period = null)
        {
            var cutoffTime = period.HasValue ? DateTime.Now - period.Value : DateTime.MinValue;
            var relevantLogs = _operationLogs.Where(log => log.Timestamp >= cutoffTime).ToList();

            return new CloudPerformanceReport
            {
                GeneratedAt = DateTime.Now,
                Period = period ?? TimeSpan.FromDays(30),
                TotalOperations = relevantLogs.Count,
                SuccessfulOperations = relevantLogs.Count(log => log.Success),
                FailedOperations = relevantLogs.Count(log => !log.Success),
                TotalDataTransferred = relevantLogs.Sum(log => log.FileSize),
                AverageOperationTime = relevantLogs.Any() ? 
                    TimeSpan.FromMilliseconds(relevantLogs.Average(log => log.Duration.TotalMilliseconds)) : 
                    TimeSpan.Zero,
                FastestOperation = relevantLogs.Any() ? relevantLogs.Min(log => log.Duration) : TimeSpan.Zero,
                SlowestOperation = relevantLogs.Any() ? relevantLogs.Max(log => log.Duration) : TimeSpan.Zero,
                OperationsByType = relevantLogs.GroupBy(log => log.OperationType)
                    .ToDictionary(g => g.Key, g => g.Count()),
                HourlyStats = GenerateHourlyStats(relevantLogs)
            };
        }

        /// <summary>
        /// توليد إحصائيات ساعية
        /// </summary>
        private Dictionary<int, int> GenerateHourlyStats(List<OperationLog> logs)
        {
            return logs.GroupBy(log => log.Timestamp.Hour)
                      .ToDictionary(g => g.Key, g => g.Count());
        }

        /// <summary>
        /// الحصول على العمليات الأخيرة
        /// </summary>
        public List<OperationLog> GetRecentOperations(int count = 50)
        {
            return _operationLogs.TakeLast(count).OrderByDescending(log => log.Timestamp).ToList();
        }

        /// <summary>
        /// مسح الإحصائيات
        /// </summary>
        public void ClearStats()
        {
            lock (_lockObject)
            {
                _metrics.Clear();
                while (_operationLogs.TryDequeue(out _)) { }
                CurrentStats = new CloudPerformanceStats();
                
                _logger?.LogInformation("تم مسح إحصائيات الأداء");
            }
        }

        /// <summary>
        /// تصدير الإحصائيات
        /// </summary>
        public async Task<string> ExportStatsAsync(string filePath)
        {
            try
            {
                var report = GetPerformanceReport();
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(report, Newtonsoft.Json.Formatting.Indented);
                
                await System.IO.File.WriteAllTextAsync(filePath, json);
                
                _logger?.LogInformation($"تم تصدير إحصائيات الأداء إلى: {filePath}");
                return filePath;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تصدير الإحصائيات إلى: {filePath}");
                throw;
            }
        }
    }

    /// <summary>
    /// متتبع الأداء للعمليات
    /// </summary>
    public class PerformanceTracker : IDisposable
    {
        private readonly Stopwatch _stopwatch;
        private readonly CloudPerformanceMonitorService _monitor;

        public string OperationType { get; }
        public string FileName { get; }
        public long FileSize { get; }
        public bool Success { get; set; } = true;
        public string ErrorMessage { get; set; } = string.Empty;
        public TimeSpan ElapsedTime => _stopwatch.Elapsed;

        internal PerformanceTracker(string operationType, string fileName, long fileSize, CloudPerformanceMonitorService monitor)
        {
            OperationType = operationType;
            FileName = fileName;
            FileSize = fileSize;
            _monitor = monitor;
            _stopwatch = Stopwatch.StartNew();
        }

        public void MarkAsFailure(string errorMessage)
        {
            Success = false;
            ErrorMessage = errorMessage;
        }

        public void Dispose()
        {
            _stopwatch.Stop();
            _monitor.EndOperation(this);
        }
    }

    /// <summary>
    /// مقياس الأداء
    /// </summary>
    public class PerformanceMetric
    {
        public string OperationType { get; set; } = string.Empty;
        public int TotalOperations { get; set; }
        public int SuccessfulOperations { get; set; }
        public int FailedOperations { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public long TotalDataSize { get; set; }
        public TimeSpan FastestOperation { get; set; }
        public TimeSpan SlowestOperation { get; set; }
        public TimeSpan AverageOperationTime => TotalOperations > 0 ? 
            TimeSpan.FromMilliseconds(TotalDuration.TotalMilliseconds / TotalOperations) : TimeSpan.Zero;
    }

    /// <summary>
    /// سجل العملية
    /// </summary>
    public class OperationLog
    {
        public string OperationType { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public TimeSpan Duration { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// إحصائيات الأداء الحالية
    /// </summary>
    public class CloudPerformanceStats
    {
        public int TotalOperations { get; set; }
        public int RecentOperations { get; set; }
        public double SuccessRate { get; set; }
        public double AverageUploadSpeed { get; set; } // bytes per second
        public double AverageDownloadSpeed { get; set; } // bytes per second
        public long TotalDataTransferred { get; set; }
        public double AverageResponseTime { get; set; } // milliseconds
        public DateTime LastUpdateTime { get; set; }
    }

    /// <summary>
    /// تقرير الأداء
    /// </summary>
    public class CloudPerformanceReport
    {
        public DateTime GeneratedAt { get; set; }
        public TimeSpan Period { get; set; }
        public int TotalOperations { get; set; }
        public int SuccessfulOperations { get; set; }
        public int FailedOperations { get; set; }
        public long TotalDataTransferred { get; set; }
        public TimeSpan AverageOperationTime { get; set; }
        public TimeSpan FastestOperation { get; set; }
        public TimeSpan SlowestOperation { get; set; }
        public Dictionary<string, int> OperationsByType { get; set; } = new();
        public Dictionary<int, int> HourlyStats { get; set; } = new();
    }
}
