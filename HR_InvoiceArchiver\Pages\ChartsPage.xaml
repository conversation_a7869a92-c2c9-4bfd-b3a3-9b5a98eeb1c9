<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="HR_InvoiceArchiver.Pages.ChartsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             FlowDirection="RightToLeft"
             FontFamily="{DynamicResource MaterialDesignFont}"
             TextElement.Foreground="{DynamicResource MaterialDesignBody}"
             Background="#F8F9FF">

    <UserControl.Resources>
        <ResourceDictionary>
            <!-- Modern Card Style -->
            <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
                <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Background" Value="White"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="#40000000" BlurRadius="20" ShadowDepth="8" Direction="270"/>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- Enhanced Gradient Brushes -->
            <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#667eea" Offset="0"/>
                <GradientStop Color="#764ba2" Offset="1"/>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#10B981" Offset="0"/>
                <GradientStop Color="#059669" Offset="1"/>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="WarningGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#F59E0B" Offset="0"/>
                <GradientStop Color="#D97706" Offset="1"/>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="InfoGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#3B82F6" Offset="0"/>
                <GradientStop Color="#1D4ED8" Offset="1"/>
            </LinearGradientBrush>

            <!-- Statistics Card Style -->
            <Style x:Key="StatisticsCardStyle" TargetType="materialDesign:Card">
                <Setter Property="Padding" Value="20"/>
                <Setter Property="Margin" Value="0,0,8,0"/>
                <Setter Property="MinHeight" Value="120"/>
                <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4"/>
                <Setter Property="Background" Value="White"/>
            </Style>

            <!-- Chart Card Style -->
            <Style x:Key="ChartCardStyle" TargetType="materialDesign:Card">
                <Setter Property="Padding" Value="24"/>
                <Setter Property="Margin" Value="0,0,12,0"/>
                <Setter Property="MinHeight" Value="350"/>
                <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
                <Setter Property="Background" Value="White"/>
            </Style>

            <!-- Chart Button Styles -->
            <Style x:Key="ChartButtonStyle" TargetType="Border">
                <Setter Property="Background" Value="White"/>
                <Setter Property="CornerRadius" Value="12"/>
                <Setter Property="Margin" Value="8,0,0,0"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Opacity" Value="0.7"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="#40000000" Opacity="0.15" BlurRadius="15" ShadowDepth="5"/>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Opacity" Value="0.9"/>
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="#40000000" Opacity="0.25" BlurRadius="20" ShadowDepth="8"/>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="SelectedChartButtonStyle" TargetType="Border" BasedOn="{StaticResource ChartButtonStyle}">
                <Setter Property="Opacity" Value="1.0"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Blue" Opacity="0.3" BlurRadius="20" ShadowDepth="8"/>
                    </Setter.Value>
                </Setter>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <!-- Main Container Grid -->
    <Grid>
        <!-- Loading Overlay -->
        <Grid x:Name="LoadingOverlay" Background="#80FFFFFF" Visibility="Collapsed" Panel.ZIndex="1000">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon x:Name="LoadingIcon" Kind="Loading" Width="48" Height="48"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}">
                    <materialDesign:PackIcon.RenderTransform>
                        <RotateTransform Angle="0"/>
                    </materialDesign:PackIcon.RenderTransform>
                </materialDesign:PackIcon>
                <TextBlock Text="جاري تحميل المخططات..." Margin="0,16,0,0"
                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                         HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>

        <!-- Main Content -->
        <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
            <Grid Margin="24" x:Name="MainContent">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Enhanced Modern Header -->
                <Border Grid.Row="0"
                        Background="{StaticResource SidebarGradient}"
                        Margin="0,0,0,20"
                        CornerRadius="20">
                    <Border.Effect>
                        <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="15" ShadowDepth="5"/>
                    </Border.Effect>

                    <Grid Margin="30,25">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Icon with glow effect -->
                        <Border Grid.Column="0"
                               Background="#30FFFFFF"
                               Width="60" Height="60"
                               CornerRadius="30"
                               VerticalAlignment="Center"
                               Margin="0,0,20,0">
                            <Border.Effect>
                                <DropShadowEffect Color="White" Opacity="0.3" BlurRadius="10" ShadowDepth="0"/>
                            </Border.Effect>
                            <materialDesign:PackIcon Kind="ChartLine"
                                                   Width="32" Height="32"
                                                   Foreground="White"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"/>
                        </Border>

                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="المخططات البيانية والإحصائيات"
                                     FontSize="28"
                                     FontWeight="Bold"
                                     Foreground="White">
                                <TextBlock.Effect>
                                    <DropShadowEffect Color="Black" Opacity="0.3" BlurRadius="4" ShadowDepth="1"/>
                                </TextBlock.Effect>
                            </TextBlock>
                            <TextBlock Text="تحليل مرئي شامل للبيانات المالية مع مخططات تفاعلية متقدمة"
                                     FontSize="16"
                                     FontWeight="Medium"
                                     Foreground="#E0FFFFFF"
                                     Margin="0,6,0,0">
                                <TextBlock.Effect>
                                    <DropShadowEffect Color="Black" Opacity="0.2" BlurRadius="3" ShadowDepth="1"/>
                                </TextBlock.Effect>
                            </TextBlock>
                        </StackPanel>

                        <!-- Enhanced Action Buttons -->
                        <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                            <Button x:Name="RefreshChartsButton"
                                    Style="{StaticResource MaterialDesignIconButton}"
                                    Width="50" Height="50"
                                    Margin="8,0"
                                    ToolTip="تحديث المخططات"
                                    Click="RefreshChartsButton_Click">
                                <materialDesign:PackIcon Kind="Refresh"
                                                       Width="24" Height="24"
                                                       Foreground="White"/>
                            </Button>

                            <Button x:Name="ExportChartsButton"
                                    Style="{StaticResource MaterialDesignIconButton}"
                                    Width="50" Height="50"
                                    Margin="8,0"
                                    ToolTip="تصدير المخططات"
                                    Click="ExportChartsButton_Click">
                                <materialDesign:PackIcon Kind="Export"
                                                       Width="24" Height="24"
                                                       Foreground="White"/>
                            </Button>

                            <Button x:Name="SettingsButton"
                                    Style="{StaticResource MaterialDesignIconButton}"
                                    Width="50" Height="50"
                                    Margin="8,0"
                                    ToolTip="إعدادات المخططات"
                                    Click="SettingsButton_Click">
                                <materialDesign:PackIcon Kind="Settings"
                                                       Width="24" Height="24"
                                                       Foreground="White"/>
                            </Button>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Enhanced Statistics Cards Section -->
                <Grid Grid.Row="1" Margin="0,0,0,24">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Section Header -->
                    <TextBlock Grid.ColumnSpan="4"
                             Text="📊 الإحصائيات السريعة"
                             FontSize="20"
                             FontWeight="SemiBold"
                             Foreground="#2D3748"
                             Margin="0,0,0,16"
                             HorizontalAlignment="Right"/>

                    <!-- Enhanced Total Invoices Card -->
                    <Border Grid.Column="0"
                            Background="White"
                            CornerRadius="16"
                            Margin="0,0,12,0"
                            Padding="24">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.08" BlurRadius="20" ShadowDepth="8"/>
                        </Border.Effect>

                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Icon and Title -->
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                                <Border Background="{StaticResource PrimaryGradientBrush}"
                                       CornerRadius="12"
                                       Width="48" Height="48"
                                       Margin="0,0,16,0">
                                    <materialDesign:PackIcon Kind="FileDocument"
                                                           Width="24" Height="24"
                                                           Foreground="White"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="إجمالي الفواتير"
                                             FontSize="14"
                                             FontWeight="Medium"
                                             Foreground="#64748B"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- Main Value -->
                            <TextBlock Grid.Row="1"
                                     x:Name="TotalInvoicesText"
                                     Text="0"
                                     FontSize="36"
                                     FontWeight="Bold"
                                     Foreground="#1E293B"
                                     VerticalAlignment="Center"/>

                            <!-- Change Indicator -->
                            <Border Grid.Row="2"
                                   Background="#F0FDF4"
                                   CornerRadius="8"
                                   Padding="12,8">
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="TrendingUp"
                                                           Width="16" Height="16"
                                                           Foreground="#10B981"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock x:Name="InvoicesChangeText"
                                             Text="+12% من الشهر الماضي"
                                             FontSize="12"
                                             FontWeight="Medium"
                                             Foreground="#10B981"
                                             Margin="8,0,0,0"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Border>

                    <!-- Enhanced Total Amount Card -->
                    <Border Grid.Column="1"
                            Background="White"
                            CornerRadius="16"
                            Margin="0,0,12,0"
                            Padding="24">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.08" BlurRadius="20" ShadowDepth="8"/>
                        </Border.Effect>

                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Icon and Title -->
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                                <Border Background="{StaticResource SecondaryGradientBrush}"
                                       CornerRadius="12"
                                       Width="48" Height="48"
                                       Margin="0,0,16,0">
                                    <materialDesign:PackIcon Kind="CurrencyUsd"
                                                           Width="24" Height="24"
                                                           Foreground="White"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="إجمالي المبالغ"
                                             FontSize="14"
                                             FontWeight="Medium"
                                             Foreground="#64748B"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- Main Value -->
                            <TextBlock Grid.Row="1"
                                     x:Name="TotalAmountText"
                                     Text="0 د.ع"
                                     FontSize="36"
                                     FontWeight="Bold"
                                     Foreground="#1E293B"
                                     VerticalAlignment="Center"/>

                            <!-- Change Indicator -->
                            <Border Grid.Row="2"
                                   Background="#F0FDF4"
                                   CornerRadius="8"
                                   Padding="12,8">
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="TrendingUp"
                                                           Width="16" Height="16"
                                                           Foreground="#10B981"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock x:Name="AmountChangeText"
                                             Text="+8% من الشهر الماضي"
                                             FontSize="12"
                                             FontWeight="Medium"
                                             Foreground="#10B981"
                                             Margin="8,0,0,0"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Border>

                    <!-- Enhanced Paid Amount Card -->
                    <Border Grid.Column="2"
                            Background="White"
                            CornerRadius="16"
                            Margin="0,0,12,0"
                            Padding="24">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.08" BlurRadius="20" ShadowDepth="8"/>
                        </Border.Effect>

                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Icon and Title -->
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                                <Border Background="#10B981"
                                       CornerRadius="12"
                                       Width="48" Height="48"
                                       Margin="0,0,16,0">
                                    <materialDesign:PackIcon Kind="CheckCircle"
                                                           Width="24" Height="24"
                                                           Foreground="White"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="المبالغ المدفوعة"
                                             FontSize="14"
                                             FontWeight="Medium"
                                             Foreground="#64748B"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- Main Value -->
                            <TextBlock Grid.Row="1"
                                     x:Name="PaidAmountText"
                                     Text="0 د.ع"
                                     FontSize="36"
                                     FontWeight="Bold"
                                     Foreground="#1E293B"
                                     VerticalAlignment="Center"/>

                            <!-- Change Indicator -->
                            <Border Grid.Row="2"
                                   Background="#F0FDF4"
                                   CornerRadius="8"
                                   Padding="12,8">
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="TrendingUp"
                                                           Width="16" Height="16"
                                                           Foreground="#10B981"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock x:Name="PaidChangeText"
                                             Text="+15% من الشهر الماضي"
                                             FontSize="12"
                                             FontWeight="Medium"
                                             Foreground="#10B981"
                                             Margin="8,0,0,0"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Border>

                    <!-- Enhanced Outstanding Amount Card -->
                    <Border Grid.Column="3"
                            Background="White"
                            CornerRadius="16"
                            Margin="0"
                            Padding="24">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.08" BlurRadius="20" ShadowDepth="8"/>
                        </Border.Effect>

                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Icon and Title -->
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                                <Border Background="{StaticResource WarningGradientBrush}"
                                       CornerRadius="12"
                                       Width="48" Height="48"
                                       Margin="0,0,16,0">
                                    <materialDesign:PackIcon Kind="ClockOutline"
                                                           Width="24" Height="24"
                                                           Foreground="White"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="المبالغ المستحقة"
                                             FontSize="14"
                                             FontWeight="Medium"
                                             Foreground="#64748B"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- Main Value -->
                            <TextBlock Grid.Row="1"
                                     x:Name="OutstandingAmountText"
                                     Text="0 د.ع"
                                     FontSize="36"
                                     FontWeight="Bold"
                                     Foreground="#1E293B"
                                     VerticalAlignment="Center"/>

                            <!-- Change Indicator -->
                            <Border Grid.Row="2"
                                   Background="#FFFBEB"
                                   CornerRadius="8"
                                   Padding="12,8">
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="TrendingDown"
                                                           Width="16" Height="16"
                                                           Foreground="#F59E0B"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock x:Name="OutstandingChangeText"
                                             Text="-5% من الشهر الماضي"
                                             FontSize="12"
                                             FontWeight="Medium"
                                             Foreground="#F59E0B"
                                             Margin="8,0,0,0"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Border>
                </Grid>

                <!-- Enhanced Charts Section -->
                <Grid Grid.Row="2" Margin="0,0,0,20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Charts Section Header -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
                        <Border Background="{StaticResource SidebarGradient}"
                               CornerRadius="12"
                               Width="40" Height="40"
                               Margin="0,0,16,0">
                            <materialDesign:PackIcon Kind="ChartMultiple"
                                                   Width="20" Height="20"
                                                   Foreground="White"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"/>
                        </Border>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="المخططات التفاعلية"
                                     FontSize="22"
                                     FontWeight="Bold"
                                     Foreground="#1E293B"/>
                            <TextBlock Text="تحليل بصري متقدم للبيانات المالية"
                                     FontSize="14"
                                     Foreground="#64748B"
                                     Margin="0,2,0,0"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- First Row of Charts -->
                    <Grid Grid.Row="1" Margin="0,0,0,24">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Enhanced Monthly Trend Chart -->
                        <Border Grid.Column="0"
                                Background="White"
                                CornerRadius="20"
                                Margin="0,0,16,0"
                                Padding="28">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Opacity="0.08" BlurRadius="25" ShadowDepth="10"/>
                            </Border.Effect>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Chart Header -->
                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
                                    <Border Background="{StaticResource PrimaryGradientBrush}"
                                           CornerRadius="12"
                                           Width="44" Height="44"
                                           Margin="0,0,16,0">
                                        <materialDesign:PackIcon Kind="ChartLine"
                                                               Width="22" Height="22"
                                                               Foreground="White"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel VerticalAlignment="Center">
                                        <TextBlock Text="اتجاه المبيعات الشهرية"
                                                 FontSize="20"
                                                 FontWeight="Bold"
                                                 Foreground="#1E293B"/>
                                        <TextBlock Text="تحليل الاتجاهات الزمنية للمبيعات"
                                                 FontSize="13"
                                                 Foreground="#64748B"
                                                 Margin="0,2,0,0"/>
                                    </StackPanel>
                                </StackPanel>

                                <!-- Chart Container -->
                                <Border Grid.Row="1"
                                       Background="#FAFBFC"
                                       CornerRadius="16"
                                       Height="320"
                                       Padding="20">
                                    <Canvas x:Name="MonthlyTrendChart" Background="Transparent"/>
                                </Border>
                            </Grid>
                        </Border>

                        <!-- Enhanced Payment Methods Chart -->
                        <Border Grid.Column="1"
                                Background="White"
                                CornerRadius="20"
                                Margin="0"
                                Padding="28">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Opacity="0.08" BlurRadius="25" ShadowDepth="10"/>
                            </Border.Effect>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Chart Header -->
                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
                                    <Border Background="{StaticResource SecondaryGradientBrush}"
                                           CornerRadius="12"
                                           Width="44" Height="44"
                                           Margin="0,0,16,0">
                                        <materialDesign:PackIcon Kind="ChartPie"
                                                               Width="22" Height="22"
                                                               Foreground="White"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel VerticalAlignment="Center">
                                        <TextBlock Text="طرق الدفع"
                                                 FontSize="20"
                                                 FontWeight="Bold"
                                                 Foreground="#1E293B"/>
                                        <TextBlock Text="توزيع طرق الدفع المستخدمة"
                                                 FontSize="13"
                                                 Foreground="#64748B"
                                                 Margin="0,2,0,0"/>
                                    </StackPanel>
                                </StackPanel>

                                <!-- Chart Container -->
                                <Border Grid.Row="1"
                                       Background="#F0FDF4"
                                       CornerRadius="16"
                                       Height="320"
                                       Padding="20">
                                    <Canvas x:Name="PaymentMethodsChart" Background="Transparent"/>
                                </Border>
                            </Grid>
                        </Border>
                    </Grid>

                    <!-- Enhanced Second Row of Charts -->
                    <Grid Grid.Row="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Enhanced Top Suppliers Chart -->
                        <Border Grid.Column="0"
                                Background="White"
                                CornerRadius="20"
                                Margin="0,0,16,0"
                                Padding="28">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Opacity="0.08" BlurRadius="25" ShadowDepth="10"/>
                            </Border.Effect>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Chart Header -->
                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
                                    <Border Background="#8B5CF6"
                                           CornerRadius="12"
                                           Width="44" Height="44"
                                           Margin="0,0,16,0">
                                        <materialDesign:PackIcon Kind="AccountGroup"
                                                               Width="22" Height="22"
                                                               Foreground="White"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel VerticalAlignment="Center">
                                        <TextBlock Text="أفضل الموردين"
                                                 FontSize="20"
                                                 FontWeight="Bold"
                                                 Foreground="#1E293B"/>
                                        <TextBlock Text="ترتيب الموردين حسب الأداء"
                                                 FontSize="13"
                                                 Foreground="#64748B"
                                                 Margin="0,2,0,0"/>
                                    </StackPanel>
                                </StackPanel>

                                <!-- Chart Container -->
                                <Border Grid.Row="1"
                                       Background="#FAF5FF"
                                       CornerRadius="16"
                                       Height="320"
                                       Padding="20">
                                    <StackPanel x:Name="TopSuppliersPanel" Background="Transparent"/>
                                </Border>
                            </Grid>
                        </Border>

                        <!-- Enhanced Invoice Status Chart -->
                        <Border Grid.Column="1"
                                Background="White"
                                CornerRadius="20"
                                Margin="0"
                                Padding="28">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Opacity="0.08" BlurRadius="25" ShadowDepth="10"/>
                            </Border.Effect>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Chart Header -->
                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
                                    <Border Background="#4ECDC4"
                                           CornerRadius="12"
                                           Width="44" Height="44"
                                           Margin="0,0,16,0">
                                        <materialDesign:PackIcon Kind="ChartDonut"
                                                               Width="22" Height="22"
                                                               Foreground="White"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel VerticalAlignment="Center">
                                        <TextBlock Text="حالة الفواتير"
                                                 FontSize="20"
                                                 FontWeight="Bold"
                                                 Foreground="#1E293B"/>
                                        <TextBlock Text="توزيع حالات الفواتير"
                                                 FontSize="13"
                                                 Foreground="#64748B"
                                                 Margin="0,2,0,0"/>
                                    </StackPanel>
                                </StackPanel>

                                <!-- Chart Container -->
                                <Border Grid.Row="1"
                                       Background="#F0FDFA"
                                       CornerRadius="16"
                                       Height="320"
                                       Padding="20">
                                    <Canvas x:Name="InvoiceStatusChart" Background="Transparent"/>
                                </Border>
                            </Grid>
                        </Border>
                    </Grid>
                </Grid>
            </Grid>
        </ScrollViewer>
    </Grid>
</UserControl>
