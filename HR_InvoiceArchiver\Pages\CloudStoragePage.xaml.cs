using System;
using System.Windows.Controls;
using HR_InvoiceArchiver.Services;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Pages
{
    /// <summary>
    /// صفحة التخزين السحابي
    /// </summary>
    public partial class CloudStoragePage : UserControl
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IToastService _toastService;

        public CloudStoragePage()
        {
            InitializeComponent();
            
            // الحصول على الخدمات من DI Container
            _serviceProvider = App.ServiceProvider;
            _toastService = _serviceProvider.GetRequiredService<IToastService>();
            
            InitializeCloudStorageControl();
        }

        /// <summary>
        /// تهيئة عنصر التحكم في التخزين السحابي
        /// </summary>
        private void InitializeCloudStorageControl()
        {
            try
            {
                // الحصول على الخدمات المطلوبة
                var cloudService = _serviceProvider.GetService<ICloudStorageService>();
                var syncService = _serviceProvider.GetService<CloudSyncService>();

                if (cloudService == null)
                {
                    // إنشاء خدمة Google Drive إذا لم تكن موجودة
                    cloudService = new GoogleDriveService();
                }

                if (syncService == null)
                {
                    // محاولة الحصول على خدمة المزامنة من DI أو تجاهلها إذا لم تكن متوفرة
                    _toastService.ShowWarning("تحذير", "خدمة المزامنة التلقائية غير متوفرة. ستعمل الواجهة بدون مزامنة تلقائية.");
                }

                // تهيئة عنصر التحكم
                CloudStorageControlInstance.InitializeServices(
                    cloudService, 
                    syncService, 
                    _toastService
                );

                _toastService.ShowInfo("تم التحميل", "تم تحميل صفحة التخزين السحابي بنجاح");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في التهيئة", 
                    $"فشل في تهيئة صفحة التخزين السحابي: {ex.Message}");
            }
        }

        /// <summary>
        /// تنظيف الموارد عند إغلاق الصفحة
        /// </summary>
        public void Cleanup()
        {
            try
            {
                CloudStorageControlInstance?.Dispose();
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"خطأ في تنظيف الموارد: {ex.Message}");
            }
        }
    }
}
