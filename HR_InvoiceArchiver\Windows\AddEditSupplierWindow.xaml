<Window x:Class="HR_InvoiceArchiver.Windows.AddEditSupplierWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إضافة/تعديل مورد"
        Height="700" Width="600"
        WindowStartupLocation="CenterOwner"
        Background="Transparent"
        FontFamily="{DynamicResource MaterialDesignFont}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize"
        MinHeight="650" MinWidth="550"
        WindowStyle="None"
        AllowsTransparency="True">

    <Window.Resources>
        <!-- Modern Gradient Brushes -->
        <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#10B981" Offset="0"/>
            <GradientStop Color="#059669" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#40000000" BlurRadius="20" ShadowDepth="8" Direction="270"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Enhanced TextBox Style -->
        <Style x:Key="EnhancedTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="Padding" Value="16,16,16,8"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="#667eea"/>
            <Setter Property="materialDesign:TextFieldAssist.RippleOnFocusEnabled" Value="True"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.75"/>
            <Style.Triggers>
                <Trigger Property="IsKeyboardFocused" Value="True">
                    <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="#667eea"/>
                    <Setter Property="BorderBrush" Value="#667eea"/>
                </Trigger>
                <Trigger Property="Validation.HasError" Value="True">
                    <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="#DC2626"/>
                    <Setter Property="BorderBrush" Value="#DC2626"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Enhanced ScrollViewer Style -->
        <Style x:Key="SmoothScrollViewerStyle" TargetType="ScrollViewer">
            <Setter Property="CanContentScroll" Value="False"/>
            <Setter Property="PanningMode" Value="VerticalOnly"/>
            <Setter Property="IsDeferredScrollingEnabled" Value="False"/>
            <Setter Property="VirtualizingPanel.IsVirtualizing" Value="False"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ScrollViewer">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <ScrollContentPresenter Grid.Column="0" Grid.Row="0"
                                                  Content="{TemplateBinding Content}"
                                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                                  CanContentScroll="{TemplateBinding CanContentScroll}"/>

                            <ScrollBar Grid.Column="1" Grid.Row="0"
                                     Name="PART_VerticalScrollBar"
                                     Orientation="Vertical"
                                     Value="{TemplateBinding VerticalOffset}"
                                     Maximum="{TemplateBinding ScrollableHeight}"
                                     ViewportSize="{TemplateBinding ViewportHeight}"
                                     Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                     Width="12"
                                     Margin="2,0,0,0"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <!-- Main Container with Shadow and Rounded Corners -->
    <Border Background="White"
            CornerRadius="15"
            Margin="10">
        <Border.Effect>
            <DropShadowEffect Color="#40000000"
                            BlurRadius="30"
                            ShadowDepth="15"
                            Direction="270"
                            Opacity="0.3"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Custom Title Bar -->
            <Border Grid.Row="0"
                    Background="#F8F9FF"
                    CornerRadius="15,15,0,0"
                    Height="45"
                    MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Window Title -->
                    <StackPanel Grid.Column="0"
                              Orientation="Horizontal"
                              VerticalAlignment="Center"
                              Margin="20,0,0,0">
                        <materialDesign:PackIcon Kind="AccountGroup"
                                               Width="20" Height="20"
                                               Foreground="#667eea"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>
                        <TextBlock x:Name="WindowTitleTextBlock"
                                 Text="إضافة مورد جديد"
                                 FontSize="14"
                                 FontWeight="Medium"
                                 Foreground="#2D3748"
                                 VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- Window Controls -->
                    <StackPanel Grid.Column="2"
                              Orientation="Horizontal"
                              VerticalAlignment="Center"
                              Margin="0,0,10,0">
                        <!-- Minimize Button -->
                        <Button x:Name="MinimizeButton"
                              Width="30" Height="30"
                              Background="Transparent"
                              BorderThickness="0"
                              Click="MinimizeButton_Click"
                              ToolTip="تصغير">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                      CornerRadius="5">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                    VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#E0E0E0"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                            <materialDesign:PackIcon Kind="WindowMinimize"
                                                   Width="16" Height="16"
                                                   Foreground="#6B7280"/>
                        </Button>

                        <!-- Close Button -->
                        <Button x:Name="CloseButton"
                              Width="30" Height="30"
                              Background="Transparent"
                              BorderThickness="0"
                              Margin="5,0,0,0"
                              Click="CloseButton_Click"
                              ToolTip="إغلاق">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                      CornerRadius="5">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                    VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#FFE5E5"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                            <materialDesign:PackIcon Kind="Close"
                                                   Width="16" Height="16"
                                                   Foreground="#DC2626"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Enhanced Header Section -->
            <Border Grid.Row="1"
                    Background="{StaticResource PrimaryGradientBrush}"
                    Height="120">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Header Content -->
                    <StackPanel Grid.Column="0"
                              VerticalAlignment="Center"
                              Margin="30,0,0,0">
                        <TextBlock x:Name="HeaderTitleTextBlock"
                                 Text="إضافة مورد جديد"
                                 FontSize="28"
                                 FontWeight="Bold"
                                 Foreground="White"
                                 Margin="0,0,0,8"/>
                        <TextBlock x:Name="HeaderDescriptionTextBlock"
                                 Text="قم بإدخال بيانات المورد الجديد"
                                 FontSize="16"
                                 Foreground="#E0E7FF"
                                 Opacity="0.9"/>
                    </StackPanel>

                    <!-- Header Icon -->
                    <Border Grid.Column="1"
                          Width="80" Height="80"
                          Background="rgba(255,255,255,0.15)"
                          CornerRadius="40"
                          Margin="0,0,30,0"
                          VerticalAlignment="Center">
                        <materialDesign:PackIcon Kind="AccountGroup"
                                               Width="40" Height="40"
                                               Foreground="White"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                    </Border>
                </Grid>
            </Border>

            <Grid Grid.Row="2" Margin="20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Form Section -->
                <ScrollViewer Grid.Row="0"
                              Style="{StaticResource SmoothScrollViewerStyle}"
                              VerticalScrollBarVisibility="Auto"
                              HorizontalScrollBarVisibility="Disabled"
                              Padding="0,10,0,10"
                              PreviewMouseWheel="ScrollViewer_PreviewMouseWheel">
                    <StackPanel>
                        <!-- Section 1: المعلومات الأساسية -->
                        <Border Background="#F7FAFC" CornerRadius="8" Padding="20" Margin="0,0,0,20">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                    <materialDesign:PackIcon Kind="AccountGroup" Width="24" Height="24"
                                                           Foreground="#667eea"
                                                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Text="المعلومات الأساسية"
                                             FontSize="18" FontWeight="Bold"
                                             Foreground="#2D3748" VerticalAlignment="Center"/>
                                </StackPanel>

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="20"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- اسم المورد -->
                                    <TextBox x:Name="NameTextBox"
                                             Grid.Row="0" Grid.Column="0"
                                             materialDesign:HintAssist.Hint="اسم المورد *"
                                             materialDesign:HintAssist.HelperText="أدخل اسم المورد أو الشركة"
                                             Style="{StaticResource EnhancedTextBoxStyle}"
                                             Margin="0,0,0,20"/>

                                    <!-- الشخص المسؤول -->
                                    <TextBox x:Name="ContactPersonTextBox"
                                             Grid.Row="0" Grid.Column="2"
                                             materialDesign:HintAssist.Hint="الشخص المسؤول"
                                             materialDesign:HintAssist.HelperText="اسم الشخص المسؤول للتواصل"
                                             Style="{StaticResource EnhancedTextBoxStyle}"
                                             Margin="0,0,0,20"/>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- Section 2: معلومات الاتصال -->
                        <Border Background="#F7FAFC" CornerRadius="8" Padding="20" Margin="0,0,0,20">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                    <materialDesign:PackIcon Kind="Phone" Width="24" Height="24"
                                                           Foreground="#10B981"
                                                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Text="معلومات الاتصال"
                                             FontSize="18" FontWeight="Bold"
                                             Foreground="#2D3748" VerticalAlignment="Center"/>
                                </StackPanel>

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="20"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- رقم الهاتف -->
                                    <TextBox x:Name="PhoneTextBox"
                                             Grid.Row="0" Grid.Column="0"
                                             materialDesign:HintAssist.Hint="رقم الهاتف"
                                             materialDesign:HintAssist.HelperText="رقم الهاتف أو الجوال"
                                             Style="{StaticResource EnhancedTextBoxStyle}"
                                             Margin="0,0,0,20"/>

                                    <!-- البريد الإلكتروني -->
                                    <TextBox x:Name="EmailTextBox"
                                             Grid.Row="0" Grid.Column="2"
                                             materialDesign:HintAssist.Hint="البريد الإلكتروني"
                                             materialDesign:HintAssist.HelperText="عنوان البريد الإلكتروني"
                                             Style="{StaticResource EnhancedTextBoxStyle}"
                                             Margin="0,0,0,20"/>

                                    <!-- الرقم الضريبي -->
                                    <TextBox x:Name="TaxNumberTextBox"
                                             Grid.Row="1" Grid.Column="0"
                                             materialDesign:HintAssist.Hint="الرقم الضريبي"
                                             materialDesign:HintAssist.HelperText="رقم التسجيل الضريبي (اختياري)"
                                             Style="{StaticResource EnhancedTextBoxStyle}"
                                             Margin="0,0,0,20"/>

                                    <!-- الوصف -->
                                    <TextBox x:Name="DescriptionTextBox"
                                             Grid.Row="1" Grid.Column="2"
                                             materialDesign:HintAssist.Hint="وصف المورد"
                                             materialDesign:HintAssist.HelperText="وصف مختصر لنشاط المورد"
                                             Style="{StaticResource EnhancedTextBoxStyle}"
                                             Margin="0,0,0,20"/>

                                    <!-- العنوان -->
                                    <TextBox x:Name="AddressTextBox"
                                             Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="3"
                                             materialDesign:HintAssist.Hint="العنوان الكامل"
                                             materialDesign:HintAssist.HelperText="العنوان التفصيلي للمورد"
                                             Style="{StaticResource EnhancedTextBoxStyle}"
                                             Height="95"
                                             TextWrapping="Wrap"
                                             AcceptsReturn="True"
                                             VerticalContentAlignment="Top"
                                             ScrollViewer.VerticalScrollBarVisibility="Auto"
                                             ScrollViewer.CanContentScroll="False"
                                             Margin="0,0,0,0"/>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- Section 3: الملاحظات -->
                        <Border Background="#F7FAFC" CornerRadius="8" Padding="20">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                    <materialDesign:PackIcon Kind="NoteText" Width="24" Height="24"
                                                           Foreground="#0EA5E9"
                                                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Text="ملاحظات إضافية"
                                             FontSize="18" FontWeight="Bold"
                                             Foreground="#2D3748" VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- الملاحظات -->
                                <TextBox x:Name="NotesTextBox"
                                         materialDesign:HintAssist.Hint="ملاحظات إضافية"
                                         materialDesign:HintAssist.HelperText="أي ملاحظات أو تعليقات إضافية"
                                         Style="{StaticResource EnhancedTextBoxStyle}"
                                         Height="95"
                                         TextWrapping="Wrap"
                                         AcceptsReturn="True"
                                         VerticalContentAlignment="Top"
                                         ScrollViewer.VerticalScrollBarVisibility="Auto"
                                         ScrollViewer.CanContentScroll="False"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>

                <!-- Action Buttons Section -->
                <Border Grid.Row="1"
                        Background="#F7FAFC"
                        CornerRadius="0,0,8,8"
                        Padding="20,15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- رسالة التحقق -->
                        <Border Grid.Column="0"
                                Background="#FEF2F2"
                                CornerRadius="8"
                                Padding="15,10"
                                Visibility="Collapsed"
                                x:Name="ValidationMessageBorder"
                                BorderBrush="#FECACA"
                                BorderThickness="1">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AlertCircle"
                                                       Width="20" Height="20"
                                                       Foreground="#DC2626"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,10,0"/>
                                <TextBlock x:Name="ValidationMessageTextBlock"
                                           Foreground="#DC2626"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           VerticalAlignment="Center"
                                           TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>

                        <!-- زر الإلغاء -->
                        <Button x:Name="CancelButton"
                                Grid.Column="1"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                materialDesign:ButtonAssist.CornerRadius="12"
                                Height="48"
                                Width="130"
                                FontSize="14"
                                FontWeight="Medium"
                                Margin="15,0,0,0"
                                BorderBrush="#6B7280"
                                Foreground="#6B7280"
                                Click="CancelButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Close"
                                                       Width="18" Height="18"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="إلغاء" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- زر الحفظ -->
                        <Button x:Name="SaveButton"
                                Grid.Column="2"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                materialDesign:ButtonAssist.CornerRadius="12"
                                Background="#667eea"
                                Height="48"
                                Width="130"
                                FontSize="14"
                                FontWeight="Bold"
                                Margin="15,0,0,0"
                                Click="SaveButton_Click">
                            <Button.Effect>
                                <DropShadowEffect Color="#667eea" Opacity="0.3" BlurRadius="15" ShadowDepth="4"/>
                            </Button.Effect>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon x:Name="SaveButtonIcon"
                                                       Kind="ContentSave"
                                                       Width="18" Height="18"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                <TextBlock x:Name="SaveButtonText" Text="حفظ" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </Border>
            </Grid>

            <!-- Enhanced Loading Overlay -->
            <Grid x:Name="LoadingGrid"
                  Grid.RowSpan="4"
                  Background="#CC000000"
                  Visibility="Collapsed">
                <Border Background="White"
                        CornerRadius="15"
                        Padding="40,30"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center">
                    <Border.Effect>
                        <DropShadowEffect Color="#40000000" BlurRadius="25" ShadowDepth="10" Direction="270"/>
                    </Border.Effect>
                    <StackPanel HorizontalAlignment="Center">
                        <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                     Width="60"
                                     Height="60"
                                     IsIndeterminate="True"
                                     Foreground="#667eea"/>
                        <TextBlock Text="جاري الحفظ..."
                                   FontSize="16"
                                   FontWeight="Medium"
                                   Foreground="#2D3748"
                                   HorizontalAlignment="Center"
                                   Margin="0,15,0,0"/>
                        <TextBlock Text="يرجى الانتظار..."
                                   FontSize="12"
                                   Foreground="#6B7280"
                                   HorizontalAlignment="Center"
                                   Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Grid>
    </Border>
</Window>
