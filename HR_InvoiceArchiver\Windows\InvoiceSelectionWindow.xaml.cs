using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Windows
{
    public partial class InvoiceSelectionWindow : Window
    {
        private readonly ObservableCollection<SelectableInvoice> _invoices = new();
        private readonly ObservableCollection<SelectableInvoice> _filteredInvoices = new();
        
        public List<Invoice>? SelectedInvoices { get; private set; }

        public InvoiceSelectionWindow(List<Invoice> availableInvoices, List<Invoice> alreadySelectedInvoices)
        {
            InitializeComponent();
            
            // Convert to selectable invoices
            foreach (var invoice in availableInvoices)
            {
                if (!alreadySelectedInvoices.Any(s => s.Id == invoice.Id))
                {
                    var selectableInvoice = new SelectableInvoice(invoice);
                    selectableInvoice.PropertyChanged += SelectableInvoice_PropertyChanged;
                    _invoices.Add(selectableInvoice);
                    _filteredInvoices.Add(selectableInvoice);
                }
            }
            
            InvoicesDataGrid.ItemsSource = _filteredInvoices;
            UpdateSelectedCount();
        }

        private void SelectableInvoice_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(SelectableInvoice.IsSelected))
            {
                UpdateSelectedCount();
            }
        }

        private void UpdateSelectedCount()
        {
            var selectedCount = _invoices.Count(i => i.IsSelected);
            SelectedCountTextBlock.Text = selectedCount.ToString();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var searchText = SearchTextBox.Text?.ToLower() ?? string.Empty;
            
            _filteredInvoices.Clear();
            
            var filtered = string.IsNullOrWhiteSpace(searchText) 
                ? _invoices 
                : _invoices.Where(i => 
                    i.InvoiceNumber.ToLower().Contains(searchText) ||
                    (i.Supplier?.Name?.ToLower().Contains(searchText) ?? false));
            
            foreach (var invoice in filtered)
            {
                _filteredInvoices.Add(invoice);
            }
        }

        private void SelectAllButton_Click(object sender, RoutedEventArgs e)
        {
            foreach (var invoice in _filteredInvoices)
            {
                invoice.IsSelected = true;
            }
        }

        private void ClearSelectionButton_Click(object sender, RoutedEventArgs e)
        {
            foreach (var invoice in _invoices)
            {
                invoice.IsSelected = false;
            }
        }

        private void ConfirmButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedInvoices = _invoices.Where(i => i.IsSelected).Select(i => i.Invoice).ToList();
            
            if (!selectedInvoices.Any())
            {
                MessageBox.Show("يرجى اختيار فاتورة واحدة على الأقل", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            SelectedInvoices = selectedInvoices;
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    public class SelectableInvoice : INotifyPropertyChanged
    {
        private bool _isSelected;
        
        public Invoice Invoice { get; }
        
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged();
            }
        }

        // Proxy properties for DataGrid binding
        public string InvoiceNumber => Invoice.InvoiceNumber;
        public Supplier? Supplier => Invoice.Supplier;
        public DateTime InvoiceDate => Invoice.InvoiceDate;
        public DateTime? DueDate => Invoice.DueDate;
        public decimal Amount => Invoice.Amount;
        public decimal PaidAmount => Invoice.PaidAmount;
        public decimal RemainingAmount => Invoice.RemainingAmount;

        public SelectableInvoice(Invoice invoice)
        {
            Invoice = invoice;
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
