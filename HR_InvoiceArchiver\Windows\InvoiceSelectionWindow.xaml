<Window x:Class="HR_InvoiceArchiver.Windows.InvoiceSelectionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="اختيار الفواتير"
        Height="600" Width="900"
        WindowStartupLocation="CenterOwner"
        Background="Transparent"
        FontFamily="{DynamicResource MaterialDesignFont}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize"
        MinHeight="550" MinWidth="850"
        WindowStyle="None"
        AllowsTransparency="True">

    <Window.Resources>
        <!-- Modern Gradient Brushes -->
        <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SuccessGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#4CAF50" Offset="0"/>
            <GradientStop Color="#8BC34A" Offset="1"/>
        </LinearGradientBrush>

        <!-- Enhanced Button Style -->
        <Style x:Key="EnhancedButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
        </Style>
    </Window.Resources>

    <!-- Main Container with Rounded Border -->
    <Border Background="White" CornerRadius="16" Margin="10">
        <Border.Effect>
            <DropShadowEffect Color="#40000000" BlurRadius="25" ShadowDepth="12" Direction="270"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0" Background="{StaticResource PrimaryGradientBrush}" CornerRadius="16,16,0,0">
                <Grid Margin="25,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="اختيار الفواتير" 
                                 FontSize="20" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="اختر الفواتير التي تريد دفعها في وصل واحد" 
                                 FontSize="13" Foreground="White" Opacity="0.9" Margin="0,4,0,0"/>
                    </StackPanel>

                    <Button Grid.Column="1" x:Name="CloseButton" 
                          Style="{StaticResource MaterialDesignIconButton}"
                          Width="35" Height="35" 
                          Foreground="White"
                          Click="CloseButton_Click">
                        <materialDesign:PackIcon Kind="Close" Width="20" Height="20"/>
                    </Button>
                </Grid>
            </Border>

            <!-- Search and Filter -->
            <Border Grid.Row="1" Background="#F8F9FA" Padding="25,15">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox x:Name="SearchTextBox" 
                           Grid.Column="0"
                           materialDesign:HintAssist.Hint="البحث في الفواتير..."
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           FontSize="14"
                           TextChanged="SearchTextBox_TextChanged"/>

                    <Button Grid.Column="2" x:Name="SelectAllButton" 
                          Content="تحديد الكل" 
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          BorderBrush="{StaticResource PrimaryGradientBrush}"
                          Foreground="{StaticResource PrimaryGradientBrush}"
                          Click="SelectAllButton_Click"
                          MinWidth="100"/>

                    <Button Grid.Column="4" x:Name="ClearSelectionButton" 
                          Content="إلغاء التحديد" 
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          BorderBrush="Gray"
                          Foreground="Gray"
                          Click="ClearSelectionButton_Click"
                          MinWidth="100"/>
                </Grid>
            </Border>

            <!-- Invoices DataGrid -->
            <DataGrid Grid.Row="2" x:Name="InvoicesDataGrid" 
                    AutoGenerateColumns="False"
                    CanUserAddRows="False"
                    CanUserDeleteRows="False"
                    CanUserReorderColumns="False"
                    CanUserResizeRows="False"
                    HeadersVisibility="Column"
                    GridLinesVisibility="None"
                    SelectionMode="Extended"
                    Background="Transparent"
                    BorderThickness="0"
                    FontSize="13"
                    RowHeight="45"
                    Margin="25,10,25,20">
                
                <DataGrid.Columns>
                    <DataGridCheckBoxColumn Header="تحديد" Binding="{Binding IsSelected}" Width="60"/>
                    <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                    <DataGridTextColumn Header="المورد" Binding="{Binding Supplier.Name}" Width="180"/>
                    <DataGridTextColumn Header="تاريخ الفاتورة" Binding="{Binding InvoiceDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                    <DataGridTextColumn Header="تاريخ الاستحقاق" Binding="{Binding DueDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                    <DataGridTextColumn Header="المبلغ الإجمالي" Binding="{Binding Amount, StringFormat=N2}" Width="120"/>
                    <DataGridTextColumn Header="المبلغ المدفوع" Binding="{Binding PaidAmount, StringFormat=N2}" Width="120"/>
                    <DataGridTextColumn Header="المبلغ المتبقي" Binding="{Binding RemainingAmount, StringFormat=N2}" Width="120"/>
                </DataGrid.Columns>

                <DataGrid.RowStyle>
                    <Style TargetType="DataGridRow">
                        <Setter Property="Background" Value="Transparent"/>
                        <Setter Property="BorderThickness" Value="0"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F0F8FF"/>
                            </Trigger>
                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                <Setter Property="Background" Value="#E3F2FD"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>
            </DataGrid>

            <!-- Footer -->
            <Border Grid.Row="3" Background="#F8F9FA" CornerRadius="0,0,16,16" Padding="25,15">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="المحدد: " FontSize="14" Foreground="Gray"/>
                        <TextBlock x:Name="SelectedCountTextBlock" Text="0" FontSize="14" FontWeight="Bold" 
                                 Foreground="{StaticResource PrimaryGradientBrush}"/>
                        <TextBlock Text=" فاتورة" FontSize="14" Foreground="Gray"/>
                    </StackPanel>

                    <Button Grid.Column="1" x:Name="ConfirmButton"
                          Style="{StaticResource EnhancedButtonStyle}"
                          Background="{StaticResource SuccessGradient}"
                          BorderBrush="{StaticResource SuccessGradient}"
                          Foreground="White"
                          Click="ConfirmButton_Click"
                          MinWidth="120">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Check" Width="16" Height="16" Margin="0,0,6,0"/>
                            <TextBlock Text="تأكيد الاختيار"/>
                        </StackPanel>
                    </Button>

                    <Button Grid.Column="2" x:Name="CancelButton" 
                          Content="إلغاء" 
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          BorderBrush="Gray"
                          Foreground="Gray"
                          Click="CancelButton_Click"
                          MinWidth="80"
                          Margin="8,8,0,8"/>
                </Grid>
            </Border>
        </Grid>
    </Border>
</Window>
