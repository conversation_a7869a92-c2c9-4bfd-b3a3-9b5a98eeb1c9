using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Windows;
using HR_InvoiceArchiver.Controls;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Pages
{
    public partial class SuppliersPage : UserControl, INavigationAware
    {
        private readonly ISupplierService _supplierService;
        private readonly IInvoiceService _invoiceService;
        private readonly IToastService _toastService;
        private readonly INavigationService _navigationService;

        private ObservableCollection<Supplier> _allSuppliers = new();
        private ObservableCollection<Supplier> _filteredSuppliers = new();
        private string _searchText = string.Empty;

        public SuppliersPage(
            ISupplierService supplierService,
            IInvoiceService invoiceService,
            IToastService toastService,
            INavigationService navigationService)
        {
            InitializeComponent();
            _supplierService = supplierService;
            _invoiceService = invoiceService;
            _toastService = toastService;
            _navigationService = navigationService;

            // Set up DataGrid binding
            SuppliersDataGrid.ItemsSource = _filteredSuppliers;

            Loaded += SuppliersPage_Loaded;
        }

        private async void SuppliersPage_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadSupplierStatisticsAsync();
            await LoadSuppliersAsync();
        }

        private async Task LoadSuppliersAsync()
        {
            try
            {
                ShowLoading(true);

                var suppliers = await _supplierService.GetAllSuppliersAsync();

                // Calculate invoice counts and totals for each supplier
                foreach (var supplier in suppliers)
                {
                    var invoices = await _invoiceService.GetInvoicesBySupplierAsync(supplier.Id);
                    supplier.InvoiceCount = invoices.Count();
                    supplier.TotalAmount = invoices.Sum(i => i.Amount);
                }

                Dispatcher.Invoke(() =>
                {
                    _allSuppliers.Clear();
                    foreach (var supplier in suppliers)
                    {
                        _allSuppliers.Add(supplier);
                    }

                    ApplyFilter();
                    UpdateFooterStatistics();
                });
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحميل الموردين", ex.Message);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private void ApplyFilter()
        {
            _filteredSuppliers.Clear();

            var filtered = _allSuppliers.AsEnumerable();

            if (!string.IsNullOrWhiteSpace(_searchText))
            {
                filtered = filtered.Where(s =>
                    s.Name.Contains(_searchText, StringComparison.OrdinalIgnoreCase) ||
                    (s.ContactPerson?.Contains(_searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                    (s.Phone?.Contains(_searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                    (s.Email?.Contains(_searchText, StringComparison.OrdinalIgnoreCase) ?? false));
            }

            foreach (var supplier in filtered.OrderBy(s => s.Name))
            {
                _filteredSuppliers.Add(supplier);
            }
        }

        private void UpdateFooterStatistics()
        {
            // Footer statistics removed as they're now integrated in the header cards
        }

        // Event Handlers
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                _searchText = textBox.Text;
                ApplyFilter();
                UpdateFooterStatistics();
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ShowLoading(true);

                // Refresh both statistics and suppliers data
                await LoadSupplierStatisticsAsync();
                await LoadSuppliersAsync();

                // Show success message
                _toastService.ShowSuccess("تم تحديث البيانات", "تم تحديث بيانات الموردين بنجاح");

                // Add delay for better UX
                await Task.Delay(1000);
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحديث البيانات", ex.Message);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        // Interactive Statistics Card Events
        private void StatCard_MouseEnter(object sender, MouseEventArgs e)
        {
            if (sender is MaterialDesignThemes.Wpf.Card card)
            {
                try
                {
                    // Create smooth scale animation
                    var scaleTransform = new ScaleTransform(1.0, 1.0);
                    card.RenderTransform = scaleTransform;
                    card.RenderTransformOrigin = new Point(0.5, 0.5);

                    var scaleXAnimation = new DoubleAnimation
                    {
                        From = 1.0,
                        To = 1.05,
                        Duration = TimeSpan.FromMilliseconds(300),
                        EasingFunction = new BackEase { EasingMode = EasingMode.EaseOut, Amplitude = 0.3 }
                    };

                    var scaleYAnimation = new DoubleAnimation
                    {
                        From = 1.0,
                        To = 1.05,
                        Duration = TimeSpan.FromMilliseconds(300),
                        EasingFunction = new BackEase { EasingMode = EasingMode.EaseOut, Amplitude = 0.3 }
                    };

                    scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleXAnimation);
                    scaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleYAnimation);
                }
                catch
                {
                    // Fallback - just change elevation
                    MaterialDesignThemes.Wpf.ElevationAssist.SetElevation(card, MaterialDesignThemes.Wpf.Elevation.Dp8);
                }
            }
        }

        private void StatCard_MouseLeave(object sender, MouseEventArgs e)
        {
            if (sender is MaterialDesignThemes.Wpf.Card card)
            {
                try
                {
                    var scaleTransform = card.RenderTransform as ScaleTransform;
                    if (scaleTransform != null)
                    {
                        var scaleXAnimation = new DoubleAnimation
                        {
                            From = 1.05,
                            To = 1.0,
                            Duration = TimeSpan.FromMilliseconds(250),
                            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
                        };

                        var scaleYAnimation = new DoubleAnimation
                        {
                            From = 1.05,
                            To = 1.0,
                            Duration = TimeSpan.FromMilliseconds(250),
                            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
                        };

                        scaleTransform.BeginAnimation(ScaleTransform.ScaleXProperty, scaleXAnimation);
                        scaleTransform.BeginAnimation(ScaleTransform.ScaleYProperty, scaleYAnimation);
                    }
                }
                catch
                {
                    // Fallback - reset elevation
                    MaterialDesignThemes.Wpf.ElevationAssist.SetElevation(card, MaterialDesignThemes.Wpf.Elevation.Dp3);
                }
            }
        }

        // Enhanced Action Button Events
        private void EditSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Supplier supplier)
            {
                EditButton_Click(sender, e);
            }
        }

        private async void DeleteSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Supplier supplier)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المورد '{supplier.Name}'؟\nسيتم حذف جميع البيانات المرتبطة به.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        ShowLoading(true);
                        await _supplierService.DeleteSupplierAsync(supplier.Id);

                        // Refresh data
                        await LoadSupplierStatisticsAsync();
                        await LoadSuppliersAsync();

                        // Show enhanced success message
                        _toastService.ShowSuccess("تم حذف المورد بنجاح", $"تم حذف المورد '{supplier.Name}' وجميع البيانات المرتبطة به");

                        // Add delay for better UX
                        await Task.Delay(1500);
                    }
                    catch (Exception ex)
                    {
                        _toastService.ShowError("خطأ في حذف المورد", ex.Message);
                    }
                    finally
                    {
                        ShowLoading(false);
                    }
                }
            }
        }



        private void SuppliersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Handle selection change if needed
        }

        private async void EditButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Supplier supplier)
            {
                try
                {
                    var supplierWindow = new Windows.AddEditSupplierWindow(supplier);

                    if (supplierWindow.ShowDialog() == true && supplierWindow.IsSaved)
                    {
                        // Show loading while refreshing
                        ShowLoading(true);

                        // Refresh statistics and suppliers list
                        await LoadSupplierStatisticsAsync();
                        await LoadSuppliersAsync();

                        // Show success message
                        if (supplierWindow.SavedSupplier != null)
                        {
                            _toastService.ShowSuccess("تم تحديث المورد بنجاح",
                                $"تم تحديث بيانات المورد '{supplierWindow.SavedSupplier.Name}' بنجاح");

                            // Scroll to the updated supplier
                            await ScrollToSupplier(supplierWindow.SavedSupplier.Name);
                        }

                        // Add delay for better UX
                        await Task.Delay(1000);
                        ShowLoading(false);
                    }
                }
                catch (Exception ex)
                {
                    _toastService.ShowError("خطأ في فتح نموذج تعديل المورد", ex.Message);
                    ShowLoading(false);
                }
            }
        }

        private void StatementButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Supplier supplier)
            {
                try
                {
                    var statementWindow = new Windows.SupplierStatementWindow(supplier)
                    {
                        Owner = Window.GetWindow(this)
                    };
                    statementWindow.ShowDialog();
                }
                catch (Exception ex)
                {
                    _toastService.ShowError("خطأ في فتح كشف حساب المورد", ex.Message);
                }
            }
        }

        public void OnNavigatedTo(object parameter)
        {
            // Refresh data when navigating to this page - use Dispatcher to avoid threading issues
            Dispatcher.BeginInvoke(new Action(async () =>
            {
                await LoadSupplierStatisticsAsync();
                await LoadSuppliersAsync();
            }));
        }

        public void OnNavigatedFrom()
        {
            // Cleanup when leaving this page
        }

        private async Task LoadSupplierStatisticsAsync()
        {
            try
            {
                ShowLoading(true);
                
                var suppliers = await _supplierService.GetAllSuppliersAsync();
                var invoices = await _invoiceService.GetAllInvoicesAsync();
                
                Dispatcher.Invoke(() =>
                {
                    try
                    {
                        // Store suppliers for later use
                        _allSuppliers = new ObservableCollection<Supplier>(suppliers);

                        // Update bottom statistics cards
                        UpdateBottomStatistics();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error updating statistics: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() =>
                {
                    _toastService.ShowError("خطأ في تحميل إحصائيات الموردين", ex.Message);
                });
            }
            finally
            {
                Dispatcher.Invoke(() => ShowLoading(false));
            }
        }

        private void ShowLoading(bool isLoading)
        {
            try
            {
                // Use modern loading panel instead of simple progress bar
                if (LoadingPanel != null)
                {
                    LoadingPanel.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
                }

                // Hide/show main content
                if (SuppliersDataGrid != null)
                {
                    SuppliersDataGrid.Visibility = isLoading ? Visibility.Collapsed : Visibility.Visible;
                }

                // Handle empty state
                if (!isLoading && _filteredSuppliers.Count == 0)
                {
                    if (EmptyStatePanel != null)
                    {
                        EmptyStatePanel.Visibility = Visibility.Visible;
                    }
                    if (SuppliersDataGrid != null)
                    {
                        SuppliersDataGrid.Visibility = Visibility.Collapsed;
                    }
                }
                else if (!isLoading)
                {
                    if (EmptyStatePanel != null)
                    {
                        EmptyStatePanel.Visibility = Visibility.Collapsed;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ShowLoading: {ex.Message}");
                // Fallback - just hide/show main content
                if (SuppliersDataGrid != null)
                {
                    SuppliersDataGrid.Visibility = isLoading ? Visibility.Collapsed : Visibility.Visible;
                }
            }
        }

        private async void AddNewSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var supplierWindow = new Windows.AddEditSupplierWindow();

                if (supplierWindow.ShowDialog() == true && supplierWindow.IsSaved)
                {
                    // Show loading while refreshing
                    ShowLoading(true);

                    // Refresh statistics and suppliers list
                    await LoadSupplierStatisticsAsync();
                    await LoadSuppliersAsync();

                    // Show success message
                    if (supplierWindow.SavedSupplier != null)
                    {
                        _toastService.ShowSuccess("تم إضافة المورد بنجاح",
                            $"تم إضافة المورد '{supplierWindow.SavedSupplier.Name}' بنجاح إلى النظام");

                        // Scroll to the newly added supplier
                        await ScrollToSupplier(supplierWindow.SavedSupplier.Name);
                    }

                    // Add delay for better UX
                    await Task.Delay(1000);
                    ShowLoading(false);
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في فتح نموذج إضافة المورد", ex.Message);
                ShowLoading(false);
            }
        }

        private async Task ScrollToSupplier(string supplierName)
        {
            try
            {
                await Task.Delay(500); // Wait for data to load

                var supplier = _filteredSuppliers.FirstOrDefault(s =>
                    s.Name.Equals(supplierName, StringComparison.OrdinalIgnoreCase));

                if (supplier != null && SuppliersDataGrid != null)
                {
                    SuppliersDataGrid.SelectedItem = supplier;
                    SuppliersDataGrid.ScrollIntoView(supplier);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error scrolling to supplier: {ex.Message}");
            }
        }



        private void SupplierStatementButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Check if there are suppliers
                if (_filteredSuppliers.Count == 0)
                {
                    _toastService.ShowWarning("لا توجد موردين", "يجب إضافة موردين أولاً لعرض كشف الحساب");
                    return;
                }

                // Get selected supplier from DataGrid or use first supplier
                var selectedSupplier = SuppliersDataGrid.SelectedItem as Supplier ?? _filteredSuppliers.FirstOrDefault();

                if (selectedSupplier != null)
                {
                    var statementWindow = new Windows.SupplierStatementWindow(selectedSupplier)
                    {
                        Owner = Window.GetWindow(this)
                    };
                    statementWindow.ShowDialog();
                }
                else
                {
                    _toastService.ShowWarning("لم يتم تحديد مورد", "يرجى تحديد مورد من القائمة أولاً");
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في فتح كشف حساب المورد", ex.Message);
            }
        }





        private static T? FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;

                var childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }
            return null;
        }

        private Grid? FindParentGrid(DependencyObject child)
        {
            var parent = VisualTreeHelper.GetParent(child);
            while (parent != null)
            {
                if (parent is Grid grid)
                    return grid;
                parent = VisualTreeHelper.GetParent(parent);
            }
            return null;
        }

        // New event handlers for enhanced UI

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService?.ShowInfo("تصدير", "سيتم إضافة ميزة التصدير قريباً");
        }

        private void ClearSearchButton_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = "";
            ApplyFilter();
            _toastService?.ShowInfo("تم المسح", "تم مسح البحث");
        }

        private void QuickFilterActive_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SearchTextBox.Text = "";
                _filteredSuppliers.Clear();

                var activeSuppliers = _allSuppliers.Where(s => s.IsActive).ToList();
                foreach (var supplier in activeSuppliers)
                {
                    _filteredSuppliers.Add(supplier);
                }

                _toastService?.ShowInfo("تم التطبيق", "تم عرض الموردين النشطين فقط");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تطبيق الفلتر: {ex.Message}");
            }
        }

        private void QuickFilterInactive_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SearchTextBox.Text = "";
                _filteredSuppliers.Clear();

                var inactiveSuppliers = _allSuppliers.Where(s => !s.IsActive).ToList();
                foreach (var supplier in inactiveSuppliers)
                {
                    _filteredSuppliers.Add(supplier);
                }

                _toastService?.ShowInfo("تم التطبيق", "تم عرض الموردين غير النشطين فقط");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تطبيق الفلتر: {ex.Message}");
            }
        }

        // New filter methods for advanced filtering
        private void StatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyAdvancedFilters();
        }

        private void AmountFilter_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyAdvancedFilters();
        }

        private void InvoiceCountFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyAdvancedFilters();
        }

        private void ApplyAdvancedFilters()
        {
            try
            {
                if (_allSuppliers == null) return;

                var filteredSuppliers = _allSuppliers.AsEnumerable();

                // Apply search filter
                if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
                {
                    var searchTerm = SearchTextBox.Text.ToLower();
                    filteredSuppliers = filteredSuppliers.Where(s =>
                        s.Name.ToLower().Contains(searchTerm) ||
                        (s.ContactPerson?.ToLower().Contains(searchTerm) ?? false) ||
                        (s.Phone?.ToLower().Contains(searchTerm) ?? false) ||
                        (s.Email?.ToLower().Contains(searchTerm) ?? false) ||
                        (s.Address?.ToLower().Contains(searchTerm) ?? false));
                }

                // Apply status filter
                if (StatusFilterComboBox?.SelectedItem is ComboBoxItem statusItem && statusItem.Content.ToString() != "الكل")
                {
                    bool isActive = statusItem.Content.ToString() == "نشط";
                    filteredSuppliers = filteredSuppliers.Where(s => s.IsActive == isActive);
                }

                // Apply amount filter
                if (decimal.TryParse(MinAmountTextBox?.Text, out decimal minAmount))
                {
                    filteredSuppliers = filteredSuppliers.Where(s => s.TotalAmount >= minAmount);
                }
                if (decimal.TryParse(MaxAmountTextBox?.Text, out decimal maxAmount))
                {
                    filteredSuppliers = filteredSuppliers.Where(s => s.TotalAmount <= maxAmount);
                }

                // Apply invoice count filter
                if (InvoiceCountFilterComboBox?.SelectedItem is ComboBoxItem invoiceItem && invoiceItem.Content.ToString() != "الكل")
                {
                    switch (invoiceItem.Content.ToString())
                    {
                        case "أقل من 5":
                            filteredSuppliers = filteredSuppliers.Where(s => s.InvoiceCount < 5);
                            break;
                        case "5-10":
                            filteredSuppliers = filteredSuppliers.Where(s => s.InvoiceCount >= 5 && s.InvoiceCount <= 10);
                            break;
                        case "أكثر من 10":
                            filteredSuppliers = filteredSuppliers.Where(s => s.InvoiceCount > 10);
                            break;
                    }
                }

                var result = filteredSuppliers.ToList();
                SuppliersDataGrid.ItemsSource = result;
                UpdateResultsCount(result.Count);
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في تطبيق الفلاتر", ex.Message);
            }
        }

        private void UpdateResultsCount(int count)
        {
            try
            {
                if (ResultsCountTextBlock != null)
                {
                    ResultsCountTextBlock.Text = $"{count} نتيجة";
                }
                if (TotalCountTextBlock != null)
                {
                    TotalCountTextBlock.Text = $"({count} مورد)";
                }

                // Update bottom statistics cards
                UpdateBottomStatistics(count);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating results count: {ex.Message}");
            }
        }

        private void UpdateBottomStatistics(int filteredCount = -1)
        {
            try
            {
                var totalSuppliers = _allSuppliers?.Count ?? 0;
                var activeSuppliers = _allSuppliers?.Count(s => s.IsActive) ?? 0;
                var totalAmount = _allSuppliers?.Sum(s => s.TotalAmount) ?? 0;
                var displayedCount = filteredCount >= 0 ? filteredCount : totalSuppliers;

                // Update bottom statistics cards
                if (TotalSuppliersTextBlock != null)
                {
                    TotalSuppliersTextBlock.Text = $"إجمالي الموردين: {totalSuppliers}";
                }

                if (FilteredSuppliersTextBlock != null)
                {
                    FilteredSuppliersTextBlock.Text = $"الموردين المعروضين: {displayedCount}";
                }

                if (ActiveSuppliersTextBlock != null)
                {
                    ActiveSuppliersTextBlock.Text = $"الموردين النشطين: {activeSuppliers}";
                }

                if (TotalAmountTextBlock != null)
                {
                    TotalAmountTextBlock.Text = $"إجمالي المبالغ: {totalAmount:N0} د.ع";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating bottom statistics: {ex.Message}");
            }
        }
    }
}
