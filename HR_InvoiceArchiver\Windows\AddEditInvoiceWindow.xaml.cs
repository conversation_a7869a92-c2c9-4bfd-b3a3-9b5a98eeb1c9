using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Win32;

namespace HR_InvoiceArchiver.Windows
{
    public partial class AddEditInvoiceWindow : Window, INotifyPropertyChanged
    {
        private readonly IInvoiceService _invoiceService;
        private readonly ISupplierService _supplierService;
        private readonly IToastService _toastService;
        
        private Invoice? _currentInvoice;
        private bool _isEditMode;
        
        // Binding Properties
        private string _invoiceNumber = string.Empty;
        private DateTime _invoiceDate = DateTime.Now;
        private DateTime? _dueDate;
        private string _description = string.Empty;
        private string _notes = string.Empty;
        private decimal _totalAmount;
        private decimal _paidAmount;
        private InvoiceStatus _status = InvoiceStatus.Unpaid;
        private string _attachmentPath = string.Empty;
        private string _attachmentName = string.Empty;
        private Supplier? _selectedSupplier;
        private ObservableCollection<Supplier> _suppliers = new();
        private ObservableCollection<InvoiceStatus> _statusOptions = new();

        public AddEditInvoiceWindow()
        {
            // Initialize services from DI container
            _invoiceService = App.ServiceProvider.GetRequiredService<IInvoiceService>();
            _supplierService = App.ServiceProvider.GetRequiredService<ISupplierService>();
            _toastService = App.ServiceProvider.GetRequiredService<IToastService>();
            
            InitializeComponent();
            DataContext = this;
            
            InitializeStatusOptions();
            _ = LoadSuppliersAsync();
        }

        public AddEditInvoiceWindow(Invoice invoice) : this()
        {
            _currentInvoice = invoice;
            _isEditMode = true;

            // تحديث النصوص للتعديل
            WindowTitleTextBlock.Text = "تعديل الفاتورة";
            HeaderTitleTextBlock.Text = "تعديل الفاتورة";
            HeaderDescriptionTextBlock.Text = "قم بتعديل بيانات الفاتورة";
            SaveButtonText.Text = "حفظ التعديلات";

            LoadInvoiceData();
            // LoadSuppliersAsync will handle setting the selected supplier
        }

        #region Properties

        public string InvoiceNumber
        {
            get => _invoiceNumber;
            set => SetProperty(ref _invoiceNumber, value);
        }

        public DateTime InvoiceDate
        {
            get => _invoiceDate;
            set => SetProperty(ref _invoiceDate, value);
        }

        public DateTime? DueDate
        {
            get => _dueDate;
            set => SetProperty(ref _dueDate, value);
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public decimal TotalAmount
        {
            get => _totalAmount;
            set => SetProperty(ref _totalAmount, value);
        }

        public decimal PaidAmount
        {
            get => _paidAmount;
            set => SetProperty(ref _paidAmount, value);
        }

        public InvoiceStatus Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        public string AttachmentPath
        {
            get => _attachmentPath;
            set
            {
                SetProperty(ref _attachmentPath, value);
                AttachmentName = Path.GetFileName(value);
                OnPropertyChanged(nameof(HasAttachment));
            }
        }

        public string AttachmentName
        {
            get => _attachmentName;
            set => SetProperty(ref _attachmentName, value);
        }

        public bool HasAttachment => !string.IsNullOrEmpty(AttachmentPath);

        public Supplier? SelectedSupplier
        {
            get => _selectedSupplier;
            set => SetProperty(ref _selectedSupplier, value);
        }

        public ObservableCollection<Supplier> Suppliers
        {
            get => _suppliers;
            set => SetProperty(ref _suppliers, value);
        }

        public ObservableCollection<InvoiceStatus> StatusOptions
        {
            get => _statusOptions;
            set => SetProperty(ref _statusOptions, value);
        }

        #endregion

        #region Initialization

        private void InitializeStatusOptions()
        {
            StatusOptions = new ObservableCollection<InvoiceStatus>
            {
                InvoiceStatus.Unpaid,
                InvoiceStatus.PartiallyPaid,
                InvoiceStatus.Paid,
                InvoiceStatus.PaidWithDiscount,
                InvoiceStatus.Pending
            };

            StatusComboBox.ItemsSource = StatusOptions;
        }

        private async Task LoadSuppliersAsync()
        {
            try
            {
                var suppliers = await _supplierService.GetAllSuppliersAsync();

                // Ensure UI updates happen on UI thread
                await Dispatcher.InvokeAsync(() =>
                {
                    Suppliers = new ObservableCollection<Supplier>(suppliers);

                    // If in edit mode, set the selected supplier after loading
                    if (_isEditMode && _currentInvoice != null && _currentInvoice.SupplierId > 0)
                    {
                        SelectedSupplier = Suppliers.FirstOrDefault(s => s.Id == _currentInvoice.SupplierId);
                    }
                });
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تحميل قائمة الموردين: {ex.Message}");
            }
        }

        private void LoadInvoiceData()
        {
            if (_currentInvoice == null) return;

            InvoiceNumber = _currentInvoice.InvoiceNumber;
            InvoiceDate = _currentInvoice.InvoiceDate;
            DueDate = _currentInvoice.DueDate;
            Description = _currentInvoice.Description ?? string.Empty;
            Notes = _currentInvoice.Notes ?? string.Empty;
            TotalAmount = _currentInvoice.Amount;
            PaidAmount = _currentInvoice.PaidAmount;
            Status = _currentInvoice.Status;
            AttachmentPath = _currentInvoice.AttachmentPath ?? string.Empty;

            // The supplier will be set in LoadSuppliersAsync after suppliers are loaded
        }

        #endregion

        #region Event Handlers

        private void BrowseAttachmentButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختيار مرفق الفاتورة",
                Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|ملفات الصور (*.jpg;*.jpeg;*.png;*.bmp)|*.jpg;*.jpeg;*.png;*.bmp|ملفات Word (*.doc;*.docx)|*.doc;*.docx|ملفات Excel (*.xls;*.xlsx)|*.xls;*.xlsx",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                AttachmentPath = openFileDialog.FileName;
            }
        }

        private void RemoveAttachmentButton_Click(object sender, RoutedEventArgs e)
        {
            AttachmentPath = string.Empty;
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                SaveButton.IsEnabled = false;

                var invoice = CreateInvoiceFromInput();

                if (_isEditMode && _currentInvoice != null)
                {
                    await _invoiceService.UpdateInvoiceAsync(invoice);
                    _toastService.ShowSuccess("نجح", "تم تحديث الفاتورة بنجاح");
                }
                else
                {
                    await _invoiceService.CreateInvoiceAsync(invoice);
                    _toastService.ShowSuccess("نجح", "تم إنشاء الفاتورة بنجاح");
                }

                DialogResult = true;
                Close();
            }
            catch (ArgumentException ex)
            {
                // أخطاء التحقق من البيانات
                _toastService.ShowError("خطأ في البيانات", ex.Message);
                ValidationMessageTextBlock.Text = ex.Message;
                ValidationMessageTextBlock.Visibility = Visibility.Visible;
            }
            catch (InvalidOperationException ex)
            {
                // أخطاء العمليات
                _toastService.ShowError("خطأ في العملية", ex.Message);
            }
            catch (Exception ex)
            {
                string errorMessage = ex.Message;

                // معالجة خاصة لأخطاء قاعدة البيانات
                if (ex.InnerException?.Message?.Contains("UNIQUE constraint failed") == true)
                {
                    if (ex.InnerException.Message.Contains("Suppliers.Id"))
                    {
                        errorMessage = "خطأ في بيانات المورد. يرجى إعادة اختيار المورد والمحاولة مرة أخرى.";
                    }
                    else if (ex.InnerException.Message.Contains("InvoiceNumber"))
                    {
                        errorMessage = "رقم الفاتورة موجود مسبقاً. يرجى استخدام رقم مختلف.";
                    }
                    else
                    {
                        errorMessage = "يوجد تضارب في البيانات. يرجى التحقق من البيانات المدخلة.";
                    }
                }
                else if (ex.InnerException != null)
                {
                    errorMessage += $"\nالتفاصيل: {ex.InnerException.Message}";
                }

                _toastService.ShowError("خطأ في حفظ الفاتورة", errorMessage);

                // إظهار رسالة في النافذة أيضاً
                ValidationMessageTextBlock.Text = errorMessage;
                ValidationMessageTextBlock.Visibility = Visibility.Visible;
            }
            finally
            {
                SaveButton.IsEnabled = true;
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void TitleBar_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (e.ButtonState == System.Windows.Input.MouseButtonState.Pressed)
            {
                DragMove();
            }
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void ScrollViewer_PreviewMouseWheel(object sender, System.Windows.Input.MouseWheelEventArgs e)
        {
            if (sender is ScrollViewer scrollViewer)
            {
                // تحديد مقدار السكرول بناءً على سرعة العجلة (أبطأ بكثير من الافتراضي)
                double scrollAmount = e.Delta > 0 ? -30 : 30; // مقدار ثابت وبطيء

                // تطبيق السكرول بسلاسة
                double newOffset = scrollViewer.VerticalOffset + scrollAmount;
                newOffset = Math.Max(0, Math.Min(newOffset, scrollViewer.ScrollableHeight));

                scrollViewer.ScrollToVerticalOffset(newOffset);
                e.Handled = true;
            }
        }

        #endregion

        #region Validation and Data Creation

        private bool ValidateInput()
        {
            ValidationMessageTextBlock.Visibility = Visibility.Collapsed;

            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(InvoiceNumber))
                errors.Add("رقم الفاتورة مطلوب");

            if (SelectedSupplier == null)
                errors.Add("يجب اختيار المورد");

            if (TotalAmount <= 0)
                errors.Add("المبلغ الإجمالي يجب أن يكون أكبر من صفر");

            if (PaidAmount < 0)
                errors.Add("المبلغ المدفوع لا يمكن أن يكون سالباً");

            if (PaidAmount > TotalAmount)
                errors.Add("المبلغ المدفوع لا يمكن أن يكون أكبر من المبلغ الإجمالي");

            if (InvoiceDate > DateTime.Now.AddDays(1))
                errors.Add("تاريخ الفاتورة لا يمكن أن يكون في المستقبل");

            if (DueDate.HasValue && DueDate < InvoiceDate)
                errors.Add("تاريخ الاستحقاق لا يمكن أن يكون قبل تاريخ الفاتورة");

            if (errors.Any())
            {
                ValidationMessageTextBlock.Text = string.Join("\n", errors);
                ValidationMessageTextBlock.Visibility = Visibility.Visible;
                return false;
            }

            return true;
        }

        private Invoice CreateInvoiceFromInput()
        {
            var invoice = new Invoice
            {
                InvoiceNumber = InvoiceNumber.Trim(),
                InvoiceDate = InvoiceDate,
                DueDate = DueDate,
                Description = Description?.Trim(),
                Notes = Notes?.Trim(),
                Amount = TotalAmount,
                PaidAmount = PaidAmount,
                Status = Status,
                SupplierId = SelectedSupplier!.Id,
                // لا نضع Supplier هنا لتجنب محاولة Entity Framework إنشاء مورد جديد
                AttachmentPath = string.IsNullOrWhiteSpace(AttachmentPath) ? null : AttachmentPath.Trim(),
                CreatedDate = _isEditMode ? _currentInvoice!.CreatedDate : DateTime.Now,
                UpdatedDate = DateTime.Now
            };

            // في حالة التعديل، نحتاج إلى الاحتفاظ بـ Id الفاتورة
            if (_isEditMode && _currentInvoice != null)
            {
                invoice.Id = _currentInvoice.Id;
            }

            return invoice;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
