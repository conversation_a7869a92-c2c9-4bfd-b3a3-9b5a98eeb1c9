<Window x:Class="HR_InvoiceArchiver.Windows.GoogleDriveGuideWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="دليل إعداد Google Drive"
        Height="650" Width="550"
        FlowDirection="RightToLeft"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <Style x:Key="StepCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
        </Style>

        <Style x:Key="StepNumberStyle" TargetType="Border">
            <Setter Property="Width" Value="25"/>
            <Setter Property="Height" Value="25"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <ScrollViewer VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- Header -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,25">
                    <materialDesign:PackIcon Kind="GoogleDrive" 
                                           Width="32" Height="32" 
                                           Foreground="#4285F4"
                                           VerticalAlignment="Center"/>
                    <StackPanel Margin="15,0,0,0">
                        <TextBlock Text="دليل إعداد Google Drive" 
                                  FontSize="20" FontWeight="Bold"/>
                        <TextBlock Text="اتبع هذه الخطوات للحصول على ملف credentials.json" 
                                  FontSize="12" Foreground="#666"/>
                    </StackPanel>
                </StackPanel>

                <!-- Step 1 -->
                <materialDesign:Card Style="{StaticResource StepCardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <Border Style="{StaticResource StepNumberStyle}">
                                <TextBlock Text="1" FontSize="12" FontWeight="Bold" 
                                          Foreground="White" HorizontalAlignment="Center" 
                                          VerticalAlignment="Center"/>
                            </Border>
                            <TextBlock Text="إنشاء مشروع Google Cloud" 
                                      FontSize="14" FontWeight="Bold"
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                        
                        <TextBlock TextWrapping="Wrap" FontSize="12" Margin="0,0,0,10">
                            • اذهب إلى: https://console.cloud.google.com<LineBreak/>
                            • انقر على "Select a project" ثم "New Project"<LineBreak/>
                            • أدخل اسم المشروع: "HR Invoice Archiver"<LineBreak/>
                            • انقر "Create"
                        </TextBlock>
                        
                        <Button Content="فتح Google Cloud Console" 
                               Background="#4285F4" Foreground="White"
                               Height="30" FontSize="11"
                               Click="OpenGoogleCloudConsole_Click"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Step 2 -->
                <materialDesign:Card Style="{StaticResource StepCardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <Border Style="{StaticResource StepNumberStyle}">
                                <TextBlock Text="2" FontSize="12" FontWeight="Bold" 
                                          Foreground="White" HorizontalAlignment="Center" 
                                          VerticalAlignment="Center"/>
                            </Border>
                            <TextBlock Text="تفعيل Google Drive API" 
                                      FontSize="14" FontWeight="Bold"
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                        
                        <TextBlock TextWrapping="Wrap" FontSize="12" Margin="0,0,0,10">
                            • في Google Cloud Console، اذهب إلى "APIs &amp; Services" → "Library"<LineBreak/>
                            • ابحث عن "Google Drive API"<LineBreak/>
                            • انقر عليه ثم انقر "Enable"
                        </TextBlock>
                        
                        <Button Content="فتح APIs Library" 
                               Background="#34A853" Foreground="White"
                               Height="30" FontSize="11"
                               Click="OpenAPIsLibrary_Click"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Step 3 -->
                <materialDesign:Card Style="{StaticResource StepCardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <Border Style="{StaticResource StepNumberStyle}">
                                <TextBlock Text="3" FontSize="12" FontWeight="Bold" 
                                          Foreground="White" HorizontalAlignment="Center" 
                                          VerticalAlignment="Center"/>
                            </Border>
                            <TextBlock Text="إنشاء OAuth 2.0 Credentials" 
                                      FontSize="14" FontWeight="Bold"
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                        
                        <TextBlock TextWrapping="Wrap" FontSize="12" Margin="0,0,0,10">
                            • اذهب إلى "APIs &amp; Services" → "Credentials"<LineBreak/>
                            • انقر "Create Credentials" → "OAuth client ID"<LineBreak/>
                            • إذا طُلب منك، قم بإعداد OAuth consent screen أولاً<LineBreak/>
                            • اختر Application type: "Desktop application"<LineBreak/>
                            • أدخل Name: "HR Invoice Archiver"<LineBreak/>
                            • انقر "Create"
                        </TextBlock>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Step 4 -->
                <materialDesign:Card Style="{StaticResource StepCardStyle}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <Border Style="{StaticResource StepNumberStyle}">
                                <TextBlock Text="4" FontSize="12" FontWeight="Bold" 
                                          Foreground="White" HorizontalAlignment="Center" 
                                          VerticalAlignment="Center"/>
                            </Border>
                            <TextBlock Text="تحميل ملف الاعتمادات" 
                                      FontSize="14" FontWeight="Bold"
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                        
                        <TextBlock TextWrapping="Wrap" FontSize="12" Margin="0,0,0,10">
                            • بعد إنشاء OAuth client، ستظهر نافذة بها Client ID و Client Secret<LineBreak/>
                            • انقر على "Download JSON" لتحميل ملف الاعتمادات<LineBreak/>
                            • احفظ الملف باسم "credentials.json"<LineBreak/>
                            • استخدم هذا الملف في التطبيق
                        </TextBlock>
                        
                        <Border Background="#FFF3CD" BorderBrush="#FFEAA7" BorderThickness="1" 
                               CornerRadius="4" Padding="10" Margin="0,10,0,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Warning" 
                                                       Width="16" Height="16" 
                                                       Foreground="#856404"
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="احتفظ بملف credentials.json في مكان آمن ولا تشاركه مع أحد"
                                          FontSize="11" Foreground="#856404"
                                          Margin="8,0,0,0" TextWrapping="Wrap"
                                          VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </materialDesign:Card>

                <!-- OAuth Consent Screen Info -->
                <materialDesign:Card Style="{StaticResource StepCardStyle}" Background="#E3F2FD">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="Information" 
                                                   Width="20" Height="20" 
                                                   Foreground="#1976D2"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Text="معلومات إضافية: OAuth Consent Screen" 
                                      FontSize="13" FontWeight="Bold"
                                      Foreground="#1976D2"
                                      Margin="8,0,0,0"
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                        
                        <TextBlock TextWrapping="Wrap" FontSize="11" Foreground="#1976D2">
                            إذا طُلب منك إعداد OAuth consent screen:<LineBreak/>
                            • اختر "External" للاستخدام الشخصي<LineBreak/>
                            • أدخل App name: "HR Invoice Archiver"<LineBreak/>
                            • أدخل User support email: بريدك الإلكتروني<LineBreak/>
                            • أدخل Developer contact information: بريدك الإلكتروني<LineBreak/>
                            • احفظ واستمر
                        </TextBlock>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Google Security Warning -->
                <materialDesign:Card Style="{StaticResource ModernCardStyle}"
                                    Background="#FFF3CD"
                                    Margin="0,15,0,0">
                    <StackPanel Margin="20">
                        <TextBlock Text="🛡️ رسالة تحذير Google"
                                  FontWeight="Bold"
                                  FontSize="14"
                                  Foreground="#856404"
                                  Margin="0,0,0,10"/>

                        <TextBlock TextWrapping="Wrap"
                                  FontSize="12"
                                  Foreground="#856404"
                                  Margin="0,0,0,8">
                            <Run Text="إذا ظهرت رسالة 'تم حظر إمكانية الوصول' من Google:"/>
                        </TextBlock>

                        <TextBlock TextWrapping="Wrap"
                                  FontSize="12"
                                  Foreground="#856404"
                                  Margin="0,0,0,5">
                            <Run Text="1. انقر على 'الانتقال إلى HR Invoice Archiver (غير آمن)'"/>
                        </TextBlock>

                        <TextBlock TextWrapping="Wrap"
                                  FontSize="12"
                                  Foreground="#856404"
                                  Margin="0,0,0,5">
                            <Run Text="2. أو انقر على 'إعدادات متقدمة' ثم 'الانتقال إلى التطبيق'"/>
                        </TextBlock>

                        <TextBlock TextWrapping="Wrap"
                                  FontSize="12"
                                  Foreground="#856404"
                                  Margin="0,0,0,5">
                            <Run Text="3. هذا التحذير طبيعي للتطبيقات الشخصية وآمن للاستخدام"/>
                        </TextBlock>

                        <TextBlock TextWrapping="Wrap"
                                  FontSize="12"
                                  Foreground="#856404"
                                  FontWeight="SemiBold"
                                  Margin="0,10,0,0">
                            <Run Text="💡 نصيحة: يمكنك إضافة بريدك الإلكتروني كـ 'Test User' في Google Cloud Console لتجنب هذا التحذير"/>
                        </TextBlock>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Close Button -->
                <Button Content="إغلاق" 
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Background="#2196F3"
                       Width="100" Height="35"
                       HorizontalAlignment="Center"
                       Margin="0,20,0,0"
                       Click="CloseButton_Click"/>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
