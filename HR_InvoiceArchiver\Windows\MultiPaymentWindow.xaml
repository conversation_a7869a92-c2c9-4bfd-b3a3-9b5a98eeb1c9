<Window x:Class="HR_InvoiceArchiver.Windows.MultiPaymentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="وصل دفع متعدد"
        Height="950" Width="1400"
        WindowStartupLocation="CenterOwner"
        Background="Transparent"
        FontFamily="{DynamicResource MaterialDesignFont}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize"
        MinHeight="900" MinWidth="1200"
        WindowStyle="None"
        AllowsTransparency="True">

    <Window.Resources>
        <!-- Modern Gradient Brushes -->
        <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="MultiPaymentGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#FF6B73" Offset="0"/>
            <GradientStop Color="#009FFF" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SuccessGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#4CAF50" Offset="0"/>
            <GradientStop Color="#8BC34A" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#40000000" BlurRadius="20" ShadowDepth="8" Direction="270"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Enhanced Button Style -->
        <Style x:Key="EnhancedButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="45"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
        </Style>
    </Window.Resources>

    <!-- Main Container with Rounded Border -->
    <Border Background="White" CornerRadius="20" Margin="10">
        <Border.Effect>
            <DropShadowEffect Color="#40000000" BlurRadius="30" ShadowDepth="15" Direction="270"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0" Background="{StaticResource MultiPaymentGradient}" CornerRadius="20,20,0,0">
                <Grid Margin="30,25">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock x:Name="WindowTitleTextBlock" Text="وصل دفع متعدد" 
                                 FontSize="24" FontWeight="Bold" Foreground="White"/>
                        <TextBlock x:Name="HeaderDescriptionTextBlock" Text="دفع عدة فواتير في وصل واحد" 
                                 FontSize="14" Foreground="White" Opacity="0.9" Margin="0,5,0,0"/>
                    </StackPanel>

                    <Button Grid.Column="1" x:Name="CloseButton" 
                          Style="{StaticResource MaterialDesignIconButton}"
                          Width="40" Height="40" 
                          Foreground="White"
                          Click="CloseButton_Click">
                        <materialDesign:PackIcon Kind="Close" Width="24" Height="24"/>
                    </Button>
                </Grid>
            </Border>

            <!-- Content -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="30,20">
                <StackPanel>
                    <!-- Step 1: Supplier Selection -->
                    <materialDesign:Card Style="{StaticResource ModernCardStyle}">
                        <StackPanel Margin="25">
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <materialDesign:PackIcon Kind="Numeric1Circle" Width="24" Height="24"
                                                       Foreground="{StaticResource PrimaryGradientBrush}" Margin="0,0,10,0"/>
                                <TextBlock Text="اختيار المورد" FontSize="18" FontWeight="SemiBold"
                                         Foreground="{StaticResource PrimaryGradientBrush}"/>
                            </StackPanel>

                            <ComboBox x:Name="SupplierComboBox"
                                    materialDesign:HintAssist.Hint="اختر المورد المراد تسديد فواتيره"
                                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                    FontSize="16" Margin="0,0,0,10"
                                    SelectionChanged="SupplierComboBox_SelectionChanged">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Name}" FontSize="14"/>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Step 2: Payment Details -->
                    <materialDesign:Card Style="{StaticResource ModernCardStyle}">
                        <StackPanel Margin="25">
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <materialDesign:PackIcon Kind="Numeric2Circle" Width="24" Height="24"
                                                       Foreground="{StaticResource PrimaryGradientBrush}" Margin="0,0,10,0"/>
                                <TextBlock Text="تفاصيل الدفع" FontSize="18" FontWeight="SemiBold"
                                         Foreground="{StaticResource PrimaryGradientBrush}"/>
                            </StackPanel>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="20"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="20"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBox x:Name="ReceiptNumberTextBox"
                                           materialDesign:HintAssist.Hint="أدخل رقم الوصل"
                                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                           FontSize="14" Margin="0,0,0,10"
                                           materialDesign:HintAssist.HelperText="مثال: REC-2024-001"/>

                                    <TextBox x:Name="TotalPaymentAmountTextBox"
                                           materialDesign:HintAssist.Hint="إجمالي المبلغ المدفوع (محسوب تلقائياً)"
                                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                           FontSize="16" Margin="0,0,0,10"
                                           IsReadOnly="True"
                                           Background="#F5F5F5"
                                           FontWeight="Bold"/>
                                </StackPanel>

                                <StackPanel Grid.Column="2">
                                    <DatePicker x:Name="PaymentDatePicker"
                                              materialDesign:HintAssist.Hint="تاريخ الدفع"
                                              Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                              FontSize="14" Margin="0,0,0,10"/>

                                    <ComboBox x:Name="PaymentMethodComboBox"
                                            materialDesign:HintAssist.Hint="طريقة الدفع"
                                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                            FontSize="14" Margin="0,0,0,10">
                                        <ComboBoxItem Content="نقدي" Tag="Cash"/>
                                        <ComboBoxItem Content="شيك" Tag="Check"/>
                                        <ComboBoxItem Content="تحويل بنكي" Tag="BankTransfer"/>
                                        <ComboBoxItem Content="بطاقة ائتمان" Tag="CreditCard"/>
                                    </ComboBox>
                                </StackPanel>

                                <StackPanel Grid.Column="4">
                                    <TextBox x:Name="NotesTextBox"
                                           materialDesign:HintAssist.Hint="ملاحظات"
                                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                           FontSize="14" Margin="0,0,0,10"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Step 3: Invoice Selection -->
                    <materialDesign:Card Style="{StaticResource ModernCardStyle}">
                        <StackPanel Margin="25">
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <materialDesign:PackIcon Kind="Numeric3Circle" Width="24" Height="24"
                                                       Foreground="{StaticResource PrimaryGradientBrush}" Margin="0,0,10,0"/>
                                <TextBlock Text="اختيار الفواتير المراد تسديدها" FontSize="18" FontWeight="SemiBold"
                                         Foreground="{StaticResource PrimaryGradientBrush}"/>
                            </StackPanel>

                            <!-- Invoices List -->
                            <DataGrid x:Name="InvoicesDataGrid"
                                    AutoGenerateColumns="False"
                                    CanUserAddRows="False"
                                    CanUserDeleteRows="False"
                                    CanUserReorderColumns="False"
                                    CanUserResizeRows="False"
                                    HeadersVisibility="Column"
                                    GridLinesVisibility="All"
                                    SelectionMode="Single"
                                    Background="White"
                                    BorderThickness="1"
                                    BorderBrush="#E0E0E0"
                                    FontSize="14"
                                    RowHeight="65"
                                    MinHeight="300"
                                    AlternatingRowBackground="#F8F9FA"
                                    RowBackground="White"
                                    ColumnHeaderHeight="45"
                                    ScrollViewer.VerticalScrollBarVisibility="Auto"
                                    ScrollViewer.HorizontalScrollBarVisibility="Auto">
                                
                                <DataGrid.Columns>
                                    <DataGridCheckBoxColumn Header="✓" Binding="{Binding IsSelected}" Width="50"/>
                                    <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="100"/>
                                    <DataGridTextColumn Header="تاريخ الفاتورة" Binding="{Binding InvoiceDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                                    <DataGridTextColumn Header="مبلغ الفاتورة" Binding="{Binding InvoiceAmount, StringFormat=N0}" Width="100"/>
                                    <DataGridTextColumn Header="المدفوع سابقاً" Binding="{Binding PaidAmount, StringFormat=N0}" Width="100"/>
                                    <DataGridTextColumn Header="المتبقي" Binding="{Binding RemainingAmount, StringFormat=N0}" Width="100"/>

                                    <DataGridTemplateColumn Header="🎯 حالة التسديد" Width="160">
                                        <DataGridTemplateColumn.HeaderStyle>
                                            <Style TargetType="DataGridColumnHeader">
                                                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Background" Value="#E3F2FD"/>
                                                <Setter Property="Foreground" Value="#1976D2"/>
                                            </Style>
                                        </DataGridTemplateColumn.HeaderStyle>
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <ComboBox SelectedValue="{Binding PaymentStatus, UpdateSourceTrigger=PropertyChanged}"
                                                        Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                        FontSize="12" Margin="3"
                                                        Background="White"
                                                        SelectionChanged="PaymentStatus_SelectionChanged">
                                                    <ComboBoxItem Content="💯 تسديد كامل" Tag="FullPayment"/>
                                                    <ComboBoxItem Content="📊 تسديد جزئي" Tag="PartialPayment"/>
                                                    <ComboBoxItem Content="🎯 تسديد وبخصم" Tag="PaymentWithDiscount"/>
                                                    <ComboBoxItem Content="💸 تسديد واسترجاع" Tag="PaymentWithRefund"/>
                                                </ComboBox>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>

                                    <DataGridTemplateColumn Header="💰 المبلغ المراد دفعه" Width="140">
                                        <DataGridTemplateColumn.HeaderStyle>
                                            <Style TargetType="DataGridColumnHeader">
                                                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Background" Value="#E8F5E8"/>
                                                <Setter Property="Foreground" Value="#2E7D32"/>
                                            </Style>
                                        </DataGridTemplateColumn.HeaderStyle>
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBox x:Name="PaymentAmountTextBox"
                                                       Text="{Binding PaymentAmount, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                                       Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                       FontSize="13" Margin="5"
                                                       TextAlignment="Center"
                                                       FontWeight="SemiBold"
                                                       Background="White"
                                                       BorderBrush="#4CAF50"
                                                       BorderThickness="2"
                                                       Foreground="#2E7D32"
                                                       Height="35"
                                                       VerticalAlignment="Center"
                                                       PreviewTextInput="NumericTextBox_PreviewTextInput"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>

                                    <DataGridTemplateColumn Header="🎯 مبلغ الخصم" Width="120">
                                        <DataGridTemplateColumn.HeaderStyle>
                                            <Style TargetType="DataGridColumnHeader">
                                                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Background" Value="#FFF3E0"/>
                                                <Setter Property="Foreground" Value="#F57C00"/>
                                            </Style>
                                        </DataGridTemplateColumn.HeaderStyle>
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBox x:Name="DiscountAmountTextBox"
                                                       Text="{Binding DiscountAmount, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                                       Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                       FontSize="13" Margin="5"
                                                       TextAlignment="Center"
                                                       FontWeight="SemiBold"
                                                       Background="White"
                                                       BorderBrush="#FF9800"
                                                       BorderThickness="2"
                                                       Foreground="#F57C00"
                                                       Height="35"
                                                       VerticalAlignment="Center"
                                                       PreviewTextInput="NumericTextBox_PreviewTextInput"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>

                                    <DataGridTextColumn Header="✅ سيتم دفعه" Binding="{Binding WillBePaid, StringFormat=N0}" Width="110">
                                        <DataGridTextColumn.HeaderStyle>
                                            <Style TargetType="DataGridColumnHeader">
                                                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Background" Value="#E8F5E8"/>
                                                <Setter Property="Foreground" Value="#2E7D32"/>
                                            </Style>
                                        </DataGridTextColumn.HeaderStyle>
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Foreground" Value="#2E7D32"/>
                                                <Setter Property="Background" Value="#F1F8E9"/>
                                                <Setter Property="Padding" Value="5"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <DataGridTextColumn Header="⏳ سيتبقى" Binding="{Binding WillRemain, StringFormat=N0}" Width="110">
                                        <DataGridTextColumn.HeaderStyle>
                                            <Style TargetType="DataGridColumnHeader">
                                                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Background" Value="#FFEBEE"/>
                                                <Setter Property="Foreground" Value="#D32F2F"/>
                                            </Style>
                                        </DataGridTextColumn.HeaderStyle>
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Foreground" Value="#D32F2F"/>
                                                <Setter Property="Background" Value="#FFEBEE"/>
                                                <Setter Property="Padding" Value="5"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <DataGridTextColumn Header="💸 قيمة الاسترجاع" Binding="{Binding RefundValue, StringFormat=N0}" Width="120">
                                        <DataGridTextColumn.HeaderStyle>
                                            <Style TargetType="DataGridColumnHeader">
                                                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Background" Value="#E3F2FD"/>
                                                <Setter Property="Foreground" Value="#1976D2"/>
                                            </Style>
                                        </DataGridTextColumn.HeaderStyle>
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="13"/>
                                                <Setter Property="Foreground" Value="#1976D2"/>
                                                <Setter Property="Background" Value="#E3F2FD"/>
                                                <Setter Property="Padding" Value="5"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </StackPanel>
                    </materialDesign:Card>


                </StackPanel>
            </ScrollViewer>

            <!-- Enhanced Footer with Summary and Buttons -->
            <Border Grid.Row="2" Background="White"
                  BorderBrush="#E0E0E0"
                  BorderThickness="0,1,0,0"
                  CornerRadius="0,0,20,20"
                  Padding="25,20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="20"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Summary Cards Row -->
                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Invoice Count Card -->
                        <Border Grid.Column="0" Background="#E3F2FD" CornerRadius="12" Padding="15" Margin="5">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="FileDocument" Width="20" Height="20"
                                                       Foreground="#1976D2" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Text="عدد الفواتير" FontSize="12" Foreground="#1976D2"
                                         HorizontalAlignment="Center" FontWeight="SemiBold"/>
                                <TextBlock x:Name="InvoiceCountTextBlock" Text="0" FontSize="22" FontWeight="Bold"
                                         Foreground="#1976D2" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- Total Amount Card -->
                        <Border Grid.Column="1" Background="#E8F5E8" CornerRadius="12" Padding="15" Margin="5">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="CurrencyUsd" Width="20" Height="20"
                                                       Foreground="#2E7D32" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Text="إجمالي المتبقي" FontSize="12" Foreground="#2E7D32"
                                         HorizontalAlignment="Center" FontWeight="SemiBold"/>
                                <TextBlock x:Name="TotalAmountTextBlock" Text="0" FontSize="22" FontWeight="Bold"
                                         Foreground="#2E7D32" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- Paid Amount Card -->
                        <Border Grid.Column="2" Background="#FFF3E0" CornerRadius="12" Padding="15" Margin="5">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="CashMultiple" Width="20" Height="20"
                                                       Foreground="#F57C00" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Text="المبلغ المدفوع" FontSize="12" Foreground="#F57C00"
                                         HorizontalAlignment="Center" FontWeight="SemiBold"/>
                                <TextBlock x:Name="PaidAmountTextBlock" Text="0" FontSize="22" FontWeight="Bold"
                                         Foreground="#F57C00" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- Refund Amount Card -->
                        <Border Grid.Column="3" Background="#FFEBEE" CornerRadius="12" Padding="15" Margin="5">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="ArrowLeftBold" Width="20" Height="20"
                                                       Foreground="#D32F2F" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Text="قيمة الاسترجاع" FontSize="12" Foreground="#D32F2F"
                                         HorizontalAlignment="Center" FontWeight="SemiBold"/>
                                <TextBlock x:Name="RefundAmountTextBlock" Text="0" FontSize="22" FontWeight="Bold"
                                         Foreground="#D32F2F" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- Buttons Row -->
                    <Grid Grid.Row="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0" x:Name="CancelButton"
                              Content="إلغاء"
                              Style="{StaticResource MaterialDesignOutlinedButton}"
                              Background="Transparent"
                              BorderBrush="#D32F2F"
                              Foreground="#D32F2F"
                              Click="CancelButton_Click"
                              HorizontalAlignment="Left"
                              MinWidth="120"
                              Height="45"
                              FontSize="14"
                              FontWeight="SemiBold"/>

                        <Button Grid.Column="1" x:Name="SaveButton"
                              Style="{StaticResource EnhancedButtonStyle}"
                              Background="{StaticResource SuccessGradient}"
                              BorderBrush="{StaticResource SuccessGradient}"
                              Foreground="White"
                              Click="SaveButton_Click"
                              MinWidth="150"
                              Height="45"
                              FontSize="14"
                              FontWeight="SemiBold">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentSave" Width="20" Height="20" Margin="0,0,10,0"/>
                                <TextBlock x:Name="SaveButtonText" Text="حفظ الوصل المتعدد"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </Border>
</Window>
