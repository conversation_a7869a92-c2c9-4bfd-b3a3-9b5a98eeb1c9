<Window x:Class="HR_InvoiceArchiver.Windows.GoogleSecurityGuideWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="حل مشكلة تحذير Google الأمني"
        Height="600" Width="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F5F5F5">

    <Window.Resources>
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="20"/>
        </Style>
        
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>
        
        <Style x:Key="StepTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Foreground" Value="#34495E"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>
    </Window.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="20">
            
            <!-- Header -->
            <TextBlock Text="🛡️ حل مشكلة تحذير Google الأمني" 
                      FontSize="20" 
                      FontWeight="Bold" 
                      Foreground="#E74C3C"
                      HorizontalAlignment="Center"
                      Margin="0,0,0,20"/>

            <!-- Problem Description -->
            <materialDesign:Card Style="{StaticResource ModernCardStyle}" Background="#FFEBEE">
                <StackPanel>
                    <TextBlock Text="🚫 ما هي المشكلة؟" Style="{StaticResource HeaderTextStyle}" Foreground="#C62828"/>
                    
                    <TextBlock Style="{StaticResource StepTextStyle}" Foreground="#C62828">
                        <Run Text="عندما تحاول الاتصال بـ Google Drive، قد تظهر رسالة:"/>
                        <LineBreak/>
                        <Run Text="'تم حظر إمكانية الوصول: لم يكمل تطبيق HR Invoice Archiver عملية التحقّق التي تفرضها Google'"/>
                    </TextBlock>
                    
                    <TextBlock Style="{StaticResource StepTextStyle}" Foreground="#C62828">
                        <Run Text="هذا يحدث لأن Google تحظر التطبيقات غير المُصدقة رسمياً منها لحماية المستخدمين."/>
                    </TextBlock>
                </StackPanel>
            </materialDesign:Card>

            <!-- Solution 1: Bypass Warning -->
            <materialDesign:Card Style="{StaticResource ModernCardStyle}" Background="#E8F5E8">
                <StackPanel>
                    <TextBlock Text="✅ الحل الأول: تجاوز التحذير (الأسرع)" Style="{StaticResource HeaderTextStyle}" Foreground="#2E7D32"/>
                    
                    <TextBlock Style="{StaticResource StepTextStyle}">
                        <Run Text="1. عندما تظهر صفحة التحذير من Google"/>
                    </TextBlock>
                    
                    <TextBlock Style="{StaticResource StepTextStyle}">
                        <Run Text="2. ابحث عن رابط 'إعدادات متقدمة' أو 'Advanced' في أسفل الصفحة"/>
                    </TextBlock>
                    
                    <TextBlock Style="{StaticResource StepTextStyle}">
                        <Run Text="3. انقر على 'إعدادات متقدمة'"/>
                    </TextBlock>
                    
                    <TextBlock Style="{StaticResource StepTextStyle}">
                        <Run Text="4. ستظهر رسالة إضافية، انقر على 'الانتقال إلى HR Invoice Archiver (غير آمن)'"/>
                    </TextBlock>
                    
                    <TextBlock Style="{StaticResource StepTextStyle}" FontWeight="SemiBold" Foreground="#2E7D32">
                        <Run Text="5. أكمل عملية تسجيل الدخول بشكل طبيعي"/>
                    </TextBlock>
                </StackPanel>
            </materialDesign:Card>

            <!-- Solution 2: Test Users -->
            <materialDesign:Card Style="{StaticResource ModernCardStyle}" Background="#E3F2FD">
                <StackPanel>
                    <TextBlock Text="🔧 الحل الثاني: إضافة نفسك كمستخدم اختبار" Style="{StaticResource HeaderTextStyle}" Foreground="#1565C0"/>
                    
                    <TextBlock Style="{StaticResource StepTextStyle}">
                        <Run Text="1. اذهب إلى Google Cloud Console"/>
                    </TextBlock>
                    
                    <Button Content="🔗 فتح Google Cloud Console" 
                           Background="#1976D2" 
                           Foreground="White"
                           Margin="0,5,0,15"
                           Padding="10,5"
                           Click="OpenCloudConsoleButton_Click"/>
                    
                    <TextBlock Style="{StaticResource StepTextStyle}">
                        <Run Text="2. اذهب إلى: APIs &amp; Services → OAuth consent screen"/>
                    </TextBlock>
                    
                    <TextBlock Style="{StaticResource StepTextStyle}">
                        <Run Text="3. في قسم 'Test users'، انقر على 'ADD USERS'"/>
                    </TextBlock>
                    
                    <TextBlock Style="{StaticResource StepTextStyle}">
                        <Run Text="4. أضف بريدك الإلكتروني الذي تستخدمه مع Google"/>
                    </TextBlock>
                    
                    <TextBlock Style="{StaticResource StepTextStyle}">
                        <Run Text="5. احفظ التغييرات"/>
                    </TextBlock>
                    
                    <TextBlock Style="{StaticResource StepTextStyle}" FontWeight="SemiBold" Foreground="#1565C0">
                        <Run Text="6. الآن لن تظهر رسالة التحذير عند استخدام هذا البريد الإلكتروني"/>
                    </TextBlock>
                </StackPanel>
            </materialDesign:Card>

            <!-- Safety Note -->
            <materialDesign:Card Style="{StaticResource ModernCardStyle}" Background="#FFF3E0">
                <StackPanel>
                    <TextBlock Text="🛡️ هل هذا آمن؟" Style="{StaticResource HeaderTextStyle}" Foreground="#F57C00"/>
                    
                    <TextBlock Style="{StaticResource StepTextStyle}" Foreground="#F57C00">
                        <Run Text="✅ نعم، هذا آمن تماماً. التحذير يظهر فقط لأن التطبيق لم يخضع لعملية التحقق الرسمية من Google."/>
                    </TextBlock>
                    
                    <TextBlock Style="{StaticResource StepTextStyle}" Foreground="#F57C00">
                        <Run Text="✅ التطبيق يستخدم OAuth 2.0 الآمن ولا يحفظ كلمة مرورك."/>
                    </TextBlock>
                    
                    <TextBlock Style="{StaticResource StepTextStyle}" Foreground="#F57C00">
                        <Run Text="✅ يمكنك إلغاء الصلاحيات في أي وقت من إعدادات حسابك في Google."/>
                    </TextBlock>
                </StackPanel>
            </materialDesign:Card>

            <!-- Action Buttons -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                <Button Content="فتح Google Cloud Console" 
                       Background="#1976D2" 
                       Foreground="White"
                       Margin="5"
                       Padding="15,8"
                       Click="OpenCloudConsoleButton_Click"/>
                
                <Button Content="إغلاق" 
                       Background="#757575" 
                       Foreground="White"
                       Margin="5"
                       Padding="15,8"
                       Click="CloseButton_Click"/>
            </StackPanel>

        </StackPanel>
    </ScrollViewer>
</Window>
