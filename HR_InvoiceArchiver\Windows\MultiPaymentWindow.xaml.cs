using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Data.Repositories;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Windows
{
    public partial class MultiPaymentWindow : Window, INotifyPropertyChanged
    {
        private readonly IInvoiceService _invoiceService;
        private readonly IPaymentService _paymentService;
        private readonly ISupplierService _supplierService;
        private readonly IToastService _toastService;

        private ObservableCollection<Supplier> _suppliers = new();
        private ObservableCollection<MultiPaymentInvoiceItem> _availableInvoices = new();
        private Supplier? _selectedSupplier;

        public bool IsSaved { get; private set; }

        public MultiPaymentWindow()
        {
            // Initialize services from DI container
            _invoiceService = App.ServiceProvider.GetRequiredService<IInvoiceService>();
            _paymentService = App.ServiceProvider.GetRequiredService<IPaymentService>();
            _supplierService = App.ServiceProvider.GetRequiredService<ISupplierService>();
            _toastService = App.ServiceProvider.GetRequiredService<IToastService>();

            InitializeComponent();
            DataContext = this;

            // Set default values
            PaymentDatePicker.SelectedDate = DateTime.Today;
            PaymentMethodComboBox.SelectedIndex = 0; // Default to Cash
            ReceiptNumberTextBox.Text = ""; // User will enter receipt number
            TotalPaymentAmountTextBox.Text = "0";

            // Bind data
            SupplierComboBox.ItemsSource = _suppliers;
            InvoicesDataGrid.ItemsSource = _availableInvoices;

            Loaded += MultiPaymentWindow_Loaded;
        }

        private async void MultiPaymentWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadSuppliersAsync();
            UpdateSummary();
        }

        private async Task LoadSuppliersAsync()
        {
            try
            {
                var suppliers = await _supplierService.GetAllSuppliersAsync();
                _suppliers.Clear();

                foreach (var supplier in suppliers)
                {
                    _suppliers.Add(supplier);
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تحميل الموردين: {ex.Message}");
            }
        }

        private async Task LoadInvoicesForSupplierAsync(int supplierId)
        {
            try
            {
                var invoices = await _invoiceService.GetInvoicesBySupplierAsync(supplierId);
                var unpaidInvoices = invoices.Where(i =>
                    (i.Status == InvoiceStatus.Unpaid || i.Status == InvoiceStatus.PartiallyPaid) &&
                    i.RemainingAmount > 0);

                _availableInvoices.Clear();

                foreach (var invoice in unpaidInvoices)
                {
                    var item = new MultiPaymentInvoiceItem
                    {
                        Invoice = invoice,
                        InvoiceNumber = invoice.InvoiceNumber,
                        InvoiceDate = invoice.InvoiceDate,
                        InvoiceAmount = invoice.Amount,
                        PaidAmount = invoice.PaidAmount,
                        RemainingAmount = invoice.RemainingAmount,
                        PaymentStatus = "FullPayment",
                        PaymentAmount = 0,
                        DiscountAmount = 0,
                        RefundValue = 0,
                        WillBePaid = 0,
                        WillRemain = invoice.RemainingAmount,
                        IsSelected = false
                    };

                    // Subscribe to property changes AFTER setting initial values
                    item.PropertyChanged += InvoiceItem_PropertyChanged;
                    _availableInvoices.Add(item);
                }

                UpdateSummary();
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تحميل فواتير المورد: {ex.Message}");
            }
        }

        private void TotalPaymentAmount_TextChanged(object sender, TextChangedEventArgs e)
        {
            // This field is now read-only and calculated automatically
            // No action needed
        }



        private async void SupplierComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (SupplierComboBox.SelectedItem is Supplier selectedSupplier)
            {
                _selectedSupplier = selectedSupplier;
                await LoadInvoicesForSupplierAsync(selectedSupplier.Id);
            }
        }

        private void InvoiceItem_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            var invoice = sender as MultiPaymentInvoiceItem;
            if (invoice == null) return;

            switch (e.PropertyName)
            {
                case nameof(MultiPaymentInvoiceItem.IsSelected):
                    if (invoice.IsSelected && invoice.PaymentAmount == 0)
                    {
                        // Set default payment amount when selected
                        invoice.PaymentAmount = invoice.RemainingAmount;
                    }
                    UpdateInvoiceAmountsBasedOnStatus(invoice);
                    UpdateSummary();
                    break;

                case nameof(MultiPaymentInvoiceItem.PaymentStatus):
                case nameof(MultiPaymentInvoiceItem.PaymentAmount):
                case nameof(MultiPaymentInvoiceItem.DiscountAmount):
                    UpdateInvoiceAmountsBasedOnStatus(invoice);
                    UpdateSummary();
                    break;
            }
        }

        private void PaymentStatus_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ComboBox comboBox && comboBox.DataContext is MultiPaymentInvoiceItem invoice)
            {
                UpdateInvoiceAmountsBasedOnStatus(invoice);
                UpdateSummary();
            }
        }

        private void NumericTextBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // Allow only digits
            e.Handled = !IsTextAllowed(e.Text);
        }

        private static bool IsTextAllowed(string text)
        {
            return text.All(char.IsDigit);
        }



        private PaymentMethod GetSelectedPaymentMethod()
        {
            if (PaymentMethodComboBox.SelectedItem is ComboBoxItem item && item.Tag != null)
            {
                return Enum.TryParse<PaymentMethod>(item.Tag.ToString(), out var method) ? method : PaymentMethod.Cash;
            }
            return PaymentMethod.Cash;
        }

        private void UpdateInvoiceAmountsBasedOnStatus(MultiPaymentInvoiceItem invoice)
        {
            if (!invoice.IsSelected)
            {
                invoice.WillBePaid = 0;
                invoice.WillRemain = invoice.RemainingAmount;
                invoice.RefundValue = 0;
                return;
            }

            // Ensure amounts are not negative
            if (invoice.PaymentAmount < 0) invoice.PaymentAmount = 0;
            if (invoice.DiscountAmount < 0) invoice.DiscountAmount = 0;

            switch (invoice.PaymentStatus)
            {
                case "FullPayment":
                    // تسديد كامل
                    invoice.DiscountAmount = 0;
                    invoice.WillBePaid = Math.Min(invoice.PaymentAmount, invoice.RemainingAmount);
                    invoice.WillRemain = Math.Max(0, invoice.RemainingAmount - invoice.WillBePaid);
                    invoice.RefundValue = 0;
                    break;

                case "PartialPayment":
                    // تسديد جزئي
                    invoice.DiscountAmount = 0;
                    invoice.WillBePaid = Math.Min(invoice.PaymentAmount, invoice.RemainingAmount);
                    invoice.WillRemain = Math.Max(0, invoice.RemainingAmount - invoice.WillBePaid);
                    invoice.RefundValue = 0;
                    break;

                case "PaymentWithDiscount":
                    // تسديد مع خصم
                    var totalCovered = invoice.PaymentAmount + invoice.DiscountAmount;
                    if (totalCovered >= invoice.RemainingAmount)
                    {
                        invoice.WillBePaid = invoice.PaymentAmount;
                        invoice.WillRemain = 0;
                    }
                    else
                    {
                        invoice.WillBePaid = invoice.PaymentAmount;
                        invoice.WillRemain = invoice.RemainingAmount - totalCovered;
                    }
                    invoice.RefundValue = 0;
                    break;

                case "PaymentWithRefund":
                    // تسديد مع استرجاع
                    invoice.DiscountAmount = 0;
                    if (invoice.PaymentAmount > invoice.RemainingAmount)
                    {
                        invoice.WillBePaid = invoice.RemainingAmount;
                        invoice.WillRemain = 0;
                        invoice.RefundValue = invoice.PaymentAmount - invoice.RemainingAmount;
                    }
                    else
                    {
                        invoice.WillBePaid = invoice.PaymentAmount;
                        invoice.WillRemain = invoice.RemainingAmount - invoice.PaymentAmount;
                        invoice.RefundValue = 0;
                    }
                    break;

                default:
                    invoice.WillBePaid = Math.Min(invoice.PaymentAmount, invoice.RemainingAmount);
                    invoice.WillRemain = Math.Max(0, invoice.RemainingAmount - invoice.WillBePaid);
                    invoice.RefundValue = 0;
                    break;
            }
        }

        private void UpdateSummary()
        {
            try
            {
                var selectedInvoices = _availableInvoices.Where(i => i.IsSelected).ToList();
                var totalRemaining = selectedInvoices.Sum(s => s.RemainingAmount);
                var totalWillBePaid = selectedInvoices.Sum(s => s.WillBePaid);
                var totalRefund = selectedInvoices.Sum(s => s.RefundValue);

                InvoiceCountTextBlock.Text = selectedInvoices.Count.ToString();
                TotalAmountTextBlock.Text = totalRemaining.ToString("N0");
                PaidAmountTextBlock.Text = totalWillBePaid.ToString("N0");
                RefundAmountTextBlock.Text = totalRefund.ToString("N0");

                // Update total payment amount based on individual payments
                var totalPaymentNeeded = selectedInvoices.Sum(s => s.PaymentAmount);
                TotalPaymentAmountTextBox.Text = totalPaymentNeeded.ToString("N0");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating summary: {ex.Message}");
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateForm())
                    return;

                SaveButton.IsEnabled = false;
                SaveButtonText.Text = "جاري الحفظ...";

                var selectedInvoices = _availableInvoices.Where(i => i.IsSelected && i.PaymentAmount > 0).ToList();
                var receiptNumber = ReceiptNumberTextBox.Text.Trim();
                var paymentDate = PaymentDatePicker.SelectedDate ?? DateTime.Today;
                var paymentMethod = GetSelectedPaymentMethod();
                var notes = NotesTextBox.Text?.Trim();

                // Create individual payment for each selected invoice
                var paymentTasks = new List<Task>();

                for (int i = 0; i < selectedInvoices.Count; i++)
                {
                    var invoice = selectedInvoices[i];
                    // Use the same receipt number for all invoices in this multi-payment

                    // تحديد حالة الدفع بناءً على الاختيار
                    var paymentStatus = PaymentStatus.FullPayment;
                    switch (invoice.PaymentStatus)
                    {
                        case "FullPayment":
                            paymentStatus = PaymentStatus.FullPayment;
                            break;
                        case "PartialPayment":
                            paymentStatus = PaymentStatus.PartialPayment;
                            break;
                        case "PaymentWithDiscount":
                            paymentStatus = PaymentStatus.PaymentWithDiscount;
                            break;
                        case "PaymentWithRefund":
                            paymentStatus = PaymentStatus.PaymentWithRefund;
                            break;
                    }

                    var payment = new Payment
                    {
                        InvoiceId = invoice.Invoice.Id,
                        ReceiptNumber = receiptNumber,
                        PaymentDate = paymentDate,
                        Amount = invoice.PaymentAmount,
                        DiscountAmount = invoice.DiscountAmount,
                        RefundValue = invoice.RefundValue,
                        Method = paymentMethod,
                        Status = paymentStatus,
                        Notes = notes
                    };

                    paymentTasks.Add(_paymentService.CreatePaymentAsync(payment));
                }

                // Execute all payment creations
                await Task.WhenAll(paymentTasks);

                IsSaved = true;
                DialogResult = true;

                _toastService.ShowSuccess("تم الحفظ", $"تم حفظ {selectedInvoices.Count} وصل دفع بنجاح");
                Close();
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في الحفظ", ex.Message);
            }
            finally
            {
                SaveButton.IsEnabled = true;
                SaveButtonText.Text = "حفظ الوصل";
            }
        }

        private bool ValidateForm()
        {
            if (_selectedSupplier == null)
            {
                _toastService.ShowWarning("تحقق من البيانات", "يرجى اختيار المورد");
                SupplierComboBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ReceiptNumberTextBox.Text))
            {
                _toastService.ShowWarning("تحقق من البيانات", "يرجى إدخال رقم الوصل");
                ReceiptNumberTextBox.Focus();
                return false;
            }

            if (!PaymentDatePicker.SelectedDate.HasValue)
            {
                _toastService.ShowWarning("تحقق من البيانات", "يرجى تحديد تاريخ الدفع");
                PaymentDatePicker.Focus();
                return false;
            }

            var selectedInvoices = _availableInvoices.Where(i => i.IsSelected).ToList();
            if (!selectedInvoices.Any())
            {
                _toastService.ShowWarning("تحقق من البيانات", "يرجى تحديد فاتورة واحدة على الأقل");
                return false;
            }

            // التحقق من أن كل فاتورة محددة لها مبلغ دفع صحيح
            foreach (var invoice in selectedInvoices)
            {
                if (invoice.PaymentAmount <= 0)
                {
                    _toastService.ShowWarning("تحقق من البيانات", $"يرجى إدخال مبلغ دفع صحيح للفاتورة {invoice.InvoiceNumber}");
                    return false;
                }

                // التحقق من صحة مبلغ الخصم
                if (invoice.PaymentStatus == "PaymentWithDiscount" && invoice.DiscountAmount < 0)
                {
                    _toastService.ShowWarning("تحقق من البيانات", $"مبلغ الخصم لا يمكن أن يكون سالباً للفاتورة {invoice.InvoiceNumber}");
                    return false;
                }

                // التحقق من أن المبلغ + الخصم لا يتجاوز المتبقي (إلا في حالة الاسترجاع)
                if (invoice.PaymentStatus != "PaymentWithRefund")
                {
                    var totalPaymentForInvoice = invoice.PaymentAmount + invoice.DiscountAmount;
                    if (totalPaymentForInvoice > invoice.RemainingAmount)
                    {
                        _toastService.ShowWarning("تحقق من البيانات",
                            $"مجموع المبلغ والخصم لا يمكن أن يتجاوز المتبقي للفاتورة {invoice.InvoiceNumber}");
                        return false;
                    }
                }
            }

            return true;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // Helper classes
    public class MultiPaymentInvoiceItem : INotifyPropertyChanged
    {
        private decimal _willBePaid;
        private decimal _willRemain;
        private decimal _paymentAmount;
        private decimal _discountAmount;
        private decimal _refundValue;
        private bool _isSelected;
        private string _paymentStatus = "FullPayment";

        public Invoice Invoice { get; set; } = null!;
        public string InvoiceNumber { get; set; } = string.Empty;
        public DateTime InvoiceDate { get; set; }
        public decimal InvoiceAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged();
            }
        }

        public string PaymentStatus
        {
            get => _paymentStatus;
            set
            {
                _paymentStatus = value;
                OnPropertyChanged();
            }
        }

        public decimal PaymentAmount
        {
            get => _paymentAmount;
            set
            {
                _paymentAmount = value;
                OnPropertyChanged();
            }
        }

        public decimal DiscountAmount
        {
            get => _discountAmount;
            set
            {
                _discountAmount = value;
                OnPropertyChanged();
            }
        }

        public decimal RefundValue
        {
            get => _refundValue;
            set
            {
                _refundValue = value;
                OnPropertyChanged();
            }
        }

        public decimal WillBePaid
        {
            get => _willBePaid;
            set
            {
                _willBePaid = value;
                OnPropertyChanged();
            }
        }

        public decimal WillRemain
        {
            get => _willRemain;
            set
            {
                _willRemain = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
