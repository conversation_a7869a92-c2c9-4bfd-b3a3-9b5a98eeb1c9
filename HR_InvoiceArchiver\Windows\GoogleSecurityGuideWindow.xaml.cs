using System;
using System.Diagnostics;
using System.Windows;

namespace HR_InvoiceArchiver.Windows
{
    /// <summary>
    /// نافذة دليل حل مشكلة تحذير Google الأمني
    /// </summary>
    public partial class GoogleSecurityGuideWindow : Window
    {
        public GoogleSecurityGuideWindow()
        {
            InitializeComponent();
        }

        /// <summary>
        /// فتح Google Cloud Console
        /// </summary>
        private void OpenCloudConsoleButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = "https://console.cloud.google.com/apis/credentials/consent",
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في فتح الرابط: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
