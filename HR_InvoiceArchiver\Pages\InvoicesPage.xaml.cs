using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Windows;
using Microsoft.Extensions.DependencyInjection;
using System.ComponentModel;
using System.Windows.Data;

namespace HR_InvoiceArchiver.Pages
{
    public partial class InvoicesPage : UserControl, INavigationAware
    {
        private readonly IInvoiceService _invoiceService;
        private readonly IPaymentService _paymentService;
        private readonly IToastService _toastService;
        private readonly INavigationService _navigationService;

        public ObservableCollection<Invoice> Invoices { get; set; } = new();
        public ObservableCollection<Invoice> AllInvoices { get; set; } = new();
        private ICollectionView _invoicesView;

        // Pagination support - دعم تقسيم الصفحات
        private PaginationCriteria _currentCriteria = new();
        private PaginationResult<Invoice>? _currentPage;
        private bool _isPaginationEnabled = false; // تعطيل مؤقت لحل مشكلة التتبع

        public InvoicesPage(
            IInvoiceService invoiceService,
            IPaymentService paymentService,
            IToastService toastService,
            INavigationService navigationService)
        {
            InitializeComponent();
            _invoiceService = invoiceService;
            _paymentService = paymentService;
            _toastService = toastService;
            _navigationService = navigationService;

            // Setup collection view for filtering
            _invoicesView = CollectionViewSource.GetDefaultView(Invoices);
            _invoicesView.Filter = FilterInvoices;
            InvoicesDataGrid.ItemsSource = _invoicesView;

            // Add selection changed event for enhanced feedback
            InvoicesDataGrid.SelectionChanged += InvoicesDataGrid_SelectionChanged;

            Loaded += InvoicesPage_Loaded;
        }

        private async void InvoicesPage_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadInvoicesAsync();
        }

        public void OnNavigatedTo(object parameter)
        {
            // Handle navigation parameters
            if (parameter is string action && action == "add")
            {
                // Show add invoice dialog
                AddInvoiceButton_Click(this, new RoutedEventArgs());
            }
            else
            {
                // Refresh data when navigating to this page
                Dispatcher.BeginInvoke(new Action(async () => await LoadInvoicesAsync()));
            }
        }

        public void OnNavigatedFrom()
        {
            // Cleanup when leaving this page
        }

        private async Task LoadInvoicesAsync()
        {
            try
            {
                ShowLoading(true);

                if (_isPaginationEnabled)
                {
                    // استخدام Pagination للأداء الأفضل
                    await LoadInvoicesWithPaginationAsync();
                }
                else
                {
                    // تحميل جميع البيانات (للتوافق مع الكود القديم)
                    await LoadAllInvoicesAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading invoices: {ex.Message}");
                _toastService.ShowError("خطأ", "حدث خطأ في تحميل الفواتير");
            }
            finally
            {
                ShowLoading(false);
            }
        }

        /// <summary>
        /// تحميل الفواتير مع تقسيم الصفحات (محسن للأداء)
        /// </summary>
        private async Task LoadInvoicesWithPaginationAsync()
        {
            var repository = App.ServiceProvider.GetRequiredService<Data.Repositories.IInvoiceRepository>();
            _currentPage = await repository.GetPagedAsync(_currentCriteria);

            Dispatcher.Invoke(() =>
            {
                AllInvoices.Clear();
                Invoices.Clear();

                foreach (var invoice in _currentPage.Items)
                {
                    AllInvoices.Add(invoice);
                    Invoices.Add(invoice);
                }

                UpdateStatistics();
                UpdateEmptyState();
                UpdatePaginationInfo();
                _invoicesView.Refresh();
            });
        }

        /// <summary>
        /// تحميل جميع الفواتير (للتوافق مع الكود القديم)
        /// </summary>
        private async Task LoadAllInvoicesAsync()
        {
            var invoices = await _invoiceService.GetAllInvoicesBasicAsync();

            Dispatcher.Invoke(() =>
            {
                AllInvoices.Clear();
                Invoices.Clear();

                foreach (var invoice in invoices.OrderByDescending(i => i.InvoiceDate))
                {
                    AllInvoices.Add(invoice);
                    Invoices.Add(invoice);
                }

                UpdateStatistics();
                UpdateEmptyState();
                _invoicesView.Refresh();
            });
        }

        private void ShowLoading(bool isLoading)
        {
            LoadingPanel.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
            InvoicesDataGrid.Visibility = isLoading ? Visibility.Collapsed : Visibility.Visible;
        }

        private void UpdateEmptyState()
        {
            EmptyStatePanel.Visibility = Invoices.Count == 0 ? Visibility.Visible : Visibility.Collapsed;
            InvoicesDataGrid.Visibility = Invoices.Count == 0 ? Visibility.Collapsed : Visibility.Visible;
        }

        /// <summary>
        /// تحديث معلومات تقسيم الصفحات
        /// </summary>
        private void UpdatePaginationInfo()
        {
            if (_currentPage == null) return;

            // يمكن إضافة UI elements لعرض معلومات Pagination هنا
            // مثل: "صفحة 1 من 5" أو "عرض 1-50 من 200"

            System.Diagnostics.Debug.WriteLine($"Pagination: Page {_currentPage.PageNumber} of {_currentPage.TotalPages}, Total: {_currentPage.TotalCount}");
        }

        /// <summary>
        /// الانتقال للصفحة التالية
        /// </summary>
        public async Task NextPageAsync()
        {
            if (_currentPage?.HasNextPage == true)
            {
                _currentCriteria.PageNumber++;
                await LoadInvoicesWithPaginationAsync();
            }
        }

        /// <summary>
        /// الانتقال للصفحة السابقة
        /// </summary>
        public async Task PreviousPageAsync()
        {
            if (_currentPage?.HasPreviousPage == true)
            {
                _currentCriteria.PageNumber--;
                await LoadInvoicesWithPaginationAsync();
            }
        }

        /// <summary>
        /// تغيير حجم الصفحة
        /// </summary>
        public async Task ChangePageSizeAsync(int pageSize)
        {
            _currentCriteria.PageSize = pageSize;
            _currentCriteria.PageNumber = 1; // العودة للصفحة الأولى
            await LoadInvoicesWithPaginationAsync();
        }

        private void UpdateStatistics()
        {
            var totalInvoices = AllInvoices.Count;

            // تحديث عداد الفواتير في عنوان الجدول
            TotalInvoicesCountTextBlock.Text = $"({totalInvoices} فاتورة)";
        }

        private bool FilterInvoices(object item)
        {
            if (item is not Invoice invoice) return false;

            // Search filter
            var searchText = SearchTextBox?.Text?.Trim().ToLower() ?? "";
            if (!string.IsNullOrEmpty(searchText))
            {
                var matchesSearch = invoice.InvoiceNumber.ToLower().Contains(searchText) ||
                                  invoice.SupplierName.ToLower().Contains(searchText) ||
                                  (invoice.Description?.ToLower().Contains(searchText) ?? false);

                if (!matchesSearch) return false;
            }

            // Status filter
            if (StatusFilterComboBox?.SelectedItem is ComboBoxItem statusItem && statusItem.Tag != null)
            {
                var selectedStatus = Enum.Parse<InvoiceStatus>(statusItem.Tag.ToString()!);
                if (invoice.Status != selectedStatus) return false;
            }

            // Date range filter
            if (FromDatePicker?.SelectedDate.HasValue == true)
            {
                if (invoice.InvoiceDate < FromDatePicker.SelectedDate.Value) return false;
            }

            if (ToDatePicker?.SelectedDate.HasValue == true)
            {
                if (invoice.InvoiceDate > ToDatePicker.SelectedDate.Value) return false;
            }

            return true;
        }

        // Event Handlers
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _invoicesView?.Refresh();
        }

        private void FilterButton_Click(object sender, RoutedEventArgs e)
        {
            AdvancedFiltersExpander.IsExpanded = !AdvancedFiltersExpander.IsExpanded;
        }

        private void StatusFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _invoicesView?.Refresh();
        }

        private void DateFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            _invoicesView?.Refresh();
        }

        private void ClearFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = "";
            StatusFilterComboBox.SelectedIndex = 0;
            FromDatePicker.SelectedDate = null;
            ToDatePicker.SelectedDate = null;
            _invoicesView?.Refresh();
        }

        private void AddInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addInvoiceWindow = new Windows.AddEditInvoiceWindow();
                addInvoiceWindow.Owner = Window.GetWindow(this);
                if (addInvoiceWindow.ShowDialog() == true)
                {
                    // تحديث القائمة بعد إضافة فاتورة جديدة
                    Dispatcher.BeginInvoke(new Action(async () => await LoadInvoicesAsync()));
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في فتح نموذج إضافة الفاتورة: {ex.Message}");
            }
        }



        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadInvoicesAsync();
        }

        private void InvoicesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (InvoicesDataGrid.SelectedItem is Invoice selectedInvoice)
                {
                    // Add subtle selection feedback
                    AnimateRowSelection();

                    // Update status bar or show invoice details if needed
                    System.Diagnostics.Debug.WriteLine($"Selected invoice: {selectedInvoice.InvoiceNumber}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in SelectionChanged: {ex.Message}");
            }
        }

        private void AnimateRowSelection()
        {
            try
            {
                if (InvoicesDataGrid.SelectedItem != null)
                {
                    var selectedRow = InvoicesDataGrid.ItemContainerGenerator
                        .ContainerFromItem(InvoicesDataGrid.SelectedItem) as DataGridRow;

                    if (selectedRow != null)
                    {
                        // Create a subtle glow effect
                        var storyboard = new Storyboard();

                        // Opacity animation for subtle highlight
                        var opacityAnimation = new DoubleAnimation(0.7, 1.0, TimeSpan.FromMilliseconds(300))
                        {
                            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
                        };

                        Storyboard.SetTarget(opacityAnimation, selectedRow);
                        Storyboard.SetTargetProperty(opacityAnimation, new PropertyPath("Opacity"));

                        storyboard.Children.Add(opacityAnimation);
                        storyboard.Begin();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in AnimateRowSelection: {ex.Message}");
            }
        }

        private void InvoicesDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                try
                {
                    // Add visual feedback for double-click
                    AnimateRowDoubleClick();

                    var editInvoiceWindow = new AddEditInvoiceWindow(selectedInvoice);
                    if (editInvoiceWindow.ShowDialog() == true)
                    {
                        _toastService.ShowSuccess("تم تحديث الفاتورة بنجاح", "تمت العملية بنجاح");
                        Dispatcher.BeginInvoke(new Action(async () => await LoadInvoicesAsync()));
                    }
                }
                catch (Exception ex)
                {
                    _toastService.ShowError("خطأ في تحديث الفاتورة", ex.Message);
                }
            }
        }

        private void AnimateRowDoubleClick()
        {
            try
            {
                if (InvoicesDataGrid.SelectedItem != null)
                {
                    var selectedRow = InvoicesDataGrid.ItemContainerGenerator
                        .ContainerFromItem(InvoicesDataGrid.SelectedItem) as DataGridRow;

                    if (selectedRow != null)
                    {
                        // Create a pulse animation
                        var scaleTransform = new ScaleTransform(1, 1);
                        selectedRow.RenderTransform = scaleTransform;
                        selectedRow.RenderTransformOrigin = new Point(0.5, 0.5);

                        var storyboard = new System.Windows.Media.Animation.Storyboard();

                        // Scale up animation
                        var scaleUpX = new System.Windows.Media.Animation.DoubleAnimation(1, 1.02, TimeSpan.FromMilliseconds(100));
                        var scaleUpY = new System.Windows.Media.Animation.DoubleAnimation(1, 1.02, TimeSpan.FromMilliseconds(100));

                        // Scale down animation
                        var scaleDownX = new System.Windows.Media.Animation.DoubleAnimation(1.02, 1, TimeSpan.FromMilliseconds(100))
                        {
                            BeginTime = TimeSpan.FromMilliseconds(100)
                        };
                        var scaleDownY = new System.Windows.Media.Animation.DoubleAnimation(1.02, 1, TimeSpan.FromMilliseconds(100))
                        {
                            BeginTime = TimeSpan.FromMilliseconds(100)
                        };

                        System.Windows.Media.Animation.Storyboard.SetTarget(scaleUpX, selectedRow);
                        System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleUpX, new PropertyPath("RenderTransform.ScaleX"));

                        System.Windows.Media.Animation.Storyboard.SetTarget(scaleUpY, selectedRow);
                        System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleUpY, new PropertyPath("RenderTransform.ScaleY"));

                        System.Windows.Media.Animation.Storyboard.SetTarget(scaleDownX, selectedRow);
                        System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleDownX, new PropertyPath("RenderTransform.ScaleX"));

                        System.Windows.Media.Animation.Storyboard.SetTarget(scaleDownY, selectedRow);
                        System.Windows.Media.Animation.Storyboard.SetTargetProperty(scaleDownY, new PropertyPath("RenderTransform.ScaleY"));

                        storyboard.Children.Add(scaleUpX);
                        storyboard.Children.Add(scaleUpY);
                        storyboard.Children.Add(scaleDownX);
                        storyboard.Children.Add(scaleDownY);

                        storyboard.Begin();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in AnimateRowDoubleClick: {ex.Message}");
            }
        }

        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "CSV files (*.csv)|*.csv|Excel files (*.xlsx)|*.xlsx",
                    DefaultExt = "csv",
                    FileName = $"الفواتير_{DateTime.Now:yyyy-MM-dd}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // Simulate async export operation
                    await Task.Delay(100);
                    // TODO: Implement export functionality
                    _toastService.ShowSuccess("تم التصدير", "تم تصدير البيانات بنجاح");
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تصدير البيانات: {ex.Message}");
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // TODO: Implement print functionality
                _toastService.ShowInfo("قريباً", "ميزة الطباعة ستكون متاحة قريباً");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في الطباعة: {ex.Message}");
            }
        }

        private void QuickFilterUnpaid_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Clear search text
                SearchTextBox.Text = "";

                // Set status filter to unpaid
                StatusFilterComboBox.SelectedIndex = 1; // Unpaid option

                _toastService.ShowInfo("تم التطبيق", "تم عرض الفواتير غير المسددة فقط");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تطبيق الفلتر: {ex.Message}");
            }
        }

        private void QuickFilterPaid_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Clear search text
                SearchTextBox.Text = "";

                // Set status filter to paid
                StatusFilterComboBox.SelectedIndex = 3; // Paid option

                _toastService.ShowInfo("تم التطبيق", "تم عرض الفواتير المسددة فقط");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تطبيق الفلتر: {ex.Message}");
            }
        }

        private void ViewInvoiceAttachment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Invoice invoice)
                {
                    if (string.IsNullOrEmpty(invoice.AttachmentPath))
                    {
                        _toastService.ShowWarning("تنبيه", "لا يوجد مرفق للفاتورة");
                        return;
                    }

                    var fullPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Attachments", invoice.AttachmentPath);

                    if (File.Exists(fullPath))
                    {
                        try
                        {
                            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                            {
                                FileName = fullPath,
                                UseShellExecute = true
                            });
                            _toastService.ShowSuccess("تم فتح المرفق", "تم فتح مرفق الفاتورة بنجاح");
                        }
                        catch (Exception ex)
                        {
                            _toastService.ShowError("خطأ في فتح الملف", $"فشل في فتح المرفق: {ex.Message}");
                        }
                    }
                    else
                    {
                        _toastService.ShowError("ملف غير موجود", "مرفق الفاتورة غير موجود في المسار المحدد");
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في عرض مرفق الفاتورة: {ex.Message}");
            }
        }

        private async void ViewReceiptAttachment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Invoice invoice)
                {
                    // Get payments for this invoice
                    var payments = await _paymentService.GetPaymentsByInvoiceAsync(invoice.Id);
                    var paymentsWithAttachments = payments.Where(p => !string.IsNullOrEmpty(p.AttachmentPath)).ToList();

                    if (!paymentsWithAttachments.Any())
                    {
                        _toastService.ShowWarning("تنبيه", "لا يوجد مرفقات للوصولات");
                        return;
                    }

                    if (paymentsWithAttachments.Count == 1)
                    {
                        // Open single attachment directly
                        var payment = paymentsWithAttachments.First();
                        OpenPaymentAttachment(payment);
                    }
                    else
                    {
                        // Show selection dialog for multiple attachments
                        ShowPaymentAttachmentsDialog(paymentsWithAttachments);
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في عرض مرفقات الوصولات: {ex.Message}");
            }
        }

        private void OpenPaymentAttachment(Payment payment)
        {
            try
            {
                if (string.IsNullOrEmpty(payment.AttachmentPath))
                {
                    _toastService.ShowWarning("تنبيه", "لا يوجد مرفق لهذا الوصل");
                    return;
                }

                Utils.FileHelper.OpenAttachment(payment.AttachmentPath, "Payments");
                _toastService.ShowSuccess("تم فتح المرفق", $"تم فتح مرفق الوصل رقم {payment.ReceiptNumber}");
            }
            catch (FileNotFoundException)
            {
                _toastService.ShowError("ملف غير موجود", "مرفق الوصل غير موجود في المسار المحدد");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في فتح الملف", $"فشل في فتح مرفق الوصل: {ex.Message}");
            }
        }

        private void ShowPaymentAttachmentsDialog(List<Payment> payments)
        {
            try
            {
                var dialog = new Window
                {
                    Title = "اختيار مرفق الوصل",
                    Width = 400,
                    Height = 300,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = Window.GetWindow(this),
                    ResizeMode = ResizeMode.NoResize
                };

                var stackPanel = new StackPanel { Margin = new Thickness(20) };

                stackPanel.Children.Add(new TextBlock
                {
                    Text = "اختر الوصل المطلوب عرض مرفقه:",
                    FontSize = 14,
                    FontWeight = FontWeights.SemiBold,
                    Margin = new Thickness(0, 0, 0, 15)
                });

                foreach (var payment in payments)
                {
                    var button = new Button
                    {
                        Content = $"وصل رقم {payment.ReceiptNumber} - {payment.Amount:N0} د.ع - {payment.PaymentDate:dd/MM/yyyy}",
                        Margin = new Thickness(0, 0, 0, 10),
                        Padding = new Thickness(15, 8, 15, 8),
                        HorizontalAlignment = HorizontalAlignment.Stretch,
                        Tag = payment
                    };

                    button.Click += (s, e) =>
                    {
                        if (s is Button btn && btn.Tag is Payment selectedPayment)
                        {
                            OpenPaymentAttachment(selectedPayment);
                            dialog.Close();
                        }
                    };

                    stackPanel.Children.Add(button);
                }

                dialog.Content = stackPanel;
                dialog.ShowDialog();
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في عرض قائمة المرفقات: {ex.Message}");
            }
        }

        private void ApplyFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            var filteredInvoices = AllInvoices.AsEnumerable();

            // فلتر حسب الحالة
            if (StatusFilterComboBox.SelectedValue != null)
            {
                var selectedStatus = (InvoiceStatus)StatusFilterComboBox.SelectedValue;
                filteredInvoices = filteredInvoices.Where(i => i.Status == selectedStatus);
            }

            // فلتر حسب التاريخ
            if (FromDatePicker.SelectedDate.HasValue)
            {
                filteredInvoices = filteredInvoices.Where(i => i.InvoiceDate >= FromDatePicker.SelectedDate.Value);
            }

            if (ToDatePicker.SelectedDate.HasValue)
            {
                filteredInvoices = filteredInvoices.Where(i => i.InvoiceDate <= ToDatePicker.SelectedDate.Value);
            }

            Invoices.Clear();
            foreach (var invoice in filteredInvoices)
            {
                Invoices.Add(invoice);
            }

            UpdateStatistics();
        }

        private void EditInvoice_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Invoice invoice)
            {
                EditInvoice(invoice);
            }
            else if (sender is Button btn && btn.Tag is Invoice taggedInvoice)
            {
                EditInvoice(taggedInvoice);
            }
        }

        private void ViewInvoiceDetails_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Invoice invoice)
            {
                ViewInvoiceDetails(invoice);
            }
            else if (sender is Button btn && btn.Tag is Invoice taggedInvoice)
            {
                ViewInvoiceDetails(taggedInvoice);
            }
        }

        private void EditInvoice(Invoice invoice)
        {
            try
            {
                var editInvoiceWindow = new Windows.AddEditInvoiceWindow(invoice);
                editInvoiceWindow.Owner = Window.GetWindow(this);
                if (editInvoiceWindow.ShowDialog() == true)
                {
                    // تحديث القائمة بعد تعديل الفاتورة
                    Dispatcher.BeginInvoke(new Action(async () => await LoadInvoicesAsync()));
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في فتح نموذج تعديل الفاتورة: {ex.Message}");
            }
        }

        private void ViewInvoiceDetails(Invoice invoice)
        {
            try
            {
                // استخدام نافذة التعديل لعرض التفاصيل
                var detailsWindow = new Windows.AddEditInvoiceWindow(invoice);
                detailsWindow.Owner = Window.GetWindow(this);
                detailsWindow.WindowStartupLocation = WindowStartupLocation.CenterOwner;
                detailsWindow.Title = $"تفاصيل الفاتورة - {invoice.InvoiceNumber}";

                // تعطيل أزرار الحفظ والتعديل لجعلها للقراءة فقط
                detailsWindow.Loaded += (s, e) =>
                {
                    if (detailsWindow.FindName("SaveButton") is Button saveBtn)
                        saveBtn.Visibility = Visibility.Collapsed;
                    if (detailsWindow.FindName("CancelButton") is Button cancelBtn)
                        cancelBtn.Content = "إغلاق";
                };

                detailsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في عرض تفاصيل الفاتورة: {ex.Message}");
            }
        }

        private void DeleteInvoice_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Invoice invoice)
            {
                DeleteInvoice(invoice);
            }
            else if (sender is Button btn && btn.Tag is Invoice taggedInvoice)
            {
                DeleteInvoice(taggedInvoice);
            }
        }

        private async void DeleteInvoice(Invoice invoice)
        {
            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف الفاتورة رقم {invoice.InvoiceNumber}؟\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await _invoiceService.DeleteInvoiceAsync(invoice.Id);
                    _toastService.ShowSuccess("تم الحذف", "تم حذف الفاتورة بنجاح");
                    await LoadInvoicesAsync();
                }
                catch (Exception ex)
                {
                    _toastService.ShowError("خطأ", $"فشل في حذف الفاتورة: {ex.Message}");
                }
            }
        }
    }
}
