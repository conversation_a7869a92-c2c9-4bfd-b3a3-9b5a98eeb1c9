using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Pages
{
    public partial class ChartsPage : UserControl, INotifyPropertyChanged
    {
        private readonly IInvoiceService _invoiceService;
        private readonly IPaymentService _paymentService;
        private readonly ISupplierService _supplierService;
        private readonly IToastService _toastService;
        private readonly IDashboardService _dashboardService;

        private string _currentChartType = "monthly";
        private bool _isLoading = false;

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
                UpdateLoadingVisibility();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public ChartsPage(
            IInvoiceService invoiceService,
            IPaymentService paymentService,
            ISupplierService supplierService,
            IToastService toastService,
            IDashboardService dashboardService)
        {
            InitializeComponent();
            
            _invoiceService = invoiceService;
            _paymentService = paymentService;
            _supplierService = supplierService;
            _toastService = toastService;
            _dashboardService = dashboardService;

            DataContext = this;
            Loaded += ChartsPage_Loaded;
            Unloaded += ChartsPage_Unloaded;
        }

        private async void ChartsPage_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                await Task.Delay(100);
                await LoadDefaultChart();
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في التحميل", $"فشل في تحميل صفحة المخططات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in ChartsPage_Loaded: {ex.Message}");
            }
        }

        private async Task LoadDefaultChart()
        {
            try
            {
                IsLoading = true;
                _currentChartType = "monthly";
                await ShowMonthlyTrends();
                UpdateButtonSelection("monthly");
                _toastService?.ShowInfo("تم التحميل", "تم تحميل المخططات البيانية بنجاح");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل المخطط: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in LoadDefaultChart: {ex.Message}");
                ShowErrorMessage("فشل في تحميل البيانات");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void UpdateLoadingVisibility()
        {
            // Loading indicator will be handled by the UI directly
        }

        #region Button Click Handlers

        private async void MonthlyTrendsButton_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (IsLoading) return;
            
            try
            {
                IsLoading = true;
                _currentChartType = "monthly";
                await ShowMonthlyTrends();
                UpdateButtonSelection("monthly");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل الاتجاهات الشهرية: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in MonthlyTrendsButton_Click: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void StatusDistributionButton_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (IsLoading) return;
            
            try
            {
                IsLoading = true;
                _currentChartType = "status";
                await ShowStatusDistribution();
                UpdateButtonSelection("status");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل توزيع الحالات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in StatusDistributionButton_Click: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void SupplierAnalysisButton_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (IsLoading) return;
            
            try
            {
                IsLoading = true;
                _currentChartType = "supplier";
                await ShowSupplierAnalysis();
                UpdateButtonSelection("supplier");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل تحليل الموردين: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in SupplierAnalysisButton_Click: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void PaymentAnalysisButton_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (IsLoading) return;
            
            try
            {
                IsLoading = true;
                _currentChartType = "payment";
                await ShowPaymentAnalysis();
                UpdateButtonSelection("payment");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل تحليل المدفوعات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in PaymentAnalysisButton_Click: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region Chart Display Methods

        private async Task ShowMonthlyTrends()
        {
            try
            {
                // Load all invoices for chart data
                var allInvoices = await _invoiceService.GetAllInvoicesAsync();
                var invoicesList = allInvoices.ToList();

                if (!invoicesList.Any())
                {
                    _toastService?.ShowWarning("لا توجد بيانات", "لا توجد فواتير لعرض الاتجاهات الشهرية");
                    ShowErrorMessage("لا توجد بيانات للعرض");
                    return;
                }

                await Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        ShowChartData();
                        UpdateStatisticsCards(invoicesList);
                        CreateMonthlyTrendChart(invoicesList);
                        System.Diagnostics.Debug.WriteLine($"Monthly trends displayed with {invoicesList.Count} invoices");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error in ShowMonthlyTrends UI update: {ex.Message}");
                        ShowErrorMessage("خطأ في عرض البيانات");
                    }
                });

                _toastService?.ShowSuccess("تم التحديث", "تم تحميل الاتجاهات الشهرية بنجاح");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل الاتجاهات الشهرية: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in ShowMonthlyTrends: {ex.Message}");
                ShowErrorMessage("فشل في تحميل البيانات");
            }
        }

        private async Task ShowStatusDistribution()
        {
            try
            {
                var invoices = await _invoiceService.GetAllInvoicesAsync();
                
                var unpaidCount = invoices.Count(i => i.Status == InvoiceStatus.Unpaid);
                var partiallyPaidCount = invoices.Count(i => i.Status == InvoiceStatus.PartiallyPaid);
                var paidCount = invoices.Count(i => i.Status == InvoiceStatus.Paid);

                await Dispatcher.InvokeAsync(async () =>
                {
                    try
                    {
                        ShowChartData();
                        UpdateStatisticsCards(invoices.ToList());

                        // Small delay to ensure layout is updated
                        await Task.Delay(100);

                        CreateStatusDistributionDisplay(unpaidCount, partiallyPaidCount, paidCount);
                        System.Diagnostics.Debug.WriteLine($"Status distribution displayed: Unpaid={unpaidCount}, Partial={partiallyPaidCount}, Paid={paidCount}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error in ShowStatusDistribution UI update: {ex.Message}");
                        ShowErrorMessage("خطأ في عرض البيانات");
                    }
                });
                
                _toastService?.ShowSuccess("تم التحديث", "تم تحميل توزيع الحالات بنجاح");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل توزيع الحالات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in ShowStatusDistribution: {ex.Message}");
                ShowErrorMessage("فشل في تحميل البيانات");
            }
        }

        private async Task ShowSupplierAnalysis()
        {
            try
            {
                var invoices = await _invoiceService.GetAllInvoicesAsync();

                var supplierData = invoices
                    .GroupBy(i => i.Supplier.Name)
                    .Select(g => new
                    {
                        SupplierName = g.Key,
                        TotalAmount = g.Sum(i => i.Amount),
                        InvoiceCount = g.Count(),
                        PaidAmount = g.Sum(i => i.PaidAmount)
                    })
                    .OrderByDescending(s => s.TotalAmount)
                    .Take(10)
                    .ToList();

                await Dispatcher.InvokeAsync(async () =>
                {
                    try
                    {
                        ShowChartData();
                        UpdateStatisticsCards(invoices.ToList());

                        // Small delay to ensure layout is updated
                        await Task.Delay(100);

                        CreateTopSuppliersChart(invoices.ToList());
                        CreateInvoiceStatusChart(invoices.ToList());
                        System.Diagnostics.Debug.WriteLine($"Supplier analysis displayed with {supplierData.Count} suppliers");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error in ShowSupplierAnalysis UI update: {ex.Message}");
                        ShowErrorMessage("خطأ في عرض البيانات");
                    }
                });

                _toastService?.ShowSuccess("تم التحديث", "تم تحميل تحليل الموردين بنجاح");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحليل الموردين: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in ShowSupplierAnalysis: {ex.Message}");
                ShowErrorMessage("فشل في تحميل البيانات");
            }
        }

        private async Task ShowPaymentAnalysis()
        {
            try
            {
                var allInvoices = await _invoiceService.GetAllInvoicesAsync();
                var allPayments = await _paymentService.GetAllPaymentsAsync();
                var invoicesList = allInvoices.ToList();
                var paymentsList = allPayments.ToList();

                if (!invoicesList.Any() && !paymentsList.Any())
                {
                    _toastService?.ShowWarning("لا توجد بيانات", "لا توجد بيانات لعرض تحليل المدفوعات");
                    ShowErrorMessage("لا توجد بيانات للعرض");
                    return;
                }

                await Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        ShowChartData();
                        UpdateStatisticsCards(invoicesList);
                        CreatePaymentMethodsChart(paymentsList);
                        CreateMonthlyTrendChart(invoicesList);
                        System.Diagnostics.Debug.WriteLine($"Payment analysis displayed with {paymentsList.Count} payments");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error in ShowPaymentAnalysis UI update: {ex.Message}");
                        ShowErrorMessage("خطأ في عرض البيانات");
                    }
                });

                _toastService?.ShowSuccess("تم التحديث", "تم تحميل تحليل المدفوعات بنجاح");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحليل المدفوعات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in ShowPaymentAnalysis: {ex.Message}");
                ShowErrorMessage("فشل في تحميل البيانات");
            }
        }

        #endregion

        #region Helper Methods

        private async void RefreshChartsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Console.WriteLine("ChartsPage: RefreshChartsButton_Click triggered");
                RefreshChartsButton.IsEnabled = false;
                IsLoading = true;

                _toastService?.ShowInfo("تحديث", "جاري تحديث المخططات البيانية...");

                await LoadChartsData();

                _toastService?.ShowSuccess("تم التحديث", "تم تحديث جميع المخططات البيانية بنجاح");
                System.Console.WriteLine("ChartsPage: Charts refreshed successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"ChartsPage: Exception in RefreshChartsButton_Click: {ex.Message}");
                _toastService?.ShowError("خطأ", $"فشل في تحديث المخططات: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
                RefreshChartsButton.IsEnabled = true;
            }
        }

        private async void ExportChartsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Console.WriteLine("ChartsPage: ExportChartsButton_Click triggered");
                ExportChartsButton.IsEnabled = false;

                _toastService?.ShowInfo("تصدير", "جاري تحضير ملف التصدير...");

                // محاكاة عملية التصدير
                await Task.Delay(2000);

                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "تصدير المخططات البيانية",
                    Filter = "PDF Files (*.pdf)|*.pdf|Excel Files (*.xlsx)|*.xlsx|Image Files (*.png)|*.png|All Files (*.*)|*.*",
                    DefaultExt = "pdf",
                    FileName = $"المخططات_البيانية_{DateTime.Now:yyyy-MM-dd}.pdf"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // هنا يتم التصدير الفعلي
                    _toastService?.ShowSuccess("تم التصدير", $"تم حفظ المخططات في: {saveFileDialog.FileName}");
                    System.Console.WriteLine($"ChartsPage: Charts exported to: {saveFileDialog.FileName}");
                }
                else
                {
                    _toastService?.ShowInfo("تم الإلغاء", "تم إلغاء عملية التصدير");
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"ChartsPage: Exception in ExportChartsButton_Click: {ex.Message}");
                _toastService?.ShowError("خطأ", $"فشل في تصدير المخططات: {ex.Message}");
            }
            finally
            {
                ExportChartsButton.IsEnabled = true;
            }
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Console.WriteLine("ChartsPage: SettingsButton_Click triggered");

                _toastService?.ShowInfo("إعدادات", "ستتم إضافة المزيد من الإعدادات قريباً");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"ChartsPage: Exception in SettingsButton_Click: {ex.Message}");
                _toastService?.ShowError("خطأ", $"فشل في فتح الإعدادات: {ex.Message}");
            }
        }

        private async Task LoadChartsData()
        {
            try
            {
                // Load all data
                var invoicesTask = _invoiceService.GetAllInvoicesAsync();
                var paymentsTask = _paymentService.GetAllPaymentsAsync();
                var suppliersTask = _supplierService.GetAllSuppliersAsync();

                await Task.WhenAll(invoicesTask, paymentsTask, suppliersTask);

                var allInvoices = invoicesTask.Result.ToList();
                var allPayments = paymentsTask.Result.ToList();
                var allSuppliers = suppliersTask.Result.ToList();

                // Update statistics cards
                UpdateStatisticsCards(allInvoices);

                // Update charts
                CreateMonthlyTrendChart(allInvoices);
                CreatePaymentMethodsChart(allPayments);
                CreateTopSuppliersChart(allInvoices);
                CreateInvoiceStatusChart(allInvoices);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading charts data: {ex.Message}");
                throw;
            }
        }

        private void UpdateStatisticsCards(List<Invoice> invoices)
        {
            try
            {
                if (invoices?.Any() == true)
                {
                    // Total Invoices
                    TotalInvoicesText.Text = invoices.Count.ToString("N0");

                    // Total Amount
                    var totalAmount = invoices.Sum(i => i.Amount);
                    TotalAmountText.Text = $"{totalAmount:N0} د.ع";

                    // Paid Amount
                    var paidAmount = invoices.Sum(i => i.PaidAmount);
                    PaidAmountText.Text = $"{paidAmount:N0} د.ع";

                    // Outstanding Amount
                    var outstandingAmount = totalAmount - paidAmount;
                    OutstandingAmountText.Text = $"{outstandingAmount:N0} د.ع";

                    // Calculate percentage changes (mock data for now)
                    InvoicesChangeText.Text = "+12% من الشهر الماضي";
                    AmountChangeText.Text = "+8% من الشهر الماضي";
                    PaidChangeText.Text = "+15% من الشهر الماضي";
                    OutstandingChangeText.Text = "-5% من الشهر الماضي";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating statistics: {ex.Message}");
            }
        }

        private void CreateMonthlyTrendChart(List<Invoice> invoices)
        {
            try
            {
                MonthlyTrendChart.Children.Clear();

                if (invoices?.Any() != true) return;

                var monthlyData = invoices
                    .GroupBy(i => new { Year = i.InvoiceDate.Year, Month = i.InvoiceDate.Month })
                    .Select(g => new
                    {
                        Date = new DateTime(g.Key.Year, g.Key.Month, 1),
                        Amount = g.Sum(i => i.Amount),
                        Count = g.Count()
                    })
                    .OrderBy(x => x.Date)
                    .Take(12)
                    .ToList();

                if (!monthlyData.Any()) return;

                var maxAmount = monthlyData.Max(x => x.Amount);
                var chartWidth = MonthlyTrendChart.ActualWidth > 0 ? MonthlyTrendChart.ActualWidth - 40 : 600;
                var chartHeight = MonthlyTrendChart.ActualHeight > 0 ? MonthlyTrendChart.ActualHeight - 40 : 260;
                // Draw grid lines
                for (int i = 0; i <= 5; i++)
                {
                    var y = 20 + (chartHeight - 40) * i / 5;
                    var line = new System.Windows.Shapes.Line
                    {
                        X1 = 20,
                        Y1 = y,
                        X2 = chartWidth - 20,
                        Y2 = y,
                        Stroke = new SolidColorBrush(Color.FromRgb(229, 231, 235)),
                        StrokeThickness = 1
                    };
                    MonthlyTrendChart.Children.Add(line);
                }

                // Draw chart line
                var points = new System.Windows.Media.PointCollection();
                for (int i = 0; i < monthlyData.Count; i++)
                {
                    var x = 20 + (chartWidth - 40) * i / Math.Max(monthlyData.Count - 1, 1);
                    var y = chartHeight - 20 - (chartHeight - 40) * (double)monthlyData[i].Amount / (double)maxAmount;
                    points.Add(new Point(x, y));
                }

                var polyline = new System.Windows.Shapes.Polyline
                {
                    Points = points,
                    Stroke = new SolidColorBrush(Color.FromRgb(102, 126, 234)),
                    StrokeThickness = 3,
                    Fill = Brushes.Transparent
                };
                MonthlyTrendChart.Children.Add(polyline);

                // Add data points
                for (int i = 0; i < points.Count; i++)
                {
                    var point = points[i];
                    var circle = new System.Windows.Shapes.Ellipse
                    {
                        Width = 8,
                        Height = 8,
                        Fill = new SolidColorBrush(Color.FromRgb(102, 126, 234)),
                        Stroke = Brushes.White,
                        StrokeThickness = 2
                    };
                    Canvas.SetLeft(circle, point.X - 4);
                    Canvas.SetTop(circle, point.Y - 4);
                    MonthlyTrendChart.Children.Add(circle);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating monthly trend chart: {ex.Message}");
            }
        }

        private void CreatePaymentMethodsChart(List<Payment> payments)
        {
            try
            {
                PaymentMethodsChart.Children.Clear();

                if (payments?.Any() != true) return;

                var paymentMethods = payments
                    .GroupBy(p => p.Method)
                    .Select(g => new
                    {
                        Method = g.Key.ToString(),
                        Amount = g.Sum(p => p.Amount),
                        Count = g.Count()
                    })
                    .ToList();

                if (!paymentMethods.Any()) return;

                var total = paymentMethods.Sum(x => x.Amount);
                var centerX = PaymentMethodsChart.ActualWidth > 0 ? PaymentMethodsChart.ActualWidth / 2 : 150;
                var centerY = PaymentMethodsChart.ActualHeight > 0 ? PaymentMethodsChart.ActualHeight / 2 : 150;
                var radius = Math.Min(centerX, centerY) - 40;

                var colors = new[]
                {
                    Color.FromRgb(16, 185, 129),  // Green
                    Color.FromRgb(59, 130, 246),  // Blue
                    Color.FromRgb(245, 158, 11),  // Yellow
                    Color.FromRgb(239, 68, 68)    // Red
                };

                double startAngle = 0;
                for (int i = 0; i < paymentMethods.Count; i++)
                {
                    var method = paymentMethods[i];
                    var percentage = (double)method.Amount / (double)total;
                    var angle = percentage * 360;

                    if (angle > 0)
                    {
                        var path = CreatePieSlice(centerX, centerY, radius, startAngle, angle, colors[i % colors.Length]);
                        PaymentMethodsChart.Children.Add(path);
                        startAngle += angle;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating payment methods chart: {ex.Message}");
            }
        }

        private System.Windows.Shapes.Path CreatePieSlice(double centerX, double centerY, double radius, double startAngle, double angle, Color color)
        {
            var startAngleRad = startAngle * Math.PI / 180;
            var endAngleRad = (startAngle + angle) * Math.PI / 180;

            var x1 = centerX + radius * Math.Cos(startAngleRad);
            var y1 = centerY + radius * Math.Sin(startAngleRad);
            var x2 = centerX + radius * Math.Cos(endAngleRad);
            var y2 = centerY + radius * Math.Sin(endAngleRad);

            var largeArc = angle > 180 ? 1 : 0;

            var pathData = $"M {centerX},{centerY} L {x1},{y1} A {radius},{radius} 0 {largeArc},1 {x2},{y2} Z";

            return new System.Windows.Shapes.Path
            {
                Data = Geometry.Parse(pathData),
                Fill = new SolidColorBrush(color),
                Stroke = Brushes.White,
                StrokeThickness = 2
            };
        }

        private void CreateTopSuppliersChart(List<Invoice> invoices)
        {
            try
            {
                TopSuppliersPanel.Children.Clear();

                if (invoices?.Any() != true)
                {
                    System.Diagnostics.Debug.WriteLine("No invoices found for top suppliers chart");
                    return;
                }

                var topSuppliers = invoices
                    .Where(i => i.Supplier != null)
                    .GroupBy(i => i.Supplier.Name)
                    .Select(g => new
                    {
                        Name = g.Key,
                        Amount = g.Sum(i => i.Amount),
                        Count = g.Count()
                    })
                    .OrderByDescending(x => x.Amount)
                    .Take(5)
                    .ToList();

                if (!topSuppliers.Any())
                {
                    System.Diagnostics.Debug.WriteLine("No top suppliers data found");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"Found {topSuppliers.Count} top suppliers");

                var maxAmount = topSuppliers.Max(x => x.Amount);

                foreach (var supplier in topSuppliers)
                {
                    var percentage = (double)supplier.Amount / (double)maxAmount;

                    var border = new Border
                    {
                        Background = Brushes.White,
                        CornerRadius = new CornerRadius(8),
                        Padding = new Thickness(16, 12, 16, 12),
                        Margin = new Thickness(0, 0, 0, 12)
                    };
                    border.Effect = new System.Windows.Media.Effects.DropShadowEffect
                    {
                        Color = Color.FromRgb(139, 92, 246),
                        Opacity = 0.1,
                        BlurRadius = 10,
                        ShadowDepth = 3
                    };

                    var grid = new Grid();
                    grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
                    grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                    var headerPanel = new StackPanel { Orientation = Orientation.Horizontal };
                    Grid.SetRow(headerPanel, 0);

                    var nameText = new TextBlock
                    {
                        Text = supplier.Name,
                        FontSize = 14,
                        FontWeight = FontWeights.SemiBold,
                        Foreground = new SolidColorBrush(Color.FromRgb(45, 55, 72)),
                        VerticalAlignment = VerticalAlignment.Center
                    };

                    var amountText = new TextBlock
                    {
                        Text = $"{supplier.Amount:N0} د.ع",
                        FontSize = 14,
                        FontWeight = FontWeights.Bold,
                        Foreground = new SolidColorBrush(Color.FromRgb(139, 92, 246)),
                        HorizontalAlignment = HorizontalAlignment.Right,
                        VerticalAlignment = VerticalAlignment.Center,
                        Margin = new Thickness(8, 0, 0, 0)
                    };

                    headerPanel.Children.Add(nameText);
                    headerPanel.Children.Add(amountText);

                    var progressBorder = new Border
                    {
                        Background = new SolidColorBrush(Color.FromRgb(243, 244, 246)),
                        CornerRadius = new CornerRadius(4),
                        Height = 8,
                        Margin = new Thickness(0, 8, 0, 0)
                    };
                    Grid.SetRow(progressBorder, 1);

                    var progressBar = new Border
                    {
                        Background = new SolidColorBrush(Color.FromRgb(139, 92, 246)),
                        CornerRadius = new CornerRadius(4),
                        Height = 8,
                        Width = 200 * percentage,
                        HorizontalAlignment = HorizontalAlignment.Left
                    };

                    progressBorder.Child = progressBar;

                    grid.Children.Add(headerPanel);
                    grid.Children.Add(progressBorder);
                    border.Child = grid;

                    TopSuppliersPanel.Children.Add(border);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating top suppliers chart: {ex.Message}");
            }
        }
        private void CreateInvoiceStatusChart(List<Invoice> invoices)
        {
            try
            {
                InvoiceStatusChart.Children.Clear();

                if (invoices?.Any() != true)
                {
                    System.Diagnostics.Debug.WriteLine("No invoices found for status chart");
                    return;
                }

                var statusData = invoices
                    .GroupBy(i => i.Status)
                    .Select(g => new
                    {
                        Status = g.Key,
                        Count = g.Count(),
                        StatusText = g.Key switch
                        {
                            InvoiceStatus.Unpaid => "غير مسددة",
                            InvoiceStatus.PartiallyPaid => "مسددة جزئياً",
                            InvoiceStatus.Paid => "مسددة",
                            InvoiceStatus.PaidWithDiscount => "مسددة بخصم",
                            _ => "غير محدد"
                        }
                    })
                    .ToList();

                if (!statusData.Any())
                {
                    System.Diagnostics.Debug.WriteLine("No status data found");
                    return;
                }

                var total = statusData.Sum(x => x.Count);

                // Force update layout to get actual size
                InvoiceStatusChart.UpdateLayout();

                var chartWidth = InvoiceStatusChart.ActualWidth > 0 ? InvoiceStatusChart.ActualWidth : 280;
                var chartHeight = InvoiceStatusChart.ActualHeight > 0 ? InvoiceStatusChart.ActualHeight : 250;
                var centerX = chartWidth / 2;
                var centerY = chartHeight / 2;
                var radius = Math.Min(centerX, centerY) - 40;

                System.Diagnostics.Debug.WriteLine($"Chart size: {chartWidth}x{chartHeight}, Center: ({centerX},{centerY}), Radius: {radius}");

                var statusColors = new Dictionary<InvoiceStatus, Color>
                {
                    { InvoiceStatus.Unpaid, Color.FromRgb(239, 68, 68) },
                    { InvoiceStatus.PartiallyPaid, Color.FromRgb(245, 158, 11) },
                    { InvoiceStatus.Paid, Color.FromRgb(16, 185, 129) },
                    { InvoiceStatus.PaidWithDiscount, Color.FromRgb(139, 92, 246) }
                };

                double startAngle = 0;
                foreach (var status in statusData)
                {
                    var percentage = (double)status.Count / (double)total;
                    var angle = percentage * 360;

                    if (angle > 0 && statusColors.ContainsKey(status.Status))
                    {
                        var path = CreatePieSlice(centerX, centerY, radius, startAngle, angle, statusColors[status.Status]);
                        InvoiceStatusChart.Children.Add(path);
                        startAngle += angle;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating invoice status chart: {ex.Message}");
            }
        }

        private void ChartsPage_Unloaded(object sender, RoutedEventArgs e)
        {
            // Cleanup resources
        }

        private void UpdateButtonSelection(string selectedType)
        {
            _currentChartType = selectedType;
        }

        private void ShowErrorMessage(string message)
        {
            _toastService?.ShowError("خطأ", message);
        }

        private void ShowChartData()
        {
            // Chart data is shown automatically
        }

        private void CreateMonthlyTrendsDisplay(dynamic monthlyTrends)
        {
            try
            {
                if (MonthlyTrendChart == null) return;

                MonthlyTrendChart.Children.Clear();

                var data = monthlyTrends as IEnumerable<dynamic>;
                if (data == null || !data.Any()) return;

                var dataList = data.ToList();
                var chartWidth = MonthlyTrendChart.ActualWidth > 0 ? MonthlyTrendChart.ActualWidth : 600;
                var chartHeight = MonthlyTrendChart.ActualHeight > 0 ? MonthlyTrendChart.ActualHeight : 250;

                if (chartWidth <= 0 || chartHeight <= 0) return;

                var margin = 40;
                var plotWidth = chartWidth - (margin * 2);
                var plotHeight = chartHeight - (margin * 2);

                // Get max value for scaling
                var maxValue = dataList.Max(d => (decimal)d.Amount);
                if (maxValue <= 0) maxValue = 1;

                // Draw axes
                DrawAxes(MonthlyTrendChart, margin, plotWidth, plotHeight);

                // Draw data points and lines
                for (int i = 0; i < dataList.Count; i++)
                {
                    var item = dataList[i];
                    var x = margin + (i * plotWidth / Math.Max(dataList.Count - 1, 1));
                    var y = margin + plotHeight - ((double)((decimal)item.Amount / maxValue) * plotHeight);

                    // Draw point
                    var point = new System.Windows.Shapes.Ellipse
                    {
                        Width = 8,
                        Height = 8,
                        Fill = new SolidColorBrush(Color.FromRgb(103, 126, 234)),
                        Stroke = new SolidColorBrush(Colors.White),
                        StrokeThickness = 2
                    };
                    Canvas.SetLeft(point, x - 4);
                    Canvas.SetTop(point, (double)y - 4);
                    MonthlyTrendChart.Children.Add(point);

                    // Draw line to next point
                    if (i < dataList.Count - 1)
                    {
                        var nextItem = dataList[i + 1];
                        var nextX = margin + ((i + 1) * plotWidth / Math.Max(dataList.Count - 1, 1));
                        var nextY = margin + plotHeight - ((double)((decimal)nextItem.Amount / maxValue) * plotHeight);

                        var line = new System.Windows.Shapes.Line
                        {
                            X1 = x,
                            Y1 = (double)y,
                            X2 = nextX,
                            Y2 = (double)nextY,
                            Stroke = new SolidColorBrush(Color.FromRgb(103, 126, 234)),
                            StrokeThickness = 3
                        };
                        MonthlyTrendChart.Children.Add(line);
                    }

                    // Add month label
                    var monthText = new TextBlock
                    {
                        Text = ((DateTime)item.Month).ToString("MMM"),
                        FontSize = 10,
                        Foreground = new SolidColorBrush(Color.FromRgb(107, 114, 128))
                    };
                    Canvas.SetLeft(monthText, x - 15);
                    Canvas.SetTop(monthText, chartHeight - margin + 5);
                    MonthlyTrendChart.Children.Add(monthText);
                }

                System.Diagnostics.Debug.WriteLine($"Monthly trends chart created with {dataList.Count} points");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating monthly trends display: {ex.Message}");
            }
        }

        private void CreateStatusDistributionDisplay(int unpaidCount, int partiallyPaidCount, int paidCount)
        {
            try
            {
                if (InvoiceStatusChart == null) return;

                InvoiceStatusChart.Children.Clear();

                var total = unpaidCount + partiallyPaidCount + paidCount;
                if (total == 0) return;

                var chartWidth = InvoiceStatusChart.ActualWidth > 0 ? InvoiceStatusChart.ActualWidth : 300;
                var chartHeight = InvoiceStatusChart.ActualHeight > 0 ? InvoiceStatusChart.ActualHeight : 250;

                var centerX = chartWidth / 2;
                var centerY = chartHeight / 2;
                var radius = Math.Min(centerX, centerY) - 20;

                if (radius <= 0) return;

                // Colors for different statuses
                var colors = new[]
                {
                    Color.FromRgb(244, 67, 54),   // Red for unpaid
                    Color.FromRgb(255, 152, 0),   // Orange for partially paid
                    Color.FromRgb(76, 175, 80)    // Green for paid
                };

                var values = new[] { unpaidCount, partiallyPaidCount, paidCount };
                var labels = new[] { "غير مسددة", "تسديد جزئي", "مسددة" };

                double startAngle = 0;

                for (int i = 0; i < values.Length; i++)
                {
                    if (values[i] == 0) continue;

                    var percentage = (double)values[i] / total;
                    var sweepAngle = percentage * 360;

                    // Create pie slice
                    var slice = CreatePieSlice(centerX, centerY, radius, startAngle, sweepAngle, colors[i]);
                    InvoiceStatusChart.Children.Add(slice);

                    // Add label
                    var labelAngle = startAngle + sweepAngle / 2;
                    var labelRadius = radius * 0.7;
                    var labelX = centerX + labelRadius * Math.Cos(labelAngle * Math.PI / 180);
                    var labelY = centerY + labelRadius * Math.Sin(labelAngle * Math.PI / 180);

                    var label = new TextBlock
                    {
                        Text = $"{labels[i]}\n{values[i]}",
                        FontSize = 10,
                        FontWeight = FontWeights.Bold,
                        Foreground = new SolidColorBrush(Colors.White),
                        TextAlignment = TextAlignment.Center,
                        HorizontalAlignment = HorizontalAlignment.Center
                    };

                    Canvas.SetLeft(label, labelX - 25);
                    Canvas.SetTop(label, labelY - 10);
                    InvoiceStatusChart.Children.Add(label);

                    startAngle += sweepAngle;
                }

                System.Diagnostics.Debug.WriteLine($"Status distribution chart created with {total} total items");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating status distribution display: {ex.Message}");
            }
        }

        private void CreateSupplierAnalysisDisplay(dynamic supplierData)
        {
            // This method is replaced by CreateTopSuppliersChart and CreateInvoiceStatusChart
            System.Diagnostics.Debug.WriteLine("CreateSupplierAnalysisDisplay called - using existing chart methods");
        }

        private void CreatePaymentAnalysisDisplay(dynamic monthlyPayments)
        {
            // This method is replaced by CreatePaymentMethodsChart and CreateMonthlyTrendChart
            System.Diagnostics.Debug.WriteLine("CreatePaymentAnalysisDisplay called - using existing chart methods");
        }

        private void DrawAxes(Canvas canvas, double margin, double plotWidth, double plotHeight)
        {
            // X-axis
            var xAxis = new System.Windows.Shapes.Line
            {
                X1 = margin,
                Y1 = margin + plotHeight,
                X2 = margin + plotWidth,
                Y2 = margin + plotHeight,
                Stroke = new SolidColorBrush(Color.FromRgb(229, 231, 235)),
                StrokeThickness = 1
            };
            canvas.Children.Add(xAxis);

            // Y-axis
            var yAxis = new System.Windows.Shapes.Line
            {
                X1 = margin,
                Y1 = margin,
                X2 = margin,
                Y2 = margin + plotHeight,
                Stroke = new SolidColorBrush(Color.FromRgb(229, 231, 235)),
                StrokeThickness = 1
            };
            canvas.Children.Add(yAxis);
        }



        #endregion
    }
}
