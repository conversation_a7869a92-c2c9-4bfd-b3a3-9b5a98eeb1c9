using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using HR_InvoiceArchiver.Data;
using HR_InvoiceArchiver.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// خدمة المزامنة التلقائية مع التخزين السحابي
    /// </summary>
    public class CloudSyncService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<CloudSyncService> _logger;
        private readonly ISettingsService _settingsService;
        private System.Timers.Timer? _syncTimer;
        private bool _isSyncing = false;
        private readonly SemaphoreSlim _syncSemaphore = new(1, 1);

        // إحصائيات المزامنة
        public int TotalFilesSynced { get; private set; }
        public int FailedSyncs { get; private set; }
        public DateTime? LastSyncTime { get; private set; }
        public TimeSpan? LastSyncDuration { get; private set; }

        // أحداث المزامنة
        public event EventHandler<SyncProgressEventArgs>? SyncProgress;
        public event EventHandler<SyncCompletedEventArgs>? SyncCompleted;
        public event EventHandler<SyncErrorEventArgs>? SyncError;

        public CloudSyncService(
            IServiceProvider serviceProvider,
            ILogger<CloudSyncService> logger,
            ISettingsService settingsService)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _settingsService = settingsService;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("بدء خدمة المزامنة السحابية");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var settings = await _settingsService.LoadSettingsAsync();
                    
                    if (settings.EnableCloudSync && settings.AutoSyncEnabled)
                    {
                        await InitializeSyncTimer(settings.SyncIntervalMinutes);
                    }
                    else
                    {
                        StopSyncTimer();
                    }

                    // انتظار لمدة دقيقة قبل التحقق مرة أخرى
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "خطأ في خدمة المزامنة السحابية");
                    await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
                }
            }
        }

        /// <summary>
        /// تهيئة مؤقت المزامنة
        /// </summary>
        private async Task InitializeSyncTimer(int intervalMinutes)
        {
            if (_syncTimer != null)
            {
                _syncTimer.Stop();
                _syncTimer.Dispose();
            }

            _syncTimer = new System.Timers.Timer(TimeSpan.FromMinutes(intervalMinutes).TotalMilliseconds);
            _syncTimer.Elapsed += async (sender, e) => await PerformSyncAsync();
            _syncTimer.AutoReset = true;
            _syncTimer.Start();

            _logger.LogInformation($"تم تهيئة مؤقت المزامنة: كل {intervalMinutes} دقيقة");

            // تنفيذ مزامنة فورية عند البدء
            _ = Task.Run(async () => await PerformSyncAsync());

            // إضافة await لحل التحذير
            await Task.CompletedTask;
        }

        /// <summary>
        /// إيقاف مؤقت المزامنة
        /// </summary>
        private void StopSyncTimer()
        {
            if (_syncTimer != null)
            {
                _syncTimer.Stop();
                _syncTimer.Dispose();
                _syncTimer = null;
                _logger.LogInformation("تم إيقاف مؤقت المزامنة");
            }
        }

        /// <summary>
        /// تنفيذ عملية المزامنة
        /// </summary>
        public async Task<SyncResult> PerformSyncAsync()
        {
            if (!await _syncSemaphore.WaitAsync(1000))
            {
                _logger.LogWarning("عملية مزامنة أخرى قيد التنفيذ");
                return new SyncResult { Success = false, Errors = new List<string> { "عملية مزامنة أخرى قيد التنفيذ" } };
            }

            try
            {
                _isSyncing = true;
                var startTime = DateTime.Now;
                var result = new SyncResult();

                _logger.LogInformation("بدء عملية المزامنة السحابية");

                using var scope = _serviceProvider.CreateScope();
                var cloudService = scope.ServiceProvider.GetRequiredService<ICloudStorageService>();
                var dbContext = scope.ServiceProvider.GetRequiredService<DatabaseContext>();

                // التحقق من الاتصال
                if (!await cloudService.IsConnectedAsync())
                {
                    _logger.LogWarning("لا يوجد اتصال بالتخزين السحابي");
                    result.Errors.Add("لا يوجد اتصال بالتخزين السحابي");
                    result.Success = false;
                    return result;
                }

                // مزامنة الفواتير
                var invoiceSyncResult = await SyncInvoicesAsync(cloudService, dbContext);
                result.FilesProcessed += invoiceSyncResult.FilesProcessed;
                result.FilesUploaded += invoiceSyncResult.FilesUploaded;
                result.Errors.AddRange(invoiceSyncResult.Errors);

                // مزامنة المدفوعات
                var paymentSyncResult = await SyncPaymentsAsync(cloudService, dbContext);
                result.FilesProcessed += paymentSyncResult.FilesProcessed;
                result.FilesUploaded += paymentSyncResult.FilesUploaded;
                result.Errors.AddRange(paymentSyncResult.Errors);

                result.Success = result.Errors.Count == 0;
                result.Duration = DateTime.Now - startTime;

                // تحديث الإحصائيات
                TotalFilesSynced += result.FilesUploaded;
                if (!result.Success) FailedSyncs++;
                LastSyncTime = DateTime.Now;
                LastSyncDuration = result.Duration;

                _logger.LogInformation($"انتهت عملية المزامنة: {result.FilesUploaded} ملف تم رفعه في {result.Duration.TotalSeconds:F1} ثانية");

                // إشعار بانتهاء المزامنة
                SyncCompleted?.Invoke(this, new SyncCompletedEventArgs
                {
                    Result = result,
                    TotalFiles = result.FilesProcessed,
                    SuccessfulFiles = result.FilesUploaded
                });

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في عملية المزامنة");
                
                FailedSyncs++;
                SyncError?.Invoke(this, new SyncErrorEventArgs
                {
                    Exception = ex,
                    Message = ex.Message
                });

                return new SyncResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message },
                    Duration = TimeSpan.Zero
                };
            }
            finally
            {
                _isSyncing = false;
                _syncSemaphore.Release();
            }
        }

        /// <summary>
        /// مزامنة الفواتير
        /// </summary>
        private async Task<SyncResult> SyncInvoicesAsync(ICloudStorageService cloudService, DatabaseContext dbContext)
        {
            var result = new SyncResult();
            
            try
            {
                var pendingInvoices = await dbContext.Invoices
                    .Where(i => i.SyncStatus == CloudSyncStatus.Pending && !string.IsNullOrEmpty(i.AttachmentPath))
                    .ToListAsync();

                result.FilesProcessed = pendingInvoices.Count;

                foreach (var invoice in pendingInvoices)
                {
                    try
                    {
                        // تحديث حالة المزامنة إلى "جاري المزامنة"
                        invoice.SyncStatus = CloudSyncStatus.Syncing;
                        await dbContext.SaveChangesAsync();

                        // رفع الملف مع معالجة أفضل للأخطاء
                        var fileName = $"Invoice_{invoice.Id}_{Path.GetFileName(invoice.AttachmentPath)}";
                        var cloudFileId = await cloudService.UploadFileAsync(
                            invoice.AttachmentPath!,
                            fileName,
                            "Invoices");

                        if (!string.IsNullOrEmpty(cloudFileId))
                        {
                            invoice.CloudFileId = cloudFileId;
                            invoice.SyncStatus = CloudSyncStatus.Synced;
                            invoice.LastSyncDate = DateTime.Now;
                            result.FilesUploaded++;

                            _logger.LogInformation($"تم رفع فاتورة {invoice.Id} بنجاح");
                        }
                        else
                        {
                            invoice.SyncStatus = CloudSyncStatus.Failed;
                            result.Errors.Add($"فشل في رفع فاتورة {invoice.Id}");
                        }

                        await dbContext.SaveChangesAsync();

                        // إشعار بالتقدم
                        SyncProgress?.Invoke(this, new SyncProgressEventArgs
                        {
                            CurrentFile = invoice.InvoiceNumber,
                            ProcessedFiles = result.FilesUploaded,
                            TotalFiles = result.FilesProcessed
                        });
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"خطأ في مزامنة فاتورة {invoice.Id}");
                        invoice.SyncStatus = CloudSyncStatus.Failed;
                        await dbContext.SaveChangesAsync();
                        result.Errors.Add($"خطأ في مزامنة فاتورة {invoice.Id}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في مزامنة الفواتير");
                result.Errors.Add($"خطأ في مزامنة الفواتير: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// مزامنة المدفوعات
        /// </summary>
        private async Task<SyncResult> SyncPaymentsAsync(ICloudStorageService cloudService, DatabaseContext dbContext)
        {
            var result = new SyncResult();
            
            try
            {
                var pendingPayments = await dbContext.Payments
                    .Where(p => p.SyncStatus == CloudSyncStatus.Pending && !string.IsNullOrEmpty(p.AttachmentPath))
                    .ToListAsync();

                result.FilesProcessed = pendingPayments.Count;

                foreach (var payment in pendingPayments)
                {
                    try
                    {
                        // تحديث حالة المزامنة إلى "جاري المزامنة"
                        payment.SyncStatus = CloudSyncStatus.Syncing;
                        await dbContext.SaveChangesAsync();

                        // رفع الملف
                        var cloudFileId = await cloudService.UploadFileAsync(
                            payment.AttachmentPath!,
                            $"Payment_{payment.Id}_{Path.GetFileName(payment.AttachmentPath)}",
                            "Payments");

                        if (!string.IsNullOrEmpty(cloudFileId))
                        {
                            payment.CloudFileId = cloudFileId;
                            payment.SyncStatus = CloudSyncStatus.Synced;
                            payment.LastSyncDate = DateTime.Now;
                            result.FilesUploaded++;

                            _logger.LogInformation($"تم رفع مدفوعة {payment.Id} بنجاح");
                        }
                        else
                        {
                            payment.SyncStatus = CloudSyncStatus.Failed;
                            result.Errors.Add($"فشل في رفع مدفوعة {payment.Id}");
                        }

                        await dbContext.SaveChangesAsync();

                        // إشعار بالتقدم
                        SyncProgress?.Invoke(this, new SyncProgressEventArgs
                        {
                            CurrentFile = $"Payment_{payment.Id}",
                            ProcessedFiles = result.FilesUploaded,
                            TotalFiles = result.FilesProcessed
                        });
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"خطأ في مزامنة مدفوعة {payment.Id}");
                        payment.SyncStatus = CloudSyncStatus.Failed;
                        await dbContext.SaveChangesAsync();
                        result.Errors.Add($"خطأ في مزامنة مدفوعة {payment.Id}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في مزامنة المدفوعات");
                result.Errors.Add($"خطأ في مزامنة المدفوعات: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// إيقاف الخدمة
        /// </summary>
        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("إيقاف خدمة المزامنة السحابية");
            
            StopSyncTimer();
            
            // انتظار انتهاء أي عملية مزامنة جارية
            if (_isSyncing)
            {
                _logger.LogInformation("انتظار انتهاء عملية المزامنة الجارية...");
                await _syncSemaphore.WaitAsync(cancellationToken);
                _syncSemaphore.Release();
            }

            await base.StopAsync(cancellationToken);
        }

        public override void Dispose()
        {
            StopSyncTimer();
            _syncSemaphore?.Dispose();
            base.Dispose();
        }
    }

    // فئات الأحداث
    public class SyncProgressEventArgs : EventArgs
    {
        public string CurrentFile { get; set; } = string.Empty;
        public int ProcessedFiles { get; set; }
        public int TotalFiles { get; set; }
        public double ProgressPercentage => TotalFiles > 0 ? (double)ProcessedFiles / TotalFiles * 100 : 0;
    }

    public class SyncCompletedEventArgs : EventArgs
    {
        public SyncResult Result { get; set; } = new();
        public int TotalFiles { get; set; }
        public int SuccessfulFiles { get; set; }
    }

    public class SyncErrorEventArgs : EventArgs
    {
        public Exception Exception { get; set; } = new();
        public string Message { get; set; } = string.Empty;
    }
}
