<UserControl x:Class="HR_InvoiceArchiver.Controls.CloudStorageControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="Transparent">

    <UserControl.Resources>
        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <!-- Header Style -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <!-- Info Item Style -->
        <Style x:Key="InfoItemStyle" TargetType="StackPanel">
            <Setter Property="Orientation" Value="Horizontal"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <!-- Icon Style -->
        <Style x:Key="IconStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="Margin" Value="0,0,15,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- Label Style -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#6C757D"/>
            <Setter Property="Margin" Value="0,0,0,3"/>
        </Style>

        <!-- Value Style -->
        <Style x:Key="ValueStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>

        <!-- Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,10,10,0"/>
        </Style>

        <!-- Status Card Style -->
        <Style x:Key="StatusCardStyle" TargetType="Border">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <StackPanel Margin="20">
            
            <!-- Header Card -->
            <materialDesign:Card Style="{StaticResource ModernCardStyle}">
                <StackPanel Margin="25">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                        <materialDesign:PackIcon Kind="CloudUpload" 
                                               Width="32" Height="32" 
                                               Foreground="#2196F3"
                                               VerticalAlignment="Center"/>
                        <TextBlock Text="التخزين السحابي - Google Drive" 
                                  Style="{StaticResource HeaderTextStyle}"
                                  Margin="15,0,0,0"
                                  VerticalAlignment="Center"/>
                    </StackPanel>
                    
                    <TextBlock Text="احفظ نسخة احتياطية آمنة من مرفقاتك في السحابة"
                              FontSize="14" 
                              Foreground="#6C757D"
                              TextWrapping="Wrap"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Credentials Setup Card -->
            <materialDesign:Card x:Name="CredentialsSetupCard" Style="{StaticResource ModernCardStyle}">
                <StackPanel Margin="25">
                    <TextBlock Text="إعداد ملف الاعتمادات" Style="{StaticResource HeaderTextStyle}"/>

                    <Border Style="{StaticResource StatusCardStyle}">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <materialDesign:PackIcon Kind="FileDocument"
                                                       Width="20" Height="20"
                                                       Foreground="#FF9800"
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="ملف credentials.json"
                                          FontSize="14" FontWeight="Bold"
                                          Margin="10,0,0,0"
                                          VerticalAlignment="Center"/>
                            </StackPanel>

                            <TextBlock Text="اختر ملف credentials.json الذي حصلت عليه من Google Cloud Console"
                                      FontSize="12" Foreground="#666"
                                      TextWrapping="Wrap"
                                      Margin="0,0,0,15"/>

                            <!-- File Path Display -->
                            <Border Background="#F5F5F5"
                                   CornerRadius="4"
                                   Padding="10"
                                   Margin="0,0,0,15">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Folder"
                                                           Width="16" Height="16"
                                                           Foreground="#666"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock x:Name="CredentialsPathText"
                                              Text="لم يتم اختيار ملف credentials.json"
                                              FontSize="12"
                                              Foreground="#666"
                                              Margin="8,0,0,0"
                                              VerticalAlignment="Center"
                                              TextTrimming="CharacterEllipsis"/>
                                </StackPanel>
                            </Border>

                            <!-- Action Buttons -->
                            <StackPanel Orientation="Horizontal">
                                <Button x:Name="BrowseCredentialsButton"
                                       Content="اختيار الملف"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#FF9800"
                                       Click="BrowseCredentialsButton_Click"/>

                                <Button x:Name="ResetCredentialsButton"
                                       Content="إعادة تعيين"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#F44336"
                                       Click="ResetCredentialsButton_Click"/>

                                <Button x:Name="DownloadGuideButton"
                                       Content="دليل التحميل"
                                       Style="{StaticResource ModernButtonStyle}"
                                       Background="#2196F3"
                                       Click="DownloadGuideButton_Click"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </materialDesign:Card>

            <!-- Connection Status Card -->
            <materialDesign:Card Style="{StaticResource ModernCardStyle}">
                <StackPanel Margin="25">
                    <TextBlock Text="حالة الاتصال" Style="{StaticResource HeaderTextStyle}"/>

                    <Border x:Name="StatusCard" Style="{StaticResource StatusCardStyle}">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                <materialDesign:PackIcon x:Name="StatusIcon"
                                                       Kind="CloudOff"
                                                       Width="24" Height="24"
                                                       Foreground="#F44336"
                                                       VerticalAlignment="Center"/>
                                <TextBlock x:Name="StatusText"
                                          Text="غير متصل"
                                          FontSize="16" FontWeight="Bold"
                                          Foreground="#F44336"
                                          Margin="10,0,0,0"
                                          VerticalAlignment="Center"/>
                            </StackPanel>

                            <TextBlock x:Name="StatusDescription"
                                      Text="يرجى اختيار ملف credentials.json أولاً"
                                      FontSize="13"
                                      Foreground="#6C757D"/>
                        </StackPanel>
                    </Border>

                    <!-- Credentials Management -->
                    <StackPanel x:Name="CredentialsManagementPanel" Orientation="Horizontal" Margin="0,10,0,0">
                        <Button x:Name="ResetCredentialsMainButton"
                               Content="🔄 إعادة تعيين الاعتمادات"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="#F44336"
                               FontSize="12"
                               Click="ResetCredentialsButton_Click"/>
                    </StackPanel>

                    <!-- Auto Connect Setting -->
                    <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                        <CheckBox x:Name="AutoConnectCheckBox"
                                 Content="الاتصال التلقائي عند فتح التطبيق"
                                 IsChecked="True"
                                 FontSize="12"
                                 Foreground="#6C757D"
                                 Checked="AutoConnectCheckBox_Changed"
                                 Unchecked="AutoConnectCheckBox_Changed"/>
                    </StackPanel>

                    <!-- User Info (Hidden initially) -->
                    <Border x:Name="UserInfoCard" Style="{StaticResource StatusCardStyle}" Visibility="Collapsed">
                        <StackPanel>
                            <TextBlock Text="معلومات الحساب" 
                                      FontSize="14" FontWeight="Bold"
                                      Margin="0,0,0,10"/>
                            
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Account" 
                                                       Width="20" Height="20" 
                                                       Foreground="#2196F3"
                                                       VerticalAlignment="Center"/>
                                <StackPanel Margin="10,0,0,0">
                                    <TextBlock x:Name="UserName" Text="" FontWeight="SemiBold"/>
                                    <TextBlock x:Name="UserEmail" Text="" FontSize="12" Foreground="#666"/>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- Action Buttons -->
                    <StackPanel Orientation="Horizontal" Margin="0,15,0,0">
                        <Button x:Name="ConnectButton" 
                               Content="ربط Google Drive"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="#4CAF50"
                               Click="ConnectButton_Click"/>
                        
                        <Button x:Name="DisconnectButton"
                               Content="قطع الاتصال"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="#F44336"
                               Visibility="Collapsed"
                               Click="DisconnectButton_Click"/>

                        <Button x:Name="ChangeCredentialsButton"
                               Content="تغيير الاعتمادات"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="#9C27B0"
                               Visibility="Collapsed"
                               Click="ChangeCredentialsButton_Click"/>

                        <Button x:Name="RefreshButton"
                               Content="تحديث"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="#FF9800"
                               Visibility="Collapsed"
                               Click="RefreshButton_Click"/>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>

            <!-- Storage Statistics Card -->
            <materialDesign:Card x:Name="StorageStatsCard" Style="{StaticResource ModernCardStyle}" Visibility="Collapsed">
                <StackPanel Margin="25">
                    <TextBlock Text="إحصائيات التخزين" Style="{StaticResource HeaderTextStyle}"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Total Files -->
                        <Border Grid.Column="0" Style="{StaticResource StatusCardStyle}" Margin="0,0,10,0">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="FileMultiple" 
                                                       Width="32" Height="32" 
                                                       Foreground="#2196F3"
                                                       HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TotalFilesText" 
                                          Text="0"
                                          FontSize="24" FontWeight="Bold"
                                          Foreground="#2196F3"
                                          HorizontalAlignment="Center"
                                          Margin="0,5,0,0"/>
                                <TextBlock Text="إجمالي الملفات"
                                          FontSize="12"
                                          Foreground="#6C757D"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- Total Size -->
                        <Border Grid.Column="1" Style="{StaticResource StatusCardStyle}" Margin="5,0,5,0">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="HardDisk" 
                                                       Width="32" Height="32" 
                                                       Foreground="#FF9800"
                                                       HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TotalSizeText" 
                                          Text="0 MB"
                                          FontSize="24" FontWeight="Bold"
                                          Foreground="#FF9800"
                                          HorizontalAlignment="Center"
                                          Margin="0,5,0,0"/>
                                <TextBlock Text="الحجم الإجمالي"
                                          FontSize="12"
                                          Foreground="#6C757D"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- Last Sync -->
                        <Border Grid.Column="2" Style="{StaticResource StatusCardStyle}" Margin="10,0,0,0">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="CloudSync" 
                                                       Width="32" Height="32" 
                                                       Foreground="#4CAF50"
                                                       HorizontalAlignment="Center"/>
                                <TextBlock x:Name="LastSyncText" 
                                          Text="--"
                                          FontSize="16" FontWeight="Bold"
                                          Foreground="#4CAF50"
                                          HorizontalAlignment="Center"
                                          TextWrapping="Wrap"
                                          Margin="0,5,0,0"/>
                                <TextBlock Text="آخر مزامنة"
                                          FontSize="12"
                                          Foreground="#6C757D"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Sync Controls Card -->
            <materialDesign:Card x:Name="SyncControlsCard" Style="{StaticResource ModernCardStyle}" Visibility="Collapsed">
                <StackPanel Margin="25">
                    <TextBlock Text="التحكم في المزامنة" Style="{StaticResource HeaderTextStyle}"/>

                    <!-- Sync Status -->
                    <Border x:Name="SyncStatusCard" Style="{StaticResource StatusCardStyle}">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                <materialDesign:PackIcon x:Name="SyncStatusIcon"
                                                       Kind="CloudSync"
                                                       Width="24" Height="24"
                                                       Foreground="#4CAF50"
                                                       VerticalAlignment="Center"/>
                                <TextBlock x:Name="SyncStatusText"
                                          Text="المزامنة التلقائية مفعلة"
                                          FontSize="16" FontWeight="Bold"
                                          Foreground="#4CAF50"
                                          Margin="10,0,0,0"
                                          VerticalAlignment="Center"/>
                            </StackPanel>

                            <TextBlock x:Name="SyncStatusDescription"
                                      Text="المزامنة التالية خلال 25 دقيقة"
                                      FontSize="13"
                                      Foreground="#6C757D"/>
                        </StackPanel>
                    </Border>

                    <!-- Sync Progress (Hidden initially) -->
                    <Border x:Name="SyncProgressCard" Style="{StaticResource StatusCardStyle}" Visibility="Collapsed">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                <materialDesign:PackIcon Kind="CloudUpload"
                                                       Width="20" Height="20"
                                                       Foreground="#2196F3"
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="جاري المزامنة..."
                                          FontSize="14" FontWeight="Bold"
                                          Margin="10,0,0,0"/>
                            </StackPanel>

                            <ProgressBar x:Name="SyncProgressBar"
                                        Height="8"
                                        Margin="0,5,0,10"
                                        Background="#E0E0E0"
                                        Foreground="#2196F3"/>

                            <StackPanel Orientation="Horizontal">
                                <TextBlock x:Name="CurrentFileText"
                                          Text="جاري معالجة: invoice_001.pdf"
                                          FontSize="12"
                                          Foreground="#666"/>
                                <TextBlock x:Name="ProgressText"
                                          Text="(3/10)"
                                          FontSize="12"
                                          Foreground="#666"
                                          Margin="10,0,0,0"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- Action Buttons -->
                    <StackPanel Orientation="Horizontal" Margin="0,15,0,0">
                        <Button x:Name="SyncNowButton"
                               Content="مزامنة الآن"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="#2196F3"
                               Click="SyncNowButton_Click"/>

                        <Button x:Name="UploadFileButton"
                               Content="رفع ملف"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="#4CAF50"
                               Click="UploadFileButton_Click"/>

                        <Button x:Name="PauseResumeButton"
                               Content="إيقاف مؤقت"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="#FF9800"
                               Click="PauseResumeButton_Click"/>

                        <Button x:Name="SettingsButton"
                               Content="الإعدادات"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="#9C27B0"
                               Click="SettingsButton_Click"/>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>

            <!-- Performance Stats Card -->
            <materialDesign:Card x:Name="PerformanceStatsCard" Style="{StaticResource ModernCardStyle}" Visibility="Collapsed">
                <StackPanel Margin="25">
                    <TextBlock Text="إحصائيات الأداء" Style="{StaticResource HeaderTextStyle}"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Success Rate -->
                        <Border Grid.Column="0" Style="{StaticResource StatusCardStyle}" Margin="0,0,10,0">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="CheckCircle"
                                                       Width="32" Height="32"
                                                       Foreground="#4CAF50"
                                                       HorizontalAlignment="Center"/>
                                <TextBlock x:Name="SuccessRateText"
                                          Text="98%"
                                          FontSize="24" FontWeight="Bold"
                                          Foreground="#4CAF50"
                                          HorizontalAlignment="Center"
                                          Margin="0,5,0,0"/>
                                <TextBlock Text="معدل النجاح"
                                          FontSize="12"
                                          Foreground="#6C757D"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- Upload Speed -->
                        <Border Grid.Column="1" Style="{StaticResource StatusCardStyle}" Margin="5,0,5,0">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="Speedometer"
                                                       Width="32" Height="32"
                                                       Foreground="#2196F3"
                                                       HorizontalAlignment="Center"/>
                                <TextBlock x:Name="UploadSpeedText"
                                          Text="1.2 MB/s"
                                          FontSize="20" FontWeight="Bold"
                                          Foreground="#2196F3"
                                          HorizontalAlignment="Center"
                                          Margin="0,5,0,0"/>
                                <TextBlock Text="سرعة الرفع"
                                          FontSize="12"
                                          Foreground="#6C757D"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- Total Operations -->
                        <Border Grid.Column="2" Style="{StaticResource StatusCardStyle}" Margin="10,0,0,0">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="Counter"
                                                       Width="32" Height="32"
                                                       Foreground="#FF9800"
                                                       HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TotalOperationsText"
                                          Text="156"
                                          FontSize="24" FontWeight="Bold"
                                          Foreground="#FF9800"
                                          HorizontalAlignment="Center"
                                          Margin="0,5,0,0"/>
                                <TextBlock Text="إجمالي العمليات"
                                          FontSize="12"
                                          Foreground="#6C757D"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Synced Files Card -->
            <materialDesign:Card x:Name="SyncedFilesCard" Style="{StaticResource ModernCardStyle}" Visibility="Collapsed">
                <StackPanel Margin="25">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="الملفات المتزامنة" Style="{StaticResource HeaderTextStyle}" Margin="0"/>
                        <Button x:Name="RefreshFilesButton"
                               Content="🔄"
                               Width="35" Height="35"
                               FontSize="14"
                               Margin="15,0,0,0"
                               Background="Transparent"
                               BorderThickness="1"
                               BorderBrush="#DDD"
                               Click="RefreshFilesButton_Click"
                               ToolTip="تحديث قائمة الملفات"/>

                        <Button x:Name="TestLoadFilesButton"
                               Content="📋"
                               Width="35" Height="35"
                               FontSize="14"
                               Margin="5,0,0,0"
                               Background="Transparent"
                               BorderThickness="1"
                               BorderBrush="#DDD"
                               Click="TestLoadFilesButton_Click"
                               ToolTip="تحميل ملفات تجريبية"/>
                        <Button x:Name="ExportReportButton"
                               Content="📊"
                               Width="35" Height="35"
                               FontSize="14"
                               Margin="10,0,0,0"
                               Background="Transparent"
                               BorderThickness="1"
                               BorderBrush="#DDD"
                               Click="ExportReportButton_Click"
                               ToolTip="تصدير تقرير الأداء"/>
                    </StackPanel>
                    
                    <Border Background="#F8F9FA" 
                           CornerRadius="8" 
                           Padding="15"
                           MaxHeight="300">
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <ListView x:Name="SyncedFilesList" 
                                     Background="Transparent"
                                     BorderThickness="0">
                                <ListView.View>
                                    <GridView>
                                        <GridViewColumn Header="اسم الملف" Width="250">
                                            <GridViewColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal">
                                                        <materialDesign:PackIcon Kind="FileDocument" 
                                                                               Width="16" Height="16" 
                                                                               Foreground="#2196F3"
                                                                               VerticalAlignment="Center"/>
                                                        <TextBlock Text="{Binding FileName}" 
                                                                  FontSize="12"
                                                                  Margin="8,0,0,0"
                                                                  VerticalAlignment="Center"
                                                                  ToolTip="{Binding FileName}"/>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </GridViewColumn.CellTemplate>
                                        </GridViewColumn>
                                        <GridViewColumn Header="الحجم" Width="100">
                                            <GridViewColumn.CellTemplate>
                                                <DataTemplate>
                                                    <TextBlock Text="{Binding FileSize}" 
                                                              FontSize="12"
                                                              HorizontalAlignment="Center"/>
                                                </DataTemplate>
                                            </GridViewColumn.CellTemplate>
                                        </GridViewColumn>
                                        <GridViewColumn Header="تاريخ الرفع" Width="150">
                                            <GridViewColumn.CellTemplate>
                                                <DataTemplate>
                                                    <TextBlock Text="{Binding UploadDate}" 
                                                              FontSize="12"
                                                              HorizontalAlignment="Center"/>
                                                </DataTemplate>
                                            </GridViewColumn.CellTemplate>
                                        </GridViewColumn>
                                        <GridViewColumn Header="الحالة" Width="100">
                                            <GridViewColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                        <materialDesign:PackIcon Kind="CheckCircle"
                                                                               Width="14" Height="14"
                                                                               Foreground="#4CAF50"
                                                                               VerticalAlignment="Center"/>
                                                        <TextBlock Text="متزامن"
                                                                  FontSize="11"
                                                                  Foreground="#4CAF50"
                                                                  Margin="5,0,0,0"
                                                                  VerticalAlignment="Center"/>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </GridViewColumn.CellTemplate>
                                        </GridViewColumn>
                                        <GridViewColumn Header="إجراءات" Width="180">
                                            <GridViewColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="5,3">
                                                        <StackPanel.Resources>
                                                            <!-- تأثير الظل للأزرار -->
                                                            <DropShadowEffect x:Key="ButtonShadow"
                                                                            Color="Gray"
                                                                            Direction="315"
                                                                            ShadowDepth="2"
                                                                            Opacity="0.3"/>
                                                        </StackPanel.Resources>
                                                        <!-- Download Button -->
                                                        <Button Width="34" Height="30"
                                                               Margin="3,0"
                                                               Background="#E3F2FD"
                                                               BorderBrush="#2196F3"
                                                               BorderThickness="1"
                                                               ToolTip="تحميل الملف"
                                                               Click="DownloadFileButton_Click"
                                                               Tag="{Binding FileName}"
                                                               Cursor="Hand"
                                                               Effect="{StaticResource ButtonShadow}">
                                                            <Button.Style>
                                                                <Style TargetType="Button">
                                                                    <Setter Property="Template">
                                                                        <Setter.Value>
                                                                            <ControlTemplate TargetType="Button">
                                                                                <Border Background="{TemplateBinding Background}"
                                                                                       BorderBrush="{TemplateBinding BorderBrush}"
                                                                                       BorderThickness="{TemplateBinding BorderThickness}"
                                                                                       CornerRadius="4"
                                                                                       Padding="2">
                                                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                                                                        <TextBlock Text="📥" FontSize="12" Margin="0,0,2,0"/>
                                                                                        <TextBlock Text="تحميل" FontSize="9" FontWeight="SemiBold" Foreground="#1976D2"/>
                                                                                    </StackPanel>
                                                                                </Border>
                                                                                <ControlTemplate.Triggers>
                                                                                    <Trigger Property="IsMouseOver" Value="True">
                                                                                        <Setter Property="Background" Value="#BBDEFB"/>
                                                                                        <Setter Property="BorderBrush" Value="#1976D2"/>
                                                                                    </Trigger>
                                                                                    <Trigger Property="IsPressed" Value="True">
                                                                                        <Setter Property="Background" Value="#90CAF9"/>
                                                                                    </Trigger>
                                                                                </ControlTemplate.Triggers>
                                                                            </ControlTemplate>
                                                                        </Setter.Value>
                                                                    </Setter>
                                                                </Style>
                                                            </Button.Style>
                                                        </Button>

                                                        <!-- Preview Button -->
                                                        <Button Width="34" Height="30"
                                                               Margin="3,0"
                                                               Background="#F3E5F5"
                                                               BorderBrush="#9C27B0"
                                                               BorderThickness="1"
                                                               ToolTip="معاينة الملف"
                                                               Click="PreviewFileButton_Click"
                                                               Tag="{Binding FileName}"
                                                               Cursor="Hand"
                                                               Effect="{StaticResource ButtonShadow}">
                                                            <Button.Style>
                                                                <Style TargetType="Button">
                                                                    <Setter Property="Template">
                                                                        <Setter.Value>
                                                                            <ControlTemplate TargetType="Button">
                                                                                <Border Background="{TemplateBinding Background}"
                                                                                       BorderBrush="{TemplateBinding BorderBrush}"
                                                                                       BorderThickness="{TemplateBinding BorderThickness}"
                                                                                       CornerRadius="4"
                                                                                       Padding="2">
                                                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                                                                        <TextBlock Text="👁️" FontSize="12" Margin="0,0,2,0"/>
                                                                                        <TextBlock Text="عرض" FontSize="9" FontWeight="SemiBold" Foreground="#7B1FA2"/>
                                                                                    </StackPanel>
                                                                                </Border>
                                                                                <ControlTemplate.Triggers>
                                                                                    <Trigger Property="IsMouseOver" Value="True">
                                                                                        <Setter Property="Background" Value="#E1BEE7"/>
                                                                                        <Setter Property="BorderBrush" Value="#7B1FA2"/>
                                                                                    </Trigger>
                                                                                    <Trigger Property="IsPressed" Value="True">
                                                                                        <Setter Property="Background" Value="#CE93D8"/>
                                                                                    </Trigger>
                                                                                </ControlTemplate.Triggers>
                                                                            </ControlTemplate>
                                                                        </Setter.Value>
                                                                    </Setter>
                                                                </Style>
                                                            </Button.Style>
                                                        </Button>

                                                        <!-- Delete Button -->
                                                        <Button Width="34" Height="30"
                                                               Margin="3,0"
                                                               Background="#FFEBEE"
                                                               BorderBrush="#F44336"
                                                               BorderThickness="1"
                                                               ToolTip="حذف الملف"
                                                               Click="DeleteFileButton_Click"
                                                               Tag="{Binding FileName}"
                                                               Cursor="Hand"
                                                               Effect="{StaticResource ButtonShadow}">
                                                            <Button.Style>
                                                                <Style TargetType="Button">
                                                                    <Setter Property="Template">
                                                                        <Setter.Value>
                                                                            <ControlTemplate TargetType="Button">
                                                                                <Border Background="{TemplateBinding Background}"
                                                                                       BorderBrush="{TemplateBinding BorderBrush}"
                                                                                       BorderThickness="{TemplateBinding BorderThickness}"
                                                                                       CornerRadius="4"
                                                                                       Padding="2">
                                                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                                                                        <TextBlock Text="🗑️" FontSize="12" Margin="0,0,2,0"/>
                                                                                        <TextBlock Text="حذف" FontSize="9" FontWeight="SemiBold" Foreground="#D32F2F"/>
                                                                                    </StackPanel>
                                                                                </Border>
                                                                                <ControlTemplate.Triggers>
                                                                                    <Trigger Property="IsMouseOver" Value="True">
                                                                                        <Setter Property="Background" Value="#FFCDD2"/>
                                                                                        <Setter Property="BorderBrush" Value="#D32F2F"/>
                                                                                    </Trigger>
                                                                                    <Trigger Property="IsPressed" Value="True">
                                                                                        <Setter Property="Background" Value="#EF9A9A"/>
                                                                                    </Trigger>
                                                                                </ControlTemplate.Triggers>
                                                                            </ControlTemplate>
                                                                        </Setter.Value>
                                                                    </Setter>
                                                                </Style>
                                                            </Button.Style>
                                                        </Button>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </GridViewColumn.CellTemplate>
                                        </GridViewColumn>
                                    </GridView>
                                </ListView.View>
                            </ListView>
                        </ScrollViewer>
                    </Border>
                </StackPanel>
            </materialDesign:Card>

        </StackPanel>
    </ScrollViewer>
</UserControl>
