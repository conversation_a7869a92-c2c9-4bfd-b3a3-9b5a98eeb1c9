using System;
using System.Windows;
using System.Windows.Controls;

namespace HR_InvoiceArchiver.Windows
{
    /// <summary>
    /// نافذة إعدادات التخزين السحابي
    /// </summary>
    public partial class CloudStorageSettingsWindow : Window
    {
        public CloudStorageSettingsWindow()
        {
            InitializeComponent();
            InitializeSettings();
            SetupEventHandlers();
        }

        /// <summary>
        /// تهيئة الإعدادات الافتراضية
        /// </summary>
        private void InitializeSettings()
        {
            try
            {
                // تحميل الإعدادات الحالية (يمكن تحسينها لاحقاً)
                EnableCloudSyncCheckBox.IsChecked = true;
                AutoSyncCheckBox.IsChecked = true;
                SyncIntervalSlider.Value = 30;
                MaxFileSizeSlider.Value = 50;
                EnableCompressionCheckBox.IsChecked = true;
                EnableEncryptionCheckBox.IsChecked = true;
                ShowSuccessNotificationsCheckBox.IsChecked = true;
                ShowErrorNotificationsCheckBox.IsChecked = true;
                ShowProgressNotificationsCheckBox.IsChecked = false;

                UpdateSliderTexts();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إعداد معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            SyncIntervalSlider.ValueChanged += SyncIntervalSlider_ValueChanged;
            MaxFileSizeSlider.ValueChanged += MaxFileSizeSlider_ValueChanged;
            EnableCloudSyncCheckBox.Checked += EnableCloudSyncCheckBox_CheckedChanged;
            EnableCloudSyncCheckBox.Unchecked += EnableCloudSyncCheckBox_CheckedChanged;
        }

        /// <summary>
        /// معالج تغيير قيمة فترة المزامنة
        /// </summary>
        private void SyncIntervalSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (SyncIntervalText != null)
            {
                var minutes = (int)e.NewValue;
                if (minutes < 60)
                {
                    SyncIntervalText.Text = $"{minutes} دقيقة";
                }
                else
                {
                    var hours = minutes / 60;
                    var remainingMinutes = minutes % 60;
                    if (remainingMinutes == 0)
                    {
                        SyncIntervalText.Text = $"{hours} ساعة";
                    }
                    else
                    {
                        SyncIntervalText.Text = $"{hours} ساعة و {remainingMinutes} دقيقة";
                    }
                }
            }
        }

        /// <summary>
        /// معالج تغيير قيمة الحد الأقصى لحجم الملف
        /// </summary>
        private void MaxFileSizeSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (MaxFileSizeText != null)
            {
                MaxFileSizeText.Text = $"{(int)e.NewValue} MB";
            }
        }

        /// <summary>
        /// معالج تغيير حالة تفعيل التخزين السحابي
        /// </summary>
        private void EnableCloudSyncCheckBox_CheckedChanged(object sender, RoutedEventArgs e)
        {
            var isEnabled = EnableCloudSyncCheckBox.IsChecked == true;
            
            // تفعيل/تعطيل الإعدادات المرتبطة
            AutoSyncCheckBox.IsEnabled = isEnabled;
            SyncIntervalSlider.IsEnabled = isEnabled && AutoSyncCheckBox.IsChecked == true;
            MaxFileSizeSlider.IsEnabled = isEnabled;
            EnableCompressionCheckBox.IsEnabled = isEnabled;
            ShowSuccessNotificationsCheckBox.IsEnabled = isEnabled;
            ShowErrorNotificationsCheckBox.IsEnabled = isEnabled;
            ShowProgressNotificationsCheckBox.IsEnabled = isEnabled;
        }

        /// <summary>
        /// تحديث نصوص المنزلقات
        /// </summary>
        private void UpdateSliderTexts()
        {
            SyncIntervalSlider_ValueChanged(SyncIntervalSlider, 
                new RoutedPropertyChangedEventArgs<double>(0, SyncIntervalSlider.Value));
            MaxFileSizeSlider_ValueChanged(MaxFileSizeSlider, 
                new RoutedPropertyChangedEventArgs<double>(0, MaxFileSizeSlider.Value));
        }

        /// <summary>
        /// معالج النقر على زر الحفظ
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة الإعدادات
                if (!ValidateSettings())
                {
                    return;
                }

                // حفظ الإعدادات (يمكن تحسينها لاحقاً)
                var settings = CollectSettings();
                SaveSettingsToStorage(settings);

                MessageBox.Show("تم حفظ الإعدادات بنجاح!", "نجح الحفظ", 
                    MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج النقر على زر الإلغاء
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// التحقق من صحة الإعدادات
        /// </summary>
        private bool ValidateSettings()
        {
            // التحقق من فترة المزامنة
            if (AutoSyncCheckBox.IsChecked == true && SyncIntervalSlider.Value < 5)
            {
                MessageBox.Show("فترة المزامنة يجب أن تكون 5 دقائق على الأقل", "خطأ في الإعدادات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // التحقق من حجم الملف
            if (MaxFileSizeSlider.Value < 1)
            {
                MessageBox.Show("الحد الأقصى لحجم الملف يجب أن يكون 1 ميجابايت على الأقل", "خطأ في الإعدادات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// جمع الإعدادات من الواجهة
        /// </summary>
        private CloudStorageSettings CollectSettings()
        {
            return new CloudStorageSettings
            {
                EnableCloudSync = EnableCloudSyncCheckBox.IsChecked == true,
                AutoSyncEnabled = AutoSyncCheckBox.IsChecked == true,
                SyncIntervalMinutes = (int)SyncIntervalSlider.Value,
                MaxFileSizeMB = (int)MaxFileSizeSlider.Value,
                EnableCompression = EnableCompressionCheckBox.IsChecked == true,
                EnableEncryption = EnableEncryptionCheckBox.IsChecked == true,
                ShowSuccessNotifications = ShowSuccessNotificationsCheckBox.IsChecked == true,
                ShowErrorNotifications = ShowErrorNotificationsCheckBox.IsChecked == true,
                ShowProgressNotifications = ShowProgressNotificationsCheckBox.IsChecked == true
            };
        }

        /// <summary>
        /// حفظ الإعدادات في التخزين
        /// </summary>
        private void SaveSettingsToStorage(CloudStorageSettings settings)
        {
            // TODO: تطبيق حفظ الإعدادات في قاعدة البيانات أو ملف التكوين
            // يمكن استخدام ISettingsService هنا
        }
    }

    /// <summary>
    /// فئة إعدادات التخزين السحابي
    /// </summary>
    public class CloudStorageSettings
    {
        public bool EnableCloudSync { get; set; }
        public bool AutoSyncEnabled { get; set; }
        public int SyncIntervalMinutes { get; set; }
        public int MaxFileSizeMB { get; set; }
        public bool EnableCompression { get; set; }
        public bool EnableEncryption { get; set; }
        public bool ShowSuccessNotifications { get; set; }
        public bool ShowErrorNotifications { get; set; }
        public bool ShowProgressNotifications { get; set; }
    }
}
