using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Windows
{
    /// <summary>
    /// نافذة إضافة/تعديل الموردين
    /// </summary>
    public partial class AddEditSupplierWindow : Window
    {
        private readonly ISupplierService _supplierService;
        private readonly IToastService _toastService;
        private Supplier? _currentSupplier;
        private bool _isEditMode;

        public bool IsSaved { get; private set; }
        public Supplier? SavedSupplier { get; private set; }

        public AddEditSupplierWindow(Supplier? supplier = null)
        {
            InitializeComponent();

            // الحصول على الخدمات
            _supplierService = App.ServiceProvider.GetRequiredService<ISupplierService>();
            _toastService = App.ServiceProvider.GetRequiredService<IToastService>();

            // تحديد وضع التحرير
            _currentSupplier = supplier;
            _isEditMode = supplier != null;

            // تحديث النصوص
            UpdateWindowTexts();

            // تحميل البيانات
            if (_isEditMode && _currentSupplier != null)
            {
                LoadSupplierData(_currentSupplier);
            }

            // التركيز على أول حقل
            Loaded += (s, e) => NameTextBox.Focus();
        }

        private void UpdateWindowTexts()
        {
            if (_isEditMode)
            {
                WindowTitleTextBlock.Text = "تعديل بيانات المورد";
                HeaderTitleTextBlock.Text = "تعديل بيانات المورد";
                HeaderDescriptionTextBlock.Text = "قم بتعديل بيانات المورد";
                SaveButtonText.Text = "حفظ التعديلات";
                SaveButtonIcon.Kind = MaterialDesignThemes.Wpf.PackIconKind.ContentSaveEdit;
            }
            else
            {
                WindowTitleTextBlock.Text = "إضافة مورد جديد";
                HeaderTitleTextBlock.Text = "إضافة مورد جديد";
                HeaderDescriptionTextBlock.Text = "قم بإدخال بيانات المورد الجديد";
                SaveButtonText.Text = "حفظ";
                SaveButtonIcon.Kind = MaterialDesignThemes.Wpf.PackIconKind.ContentSave;
            }
        }

        private void LoadSupplierData(Supplier supplier)
        {
            NameTextBox.Text = supplier.Name ?? string.Empty;
            ContactPersonTextBox.Text = supplier.ContactPerson ?? string.Empty;
            PhoneTextBox.Text = supplier.Phone ?? string.Empty;
            EmailTextBox.Text = supplier.Email ?? string.Empty;
            AddressTextBox.Text = supplier.Address ?? string.Empty;
            NotesTextBox.Text = supplier.Notes ?? string.Empty;
            TaxNumberTextBox.Text = supplier.TaxNumber ?? string.Empty;
            DescriptionTextBox.Text = supplier.Description ?? string.Empty;
        }

        private bool ValidateForm()
        {
            // إخفاء رسالة التحقق السابقة
            ValidationMessageBorder.Visibility = Visibility.Collapsed;

            // التحقق من اسم المورد
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                ShowValidationMessage("يرجى إدخال اسم المورد");
                NameTextBox.Focus();
                return false;
            }

            // التحقق من طول اسم المورد
            if (NameTextBox.Text.Length < 2)
            {
                ShowValidationMessage("اسم المورد يجب أن يكون أكثر من حرف واحد");
                NameTextBox.Focus();
                return false;
            }

            // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
            if (!string.IsNullOrWhiteSpace(EmailTextBox.Text))
            {
                try
                {
                    var addr = new System.Net.Mail.MailAddress(EmailTextBox.Text);
                    if (addr.Address != EmailTextBox.Text)
                    {
                        ShowValidationMessage("يرجى إدخال بريد إلكتروني صحيح");
                        EmailTextBox.Focus();
                        return false;
                    }
                }
                catch
                {
                    ShowValidationMessage("يرجى إدخال بريد إلكتروني صحيح");
                    EmailTextBox.Focus();
                    return false;
                }
            }

            // التحقق من رقم الهاتف إذا تم إدخاله
            if (!string.IsNullOrWhiteSpace(PhoneTextBox.Text))
            {
                if (PhoneTextBox.Text.Length < 7)
                {
                    ShowValidationMessage("رقم الهاتف يجب أن يكون على الأقل 7 أرقام");
                    PhoneTextBox.Focus();
                    return false;
                }
            }

            return true;
        }

        private void ShowValidationMessage(string message)
        {
            ValidationMessageTextBlock.Text = message;
            ValidationMessageBorder.Visibility = Visibility.Visible;
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;

            try
            {
                ShowLoading(true);
                SaveButton.IsEnabled = false;

                var supplier = CreateSupplierFromInput();

                if (_isEditMode)
                {
                    await _supplierService.UpdateSupplierAsync(supplier);
                    _toastService.ShowSuccess("تم التحديث بنجاح", $"تم تحديث بيانات المورد '{supplier.Name}' بنجاح");
                }
                else
                {
                    supplier = await _supplierService.CreateSupplierAsync(supplier);
                    _toastService.ShowSuccess("تم الحفظ بنجاح", $"تم إضافة المورد '{supplier.Name}' بنجاح");
                }

                IsSaved = true;
                SavedSupplier = supplier;
                DialogResult = true;
                Close();
            }
            catch (ArgumentException ex)
            {
                // أخطاء التحقق من البيانات
                _toastService.ShowError("خطأ في البيانات", ex.Message);
                ShowValidationMessage(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                // أخطاء العمليات
                _toastService.ShowError("خطأ في العملية", ex.Message);
                ShowValidationMessage(ex.Message);
            }
            catch (Exception ex)
            {
                string errorMessage = ex.Message;

                // معالجة خاصة لأخطاء قاعدة البيانات
                if (ex.InnerException?.Message?.Contains("UNIQUE constraint failed") == true)
                {
                    if (ex.InnerException.Message.Contains("Name"))
                    {
                        errorMessage = "اسم المورد موجود مسبقاً. يرجى استخدام اسم مختلف.";
                    }
                    else
                    {
                        errorMessage = "يوجد تضارب في البيانات. يرجى التحقق من البيانات المدخلة.";
                    }
                }
                else if (ex.InnerException != null)
                {
                    errorMessage += $"\nالتفاصيل: {ex.InnerException.Message}";
                }

                _toastService.ShowError("خطأ في حفظ المورد", errorMessage);
                ShowValidationMessage(errorMessage);
            }
            finally
            {
                ShowLoading(false);
                SaveButton.IsEnabled = true;
            }
        }

        private Supplier CreateSupplierFromInput()
        {
            return new Supplier
            {
                Id = _isEditMode ? _currentSupplier!.Id : 0,
                Name = NameTextBox.Text.Trim(),
                ContactPerson = string.IsNullOrWhiteSpace(ContactPersonTextBox.Text) ? null : ContactPersonTextBox.Text.Trim(),
                Phone = string.IsNullOrWhiteSpace(PhoneTextBox.Text) ? null : PhoneTextBox.Text.Trim(),
                Email = string.IsNullOrWhiteSpace(EmailTextBox.Text) ? null : EmailTextBox.Text.Trim(),
                Address = string.IsNullOrWhiteSpace(AddressTextBox.Text) ? null : AddressTextBox.Text.Trim(),
                Notes = string.IsNullOrWhiteSpace(NotesTextBox.Text) ? null : NotesTextBox.Text.Trim(),
                TaxNumber = string.IsNullOrWhiteSpace(TaxNumberTextBox.Text) ? null : TaxNumberTextBox.Text.Trim(),
                Description = string.IsNullOrWhiteSpace(DescriptionTextBox.Text) ? null : DescriptionTextBox.Text.Trim(),
                CreatedDate = _isEditMode ? _currentSupplier!.CreatedDate : DateTime.Now,
                UpdatedDate = DateTime.Now,
                IsActive = true
            };
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                DragMove();
            }
        }

        private void ShowLoading(bool isLoading)
        {
            LoadingGrid.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
            SaveButton.IsEnabled = !isLoading;
            CancelButton.IsEnabled = !isLoading;
        }

        private void ScrollViewer_PreviewMouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (sender is System.Windows.Controls.ScrollViewer scrollViewer)
            {
                // تحديد مقدار السكرول بناءً على سرعة العجلة (أبطأ بكثير من الافتراضي)
                double scrollAmount = e.Delta > 0 ? -30 : 30; // مقدار ثابت وبطيء

                // تطبيق السكرول بسلاسة
                double newOffset = scrollViewer.VerticalOffset + scrollAmount;
                newOffset = Math.Max(0, Math.Min(newOffset, scrollViewer.ScrollableHeight));

                scrollViewer.ScrollToVerticalOffset(newOffset);
                e.Handled = true; // منع السكرول الافتراضي
            }
        }
    }
}
