<UserControl x:Class="HR_InvoiceArchiver.Controls.SupplierFormControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="Transparent">

    <UserControl.Resources>
        <!-- Modern Gradient Brushes -->
        <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#10B981" Offset="0"/>
            <GradientStop Color="#059669" Offset="1"/>
        </LinearGradientBrush>

        <!-- Enhanced Slide In Animation -->
        <Storyboard x:Key="SlideInAnimation">
            <DoubleAnimation Storyboard.TargetName="MainCard"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                           From="400" To="0" Duration="0:0:0.6">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut" Amplitude="0.4"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="MainCard"
                           Storyboard.TargetProperty="Opacity"
                           From="0" To="1" Duration="0:0:0.4"/>
        </Storyboard>

        <!-- Enhanced Slide Out Animation -->
        <Storyboard x:Key="SlideOutAnimation" Completed="SlideOutAnimation_Completed">
            <DoubleAnimation Storyboard.TargetName="MainCard"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                           From="0" To="400" Duration="0:0:0.4">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseIn" Amplitude="0.3"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="MainCard"
                           Storyboard.TargetProperty="Opacity"
                           From="1" To="0" Duration="0:0:0.3"/>
        </Storyboard>

        <!-- Save Success Animation -->
        <Storyboard x:Key="SaveSuccessAnimation">
            <DoubleAnimation Storyboard.TargetName="MainCard"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleX)"
                           From="1" To="1.05" Duration="0:0:0.2" AutoReverse="True"/>
            <DoubleAnimation Storyboard.TargetName="MainCard"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleY)"
                           From="1" To="1.05" Duration="0:0:0.2" AutoReverse="True"/>
        </Storyboard>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#40000000" BlurRadius="25" ShadowDepth="10" Direction="270"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Enhanced TextBox Style -->
        <Style x:Key="EnhancedTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="Padding" Value="16,16,16,8"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="#667eea"/>
            <Setter Property="materialDesign:TextFieldAssist.RippleOnFocusEnabled" Value="True"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.75"/>
        </Style>
    </UserControl.Resources>

    <!-- Main Container with Shadow and Rounded Corners -->
    <Border Background="White"
            CornerRadius="15"
            Margin="10">
        <Border.Effect>
            <DropShadowEffect Color="#40000000"
                            BlurRadius="30"
                            ShadowDepth="15"
                            Direction="270"
                            Opacity="0.3"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Custom Title Bar -->
            <Border Grid.Row="0"
                    Background="#F8F9FF"
                    CornerRadius="15,15,0,0"
                    Height="45"
                    MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Window Title -->
                    <StackPanel Grid.Column="0"
                              Orientation="Horizontal"
                              VerticalAlignment="Center"
                              Margin="20,0,0,0">
                        <materialDesign:PackIcon Kind="AccountGroup"
                                               Width="20" Height="20"
                                               Foreground="#667eea"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>
                        <TextBlock x:Name="FormTitleTextBlock"
                                 Text="إضافة مورد جديد"
                                 FontSize="14"
                                 FontWeight="Medium"
                                 Foreground="#2D3748"
                                 VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- Window Controls -->
                    <StackPanel Grid.Column="2"
                              Orientation="Horizontal"
                              VerticalAlignment="Center"
                              Margin="0,0,10,0">
                        <!-- Close Button -->
                        <Button x:Name="CloseButton"
                              Width="30" Height="30"
                              Background="Transparent"
                              BorderThickness="0"
                              Click="CloseButton_Click"
                              ToolTip="إغلاق">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                      CornerRadius="5">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                    VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#FFE5E5"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                            <materialDesign:PackIcon Kind="Close"
                                                   Width="16" Height="16"
                                                   Foreground="#DC2626"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>
            <Grid Grid.Row="2" Margin="25">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Form Section -->
                <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto" Margin="0,10,0,10">
                    <StackPanel>
                        <!-- Section 1: المعلومات الأساسية -->
                        <Border Background="#F8F9FA" CornerRadius="12" Padding="20" Margin="0,0,0,25">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                    <materialDesign:PackIcon Kind="AccountGroup" Width="24" Height="24"
                                                           Foreground="#667eea"
                                                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Text="المعلومات الأساسية"
                                             FontSize="18" FontWeight="Bold"
                                             Foreground="#2D3748" VerticalAlignment="Center"/>
                                </StackPanel>

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="20"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- اسم المورد -->
                                    <TextBox x:Name="NameTextBox"
                                             Grid.Row="0" Grid.Column="0"
                                             materialDesign:HintAssist.Hint="🏢 اسم المورد *"
                                             Style="{StaticResource EnhancedTextBoxStyle}"
                                             Margin="0,0,0,20"/>

                                    <!-- الشخص المسؤول -->
                                    <TextBox x:Name="ContactPersonTextBox"
                                             Grid.Row="0" Grid.Column="2"
                                             materialDesign:HintAssist.Hint="👤 الشخص المسؤول"
                                             Style="{StaticResource EnhancedTextBoxStyle}"
                                             Margin="0,0,0,20"/>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- Section 2: معلومات الاتصال -->
                        <Border Background="#F0FDF4" CornerRadius="12" Padding="20" Margin="0,0,0,25">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                    <materialDesign:PackIcon Kind="Phone" Width="24" Height="24"
                                                           Foreground="#10B981"
                                                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Text="معلومات الاتصال"
                                             FontSize="18" FontWeight="Bold"
                                             Foreground="#2D3748" VerticalAlignment="Center"/>
                                </StackPanel>

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="20"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- رقم الهاتف -->
                                    <TextBox x:Name="PhoneTextBox"
                                             Grid.Row="0" Grid.Column="0"
                                             materialDesign:HintAssist.Hint="📱 رقم الهاتف"
                                             Style="{StaticResource EnhancedTextBoxStyle}"
                                             Margin="0,0,0,20"/>

                                    <!-- البريد الإلكتروني -->
                                    <TextBox x:Name="EmailTextBox"
                                             Grid.Row="0" Grid.Column="2"
                                             materialDesign:HintAssist.Hint="📧 البريد الإلكتروني"
                                             Style="{StaticResource EnhancedTextBoxStyle}"
                                             Margin="0,0,0,20"/>

                                    <!-- العنوان -->
                                    <TextBox x:Name="AddressTextBox"
                                             Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="3"
                                             materialDesign:HintAssist.Hint="📍 العنوان الكامل"
                                             Style="{StaticResource EnhancedTextBoxStyle}"
                                             Height="100"
                                             TextWrapping="Wrap"
                                             AcceptsReturn="True"
                                             VerticalScrollBarVisibility="Auto"
                                             VerticalContentAlignment="Top"
                                             Margin="0,0,0,0"/>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- Section 3: الملاحظات -->
                        <Border Background="#F0F9FF" CornerRadius="12" Padding="20">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                    <materialDesign:PackIcon Kind="NoteText" Width="24" Height="24"
                                                           Foreground="#0EA5E9"
                                                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Text="ملاحظات إضافية"
                                             FontSize="18" FontWeight="Bold"
                                             Foreground="#2D3748" VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- الملاحظات -->
                                <TextBox x:Name="NotesTextBox"
                                         materialDesign:HintAssist.Hint="📝 ملاحظات إضافية"
                                         Style="{StaticResource EnhancedTextBoxStyle}"
                                         Height="100"
                                         TextWrapping="Wrap"
                                         AcceptsReturn="True"
                                         VerticalScrollBarVisibility="Auto"
                                         VerticalContentAlignment="Top"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
                <!-- Action Buttons Section -->
                <Border Grid.Row="1"
                        Background="#F8F9FF"
                        CornerRadius="0,0,15,15"
                        Padding="25,15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- رسالة التحقق -->
                        <Border Grid.Column="0"
                                Background="#FEF2F2"
                                CornerRadius="8"
                                Padding="15,10"
                                Visibility="Collapsed"
                                x:Name="ValidationMessageBorder">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AlertCircle"
                                                       Width="20" Height="20"
                                                       Foreground="#DC2626"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,10,0"/>
                                <TextBlock x:Name="ValidationMessageTextBlock"
                                           Foreground="#DC2626"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           VerticalAlignment="Center"
                                           TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>

                        <!-- زر الحذف -->
                        <Button x:Name="DeleteButton"
                                Grid.Column="1"
                                Content="🗑️ حذف"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                materialDesign:ButtonAssist.CornerRadius="10"
                                Height="45"
                                Width="120"
                                FontSize="14"
                                FontWeight="Medium"
                                Margin="15,0,0,0"
                                BorderBrush="#DC2626"
                                Foreground="#DC2626"
                                Visibility="Collapsed"
                                Click="DeleteButton_Click"/>

                        <!-- زر الإلغاء -->
                        <Button x:Name="CancelButton"
                                Grid.Column="2"
                                Content="❌ إلغاء"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                materialDesign:ButtonAssist.CornerRadius="10"
                                Height="45"
                                Width="120"
                                FontSize="14"
                                FontWeight="Medium"
                                Margin="15,0,0,0"
                                BorderBrush="#6B7280"
                                Foreground="#6B7280"
                                Click="CancelButton_Click"/>

                        <!-- زر الحفظ -->
                        <Button x:Name="SaveButton"
                                Grid.Column="3"
                                Content="💾 حفظ"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                materialDesign:ButtonAssist.CornerRadius="10"
                                Background="#667eea"
                                Height="45"
                                Width="120"
                                FontSize="14"
                                FontWeight="Bold"
                                Margin="15,0,0,0"
                                Click="SaveButton_Click">
                            <Button.Effect>
                                <DropShadowEffect Color="#667eea" Opacity="0.3" BlurRadius="10" ShadowDepth="3"/>
                            </Button.Effect>
                        </Button>
                    </Grid>
                </Border>
            </Grid>

            <!-- Enhanced Loading Overlay -->
            <Grid x:Name="LoadingGrid"
                  Grid.RowSpan="4"
                  Background="#CC000000"
                  Visibility="Collapsed">
                <Border Background="White"
                        CornerRadius="15"
                        Padding="40,30"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center">
                    <Border.Effect>
                        <DropShadowEffect Color="#40000000" BlurRadius="25" ShadowDepth="10" Direction="270"/>
                    </Border.Effect>
                    <StackPanel HorizontalAlignment="Center">
                        <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                     Width="60"
                                     Height="60"
                                     IsIndeterminate="True"
                                     Foreground="#667eea"/>
                        <TextBlock Text="جاري الحفظ..."
                                   FontSize="16"
                                   FontWeight="Medium"
                                   Foreground="#2D3748"
                                   HorizontalAlignment="Center"
                                   Margin="0,15,0,0"/>
                        <TextBlock Text="يرجى الانتظار..."
                                   FontSize="12"
                                   Foreground="#6B7280"
                                   HorizontalAlignment="Center"
                                   Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Grid>
    </Border>
</UserControl>
