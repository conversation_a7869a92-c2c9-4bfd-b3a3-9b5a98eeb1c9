using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// واجهة خدمة التخزين السحابي
    /// </summary>
    public interface ICloudStorageService
    {
        /// <summary>
        /// التحقق من حالة الاتصال
        /// </summary>
        Task<bool> IsConnectedAsync();

        /// <summary>
        /// تسجيل الدخول إلى الخدمة السحابية
        /// </summary>
        Task<bool> AuthenticateAsync();

        /// <summary>
        /// تسجيل الخروج من الخدمة السحابية
        /// </summary>
        Task<bool> LogoutAsync();

        /// <summary>
        /// رفع ملف إلى السحابة
        /// </summary>
        /// <param name="localFilePath">مسار الملف المحلي</param>
        /// <param name="cloudFileName">اسم الملف في السحابة</param>
        /// <param name="folderPath">مسار المجلد في السحابة</param>
        /// <returns>معرف الملف في السحابة</returns>
        Task<string?> UploadFileAsync(string localFilePath, string cloudFileName, string folderPath = "");

        /// <summary>
        /// تحميل ملف من السحابة
        /// </summary>
        /// <param name="cloudFileId">معرف الملف في السحابة</param>
        /// <param name="localFilePath">مسار الحفظ المحلي</param>
        Task<bool> DownloadFileAsync(string cloudFileId, string localFilePath);

        /// <summary>
        /// حذف ملف من السحابة
        /// </summary>
        /// <param name="cloudFileId">معرف الملف في السحابة</param>
        Task<bool> DeleteFileAsync(string cloudFileId);

        /// <summary>
        /// التحقق من وجود ملف في السحابة
        /// </summary>
        /// <param name="cloudFileId">معرف الملف في السحابة</param>
        Task<bool> FileExistsAsync(string cloudFileId);

        /// <summary>
        /// الحصول على معلومات الملف
        /// </summary>
        /// <param name="cloudFileId">معرف الملف في السحابة</param>
        Task<CloudFileInfo?> GetFileInfoAsync(string cloudFileId);

        /// <summary>
        /// الحصول على قائمة الملفات في مجلد
        /// </summary>
        /// <param name="folderPath">مسار المجلد</param>
        Task<List<CloudFileInfo>> GetFilesInFolderAsync(string folderPath = "");

        /// <summary>
        /// إنشاء مجلد في السحابة
        /// </summary>
        /// <param name="folderName">اسم المجلد</param>
        /// <param name="parentFolderId">معرف المجلد الأب</param>
        Task<string?> CreateFolderAsync(string folderName, string parentFolderId = "");

        /// <summary>
        /// الحصول على معلومات التخزين
        /// </summary>
        Task<StorageInfo> GetStorageInfoAsync();

        /// <summary>
        /// الحصول على معلومات المستخدم
        /// </summary>
        Task<UserInfo?> GetUserInfoAsync();

        /// <summary>
        /// مزامنة الملفات المحلية مع السحابة
        /// </summary>
        Task<SyncResult> SyncFilesAsync();

        /// <summary>
        /// حدث تغيير حالة الاتصال
        /// </summary>
        event EventHandler<ConnectionStatusChangedEventArgs>? ConnectionStatusChanged;

        /// <summary>
        /// حدث تقدم رفع الملف
        /// </summary>
        event EventHandler<FileUploadProgressEventArgs>? FileUploadProgress;

        /// <summary>
        /// حدث تقدم تحميل الملف
        /// </summary>
        event EventHandler<FileDownloadProgressEventArgs>? FileDownloadProgress;
    }

    /// <summary>
    /// معلومات الملف في السحابة
    /// </summary>
    public class CloudFileInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public long Size { get; set; }
        public DateTime CreatedTime { get; set; }
        public DateTime ModifiedTime { get; set; }
        public string MimeType { get; set; } = string.Empty;
        public string WebViewLink { get; set; } = string.Empty;
        public string WebContentLink { get; set; } = string.Empty;
    }

    /// <summary>
    /// معلومات التخزين
    /// </summary>
    public class StorageInfo
    {
        public long TotalSpace { get; set; }
        public long UsedSpace { get; set; }
        public long FreeSpace => TotalSpace - UsedSpace;
        public double UsagePercentage => TotalSpace > 0 ? (double)UsedSpace / TotalSpace * 100 : 0;
    }

    /// <summary>
    /// معلومات المستخدم
    /// </summary>
    public class UserInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string PhotoUrl { get; set; } = string.Empty;
    }

    /// <summary>
    /// نتيجة المزامنة
    /// </summary>
    public class SyncResult
    {
        public bool Success { get; set; }
        public int FilesProcessed { get; set; }
        public int FilesUploaded { get; set; }
        public int FilesDownloaded { get; set; }
        public int FilesDeleted { get; set; }
        public int FilesSkipped { get; set; }
        public List<string> Errors { get; set; } = new();
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// أحداث تغيير حالة الاتصال
    /// </summary>
    public class ConnectionStatusChangedEventArgs : EventArgs
    {
        public bool IsConnected { get; set; }
        public string? Message { get; set; }
    }

    /// <summary>
    /// أحداث تقدم رفع الملف
    /// </summary>
    public class FileUploadProgressEventArgs : EventArgs
    {
        public string FileName { get; set; } = string.Empty;
        public long BytesUploaded { get; set; }
        public long TotalBytes { get; set; }
        public double ProgressPercentage => TotalBytes > 0 ? (double)BytesUploaded / TotalBytes * 100 : 0;
    }

    /// <summary>
    /// أحداث تقدم تحميل الملف
    /// </summary>
    public class FileDownloadProgressEventArgs : EventArgs
    {
        public string FileName { get; set; } = string.Empty;
        public long BytesDownloaded { get; set; }
        public long TotalBytes { get; set; }
        public double ProgressPercentage => TotalBytes > 0 ? (double)BytesDownloaded / TotalBytes * 100 : 0;
    }
}
