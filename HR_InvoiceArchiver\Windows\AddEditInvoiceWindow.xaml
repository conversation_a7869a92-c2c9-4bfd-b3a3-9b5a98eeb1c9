<Window x:Class="HR_InvoiceArchiver.Windows.AddEditInvoiceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:converters="clr-namespace:HR_InvoiceArchiver.Converters"
        Title="إضافة/تعديل فاتورة"
        Height="900" Width="700"
        WindowStartupLocation="CenterOwner"
        Background="Transparent"
        FontFamily="{DynamicResource MaterialDesignFont}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize"
        MinHeight="850" MinWidth="650"
        MaxHeight="1000" MaxWidth="800"
        WindowStyle="None"
        AllowsTransparency="True">

    <Window.Resources>
        <converters:StatusToTextConverter x:Key="StatusToTextConverter"/>
        <converters:CurrencyConverter x:Key="CurrencyConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Modern Gradient Brushes -->
        <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#10B981" Offset="0"/>
            <GradientStop Color="#059669" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#20000000" BlurRadius="20" ShadowDepth="8" Direction="270"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Enhanced TextBox Style -->
        <Style x:Key="EnhancedTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="Padding" Value="16,16,16,8"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="#667eea"/>
            <Setter Property="materialDesign:TextFieldAssist.RippleOnFocusEnabled" Value="True"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.75"/>
        </Style>

        <!-- Enhanced ComboBox Style -->
        <Style x:Key="EnhancedComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="Padding" Value="16,16,16,8"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="#667eea"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.75"/>
        </Style>

        <!-- Enhanced DatePicker Style -->
        <Style x:Key="EnhancedDatePickerStyle" TargetType="DatePicker" BasedOn="{StaticResource MaterialDesignOutlinedDatePicker}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="Padding" Value="16,16,16,8"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="#667eea"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.75"/>
        </Style>

        <!-- Enhanced ScrollViewer Style -->
        <Style x:Key="SmoothScrollViewerStyle" TargetType="ScrollViewer">
            <Setter Property="CanContentScroll" Value="False"/>
            <Setter Property="PanningMode" Value="VerticalOnly"/>
            <Setter Property="IsDeferredScrollingEnabled" Value="False"/>
            <Setter Property="VirtualizingPanel.IsVirtualizing" Value="False"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ScrollViewer">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <ScrollContentPresenter Grid.Column="0" Grid.Row="0"
                                                  Content="{TemplateBinding Content}"
                                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                                  CanContentScroll="{TemplateBinding CanContentScroll}"/>

                            <ScrollBar Grid.Column="1" Grid.Row="0"
                                     Name="PART_VerticalScrollBar"
                                     Orientation="Vertical"
                                     Value="{TemplateBinding VerticalOffset}"
                                     Maximum="{TemplateBinding ScrollableHeight}"
                                     ViewportSize="{TemplateBinding ViewportHeight}"
                                     Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                     Width="12"
                                     Margin="2,0,0,0"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <!-- Main Container with Shadow and Rounded Corners -->
    <Border Background="White"
            CornerRadius="16"
            Margin="12">
        <Border.Effect>
            <DropShadowEffect Color="#30000000"
                            BlurRadius="35"
                            ShadowDepth="12"
                            Direction="270"
                            Opacity="0.4"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Custom Title Bar -->
            <Border Grid.Row="0"
                    Background="#F8FAFF"
                    CornerRadius="16,16,0,0"
                    Height="50"
                    BorderBrush="#E5E7EB"
                    BorderThickness="0,0,0,1"
                    MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Window Title -->
                    <StackPanel Grid.Column="0"
                              Orientation="Horizontal"
                              VerticalAlignment="Center"
                              Margin="25,0,0,0">
                        <Border Background="{StaticResource PrimaryGradientBrush}"
                                CornerRadius="6"
                                Width="28" Height="28"
                                VerticalAlignment="Center"
                                Margin="0,0,12,0">
                            <materialDesign:PackIcon Kind="FileDocumentPlus"
                                                   Width="16" Height="16"
                                                   Foreground="White"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock x:Name="WindowTitleTextBlock"
                                   Text="إضافة فاتورة جديدة"
                                   FontSize="15"
                                   FontWeight="SemiBold"
                                   Foreground="#1F2937"
                                   VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- Minimize Button -->
                    <Button Grid.Column="1"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="35" Height="35"
                            Click="MinimizeButton_Click"
                            Foreground="#6B7280"
                            ToolTip="تصغير النافذة">
                        <Button.Effect>
                            <DropShadowEffect Color="#20000000" BlurRadius="8" ShadowDepth="2" Opacity="0.2"/>
                        </Button.Effect>
                        <materialDesign:PackIcon Kind="WindowMinimize" Width="16" Height="16"/>
                    </Button>

                    <!-- Close Button -->
                    <Button Grid.Column="2"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="35" Height="35"
                            Click="CloseButton_Click"
                            Foreground="#EF4444"
                            ToolTip="إغلاق النافذة">
                        <Button.Effect>
                            <DropShadowEffect Color="#20000000" BlurRadius="8" ShadowDepth="2" Opacity="0.2"/>
                        </Button.Effect>
                        <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                    </Button>
                </Grid>
            </Border>

            <!-- Header Section -->
            <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCardStyle}" Margin="25,15,25,0">
                <Border CornerRadius="12" Padding="30,25">
                    <Border.Background>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                            <GradientStop Color="#F8FAFF" Offset="0"/>
                            <GradientStop Color="#EEF2FF" Offset="1"/>
                        </LinearGradientBrush>
                    </Border.Background>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Icon Container -->
                        <Border Grid.Column="0"
                                Background="{StaticResource PrimaryGradientBrush}"
                                CornerRadius="16"
                                Width="64" Height="64"
                                VerticalAlignment="Center"
                                Margin="0,0,25,0">
                            <Border.Effect>
                                <DropShadowEffect Color="#667eea" BlurRadius="20" ShadowDepth="8" Opacity="0.3"/>
                            </Border.Effect>
                            <materialDesign:PackIcon Kind="FileDocumentPlus"
                                                   Width="32" Height="32"
                                                   Foreground="White"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"/>
                        </Border>

                        <!-- Title and Description -->
                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock x:Name="HeaderTitleTextBlock"
                                       Text="إضافة فاتورة جديدة"
                                       FontSize="26"
                                       FontWeight="Bold"
                                       Foreground="#1F2937"
                                       Margin="0,0,0,8"
                                       LineHeight="32"/>
                            <TextBlock x:Name="HeaderDescriptionTextBlock"
                                       Text="قم بإدخال بيانات الفاتورة الجديدة بعناية ودقة"
                                       FontSize="15"
                                       Foreground="#6B7280"
                                       TextWrapping="Wrap"
                                       LineHeight="22"
                                       Opacity="0.9"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </materialDesign:Card>

            <!-- Main Content -->
            <ScrollViewer Grid.Row="2"
                          Style="{StaticResource SmoothScrollViewerStyle}"
                          VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Disabled"
                          Padding="25,20,25,0"
                          PreviewMouseWheel="ScrollViewer_PreviewMouseWheel">
                <StackPanel>
                    <!-- Basic Information Card -->
                    <materialDesign:Card Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
                        <Grid Margin="35,30">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Section Header -->
                            <Border Grid.Row="0"
                                    Background="#F8F9FF"
                                    CornerRadius="12"
                                    Padding="25,18"
                                    Margin="0,0,0,30">
                                <StackPanel Orientation="Horizontal">
                                    <Border Background="{StaticResource PrimaryGradientBrush}"
                                            CornerRadius="10"
                                            Width="44" Height="44"
                                            VerticalAlignment="Center"
                                            Margin="0,0,18,0">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#667eea" BlurRadius="12" ShadowDepth="4" Opacity="0.25"/>
                                        </Border.Effect>
                                        <materialDesign:PackIcon Kind="InformationOutline"
                                                               Width="24" Height="24"
                                                               Foreground="White"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel VerticalAlignment="Center">
                                        <TextBlock Text="المعلومات الأساسية"
                                                 FontSize="19"
                                                 FontWeight="Bold"
                                                 Foreground="#1F2937"
                                                 Margin="0,0,0,5"
                                                 LineHeight="24"/>
                                        <TextBlock Text="بيانات الفاتورة الرئيسية والمطلوبة"
                                                 FontSize="14"
                                                 Foreground="#6B7280"
                                                 LineHeight="18"
                                                 Opacity="0.9"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- Form Fields -->
                            <Grid Grid.Row="1">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="25"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Row 1: Invoice Number & Date -->
                                <TextBox x:Name="InvoiceNumberTextBox"
                                       Grid.Row="0" Grid.Column="0"
                                       Style="{StaticResource EnhancedTextBoxStyle}"
                                       materialDesign:HintAssist.Hint="رقم الفاتورة *"
                                       materialDesign:HintAssist.HelperText="أدخل رقم الفاتورة الفريد"
                                       Text="{Binding InvoiceNumber, UpdateSourceTrigger=PropertyChanged}"
                                       Margin="0,0,0,25"/>

                                <DatePicker x:Name="InvoiceDatePicker"
                                          Grid.Row="0" Grid.Column="2"
                                          Style="{StaticResource EnhancedDatePickerStyle}"
                                          materialDesign:HintAssist.Hint="تاريخ الفاتورة *"
                                          materialDesign:HintAssist.HelperText="تاريخ إصدار الفاتورة"
                                          SelectedDate="{Binding InvoiceDate, UpdateSourceTrigger=PropertyChanged}"
                                          Margin="0,0,0,25"/>

                                <!-- Row 2: Supplier & Due Date -->
                                <ComboBox x:Name="SupplierComboBox"
                                        Grid.Row="1" Grid.Column="0"
                                        Style="{StaticResource EnhancedComboBoxStyle}"
                                        materialDesign:HintAssist.Hint="المورد *"
                                        materialDesign:HintAssist.HelperText="اختر المورد من القائمة"
                                        ItemsSource="{Binding Suppliers}"
                                        SelectedItem="{Binding SelectedSupplier}"
                                        DisplayMemberPath="Name"
                                        SelectedValuePath="Id"
                                        Margin="0,0,0,25"/>

                                <DatePicker x:Name="DueDatePicker"
                                          Grid.Row="1" Grid.Column="2"
                                          Style="{StaticResource EnhancedDatePickerStyle}"
                                          materialDesign:HintAssist.Hint="تاريخ الاستحقاق"
                                          materialDesign:HintAssist.HelperText="تاريخ استحقاق الدفع (اختياري)"
                                          SelectedDate="{Binding DueDate, UpdateSourceTrigger=PropertyChanged}"
                                          Margin="0,0,0,25"/>

                                <!-- Row 3: Amount & Status -->
                                <TextBox x:Name="AmountTextBox"
                                       Grid.Row="2" Grid.Column="0"
                                       Style="{StaticResource EnhancedTextBoxStyle}"
                                       materialDesign:HintAssist.Hint="المبلغ (د.ع) *"
                                       materialDesign:HintAssist.HelperText="المبلغ الإجمالي للفاتورة"
                                       Text="{Binding TotalAmount, UpdateSourceTrigger=PropertyChanged}"
                                       Margin="0,0,0,25"/>

                                <ComboBox x:Name="StatusComboBox"
                                        Grid.Row="2" Grid.Column="2"
                                        Style="{StaticResource EnhancedComboBoxStyle}"
                                        materialDesign:HintAssist.Hint="حالة الفاتورة *"
                                        materialDesign:HintAssist.HelperText="الحالة الحالية للفاتورة"
                                        ItemsSource="{Binding StatusOptions}"
                                        SelectedItem="{Binding Status, UpdateSourceTrigger=PropertyChanged}"
                                        Margin="0,0,0,25">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding Converter={StaticResource StatusToTextConverter}}" />
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>

                                <!-- Row 4: Payment Amount (spans both columns) -->
                                <TextBox x:Name="PaidAmountTextBox"
                                       Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="3"
                                       Style="{StaticResource EnhancedTextBoxStyle}"
                                       materialDesign:HintAssist.Hint="المبلغ المدفوع (د.ع)"
                                       materialDesign:HintAssist.HelperText="المبلغ المدفوع من إجمالي الفاتورة (اختياري)"
                                       Text="{Binding PaidAmount, UpdateSourceTrigger=PropertyChanged}"
                                       Margin="0,0,0,0"/>
                            </Grid>
                        </Grid>
                    </materialDesign:Card>

                    <!-- Additional Information Card -->
                    <materialDesign:Card Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
                        <Grid Margin="35,30">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Section Header -->
                            <Border Grid.Row="0"
                                    Background="#F0FDF4"
                                    CornerRadius="12"
                                    Padding="25,18"
                                    Margin="0,0,0,30">
                                <StackPanel Orientation="Horizontal">
                                    <Border Background="{StaticResource SecondaryGradientBrush}"
                                            CornerRadius="10"
                                            Width="44" Height="44"
                                            VerticalAlignment="Center"
                                            Margin="0,0,18,0">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#10B981" BlurRadius="12" ShadowDepth="4" Opacity="0.25"/>
                                        </Border.Effect>
                                        <materialDesign:PackIcon Kind="TextBoxOutline"
                                                               Width="24" Height="24"
                                                               Foreground="White"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel VerticalAlignment="Center">
                                        <TextBlock Text="معلومات إضافية"
                                                 FontSize="19"
                                                 FontWeight="Bold"
                                                 Foreground="#1F2937"
                                                 Margin="0,0,0,5"
                                                 LineHeight="24"/>
                                        <TextBlock Text="وصف وملاحظات تفصيلية للفاتورة"
                                                 FontSize="14"
                                                 Foreground="#6B7280"
                                                 LineHeight="18"
                                                 Opacity="0.9"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- Form Fields -->
                            <StackPanel Grid.Row="1">
                                <!-- Description -->
                                <TextBox x:Name="DescriptionTextBox"
                                       Style="{StaticResource EnhancedTextBoxStyle}"
                                       materialDesign:HintAssist.Hint="وصف الفاتورة"
                                       materialDesign:HintAssist.HelperText="وصف تفصيلي لمحتويات الفاتورة"
                                       Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                                       AcceptsReturn="True"
                                       TextWrapping="Wrap"
                                       Height="95"
                                       VerticalContentAlignment="Top"
                                       ScrollViewer.VerticalScrollBarVisibility="Auto"
                                       ScrollViewer.CanContentScroll="False"
                                       Margin="0,0,0,25"/>

                                <!-- Notes -->
                                <TextBox x:Name="NotesTextBox"
                                       Style="{StaticResource EnhancedTextBoxStyle}"
                                       materialDesign:HintAssist.Hint="ملاحظات إضافية"
                                       materialDesign:HintAssist.HelperText="أي ملاحظات أو تعليقات إضافية"
                                       Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                                       AcceptsReturn="True"
                                       TextWrapping="Wrap"
                                       Height="95"
                                       VerticalContentAlignment="Top"
                                       ScrollViewer.VerticalScrollBarVisibility="Auto"
                                       ScrollViewer.CanContentScroll="False"/>
                            </StackPanel>
                        </Grid>
                    </materialDesign:Card>

                    <!-- Attachments Card -->
                    <materialDesign:Card Style="{StaticResource ModernCardStyle}" Margin="0,0,0,0">
                        <Grid Margin="35,30">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Section Header -->
                            <Border Grid.Row="0"
                                    Background="#FEF3E2"
                                    CornerRadius="12"
                                    Padding="25,18"
                                    Margin="0,0,0,30">
                                <StackPanel Orientation="Horizontal">
                                    <Border Background="#F59E0B"
                                            CornerRadius="10"
                                            Width="44" Height="44"
                                            VerticalAlignment="Center"
                                            Margin="0,0,18,0">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#F59E0B" BlurRadius="12" ShadowDepth="4" Opacity="0.25"/>
                                        </Border.Effect>
                                        <materialDesign:PackIcon Kind="Attachment"
                                                               Width="24" Height="24"
                                                               Foreground="White"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>
                                    </Border>
                                    <StackPanel VerticalAlignment="Center">
                                        <TextBlock Text="المرفقات"
                                                 FontSize="19"
                                                 FontWeight="Bold"
                                                 Foreground="#1F2937"
                                                 Margin="0,0,0,5"
                                                 LineHeight="24"/>
                                        <TextBlock Text="إرفاق ملفات أو مستندات مع الفاتورة"
                                                 FontSize="14"
                                                 Foreground="#6B7280"
                                                 LineHeight="18"
                                                 Opacity="0.9"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- Attachment Controls -->
                            <Grid Grid.Row="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="20"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBox x:Name="AttachmentPathTextBox"
                                       Grid.Column="0"
                                       Style="{StaticResource EnhancedTextBoxStyle}"
                                       materialDesign:HintAssist.Hint="مسار المرفق"
                                       materialDesign:HintAssist.HelperText="اختر ملف لإرفاقه مع الفاتورة"
                                       Text="{Binding AttachmentPath, UpdateSourceTrigger=PropertyChanged}"
                                       IsReadOnly="True"/>

                                <Button Grid.Column="2"
                                      Style="{StaticResource MaterialDesignRaisedButton}"
                                      materialDesign:ButtonAssist.CornerRadius="10"
                                      Height="56"
                                      MinWidth="120"
                                      Padding="25,0"
                                      Background="{StaticResource SecondaryGradientBrush}"
                                      FontWeight="Medium"
                                      Click="BrowseAttachmentButton_Click">
                                    <Button.Effect>
                                        <DropShadowEffect Color="#10B981" BlurRadius="12" ShadowDepth="3" Opacity="0.25"/>
                                    </Button.Effect>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FolderOpen" Width="18" Height="18" Margin="0,0,8,0"/>
                                        <TextBlock Text="تصفح"/>
                                    </StackPanel>
                                </Button>
                            </Grid>
                        </Grid>
                    </materialDesign:Card>
                </StackPanel>
            </ScrollViewer>
            <!-- Action Buttons -->
            <Border Grid.Row="3"
                    Background="White"
                    BorderBrush="#E5E7EB"
                    BorderThickness="0,1,0,0"
                    CornerRadius="0,0,16,16"
                    Padding="35,25">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Validation Messages -->
                    <StackPanel Grid.Column="0" VerticalAlignment="Center">
                        <TextBlock x:Name="ValidationMessageTextBlock"
                                   Text=""
                                   Foreground="#EF4444"
                                   FontSize="13"
                                   FontWeight="Medium"
                                   TextWrapping="Wrap"
                                   Visibility="Collapsed"/>
                    </StackPanel>

                    <!-- Cancel Button -->
                    <Button Grid.Column="1"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            materialDesign:ButtonAssist.CornerRadius="12"
                            Height="52"
                            MinWidth="130"
                            Padding="30,0"
                            Margin="0,0,20,0"
                            BorderBrush="#D1D5DB"
                            Foreground="#6B7280"
                            FontSize="15"
                            FontWeight="Medium"
                            Click="CancelButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Close" Width="18" Height="18" Margin="0,0,10,0"/>
                            <TextBlock Text="إلغاء"/>
                        </StackPanel>
                    </Button>

                    <!-- Save Button -->
                    <Button x:Name="SaveButton"
                            Grid.Column="2"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            materialDesign:ButtonAssist.CornerRadius="12"
                            Height="52"
                            MinWidth="160"
                            Padding="35,0"
                            Background="{StaticResource PrimaryGradientBrush}"
                            FontSize="15"
                            FontWeight="Medium"
                            materialDesign:ElevationAssist.Elevation="Dp6"
                            Click="SaveButton_Click">
                        <Button.Effect>
                            <DropShadowEffect Color="#667eea" BlurRadius="18" ShadowDepth="4" Opacity="0.35"/>
                        </Button.Effect>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ContentSave" Width="18" Height="18" Margin="0,0,10,0"/>
                            <TextBlock x:Name="SaveButtonText" Text="حفظ الفاتورة"/>
                        </StackPanel>
                    </Button>
                </Grid>
            </Border>
        </Grid>
    </Border>
</Window>
