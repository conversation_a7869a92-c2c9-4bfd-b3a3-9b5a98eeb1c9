<UserControl x:Class="HR_InvoiceArchiver.Pages.PaymentsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:HR_InvoiceArchiver.Pages"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">

    <UserControl.Resources>
        <!-- Converters -->
        <!-- <local:StringNullOrEmptyConverter x:Key="StringNullOrEmptyConverter"/> -->

        <!-- Modern Gradient Brushes -->
        <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#10B981" Offset="0"/>
            <GradientStop Color="#059669" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="WarningGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#F59E0B" Offset="0"/>
            <GradientStop Color="#D97706" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="DangerGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#EF4444" Offset="0"/>
            <GradientStop Color="#DC2626" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#40000000" BlurRadius="20" ShadowDepth="8" Direction="270"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#40000000" BlurRadius="8" ShadowDepth="2" Direction="270"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Search Box Style -->
        <Style x:Key="SearchBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="45"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.75"/>
            <Setter Property="materialDesign:HintAssist.FloatingOffset" Value="0,-20"/>
            <Setter Property="materialDesign:TextFieldAssist.HasClearButton" Value="True"/>
            <Setter Property="materialDesign:TextFieldAssist.HasLeadingIcon" Value="False"/>
            <Setter Property="materialDesign:TextFieldAssist.DecorationVisibility" Value="Collapsed"/>
            <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="{StaticResource PrimaryGradientBrush}"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="#BDBDBD"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان والأزرار -->
        <materialDesign:Card Grid.Row="0" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
            <Grid Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- العنوان مع أيقونة -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="CreditCardMultiple"
                                           Width="32" Height="32"
                                           Foreground="{StaticResource PrimaryGradientBrush}"
                                           VerticalAlignment="Center"
                                           Margin="0,0,10,0"/>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="إدارة المدفوعات" FontSize="24" FontWeight="Bold" Foreground="#1E293B"/>
                        <TextBlock Text="إدارة وتتبع جميع المدفوعات والإيصالات" FontSize="14" 
                                 Foreground="#64748B" Margin="0,2,0,0"/>
                    </StackPanel>
                </StackPanel>

                <!-- أزرار الإجراءات -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <!-- إضافة مدفوعة -->
                    <Button x:Name="AddPaymentButton" Click="AddPaymentButton_Click"
                           Style="{StaticResource ModernButtonStyle}"
                           Background="{StaticResource SecondaryGradientBrush}"
                           Margin="5,0" MinWidth="130">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة مدفوعة" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <!-- وصل متعدد -->
                    <Button x:Name="AddMultiPaymentButton" Click="AddMultiPaymentButton_Click"
                           Style="{StaticResource ModernButtonStyle}"
                           Background="{StaticResource SecondaryGradientBrush}"
                           Margin="8,0,0,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Receipt" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="وصل متعدد" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <!-- تصدير -->
                    <Button x:Name="ExportButton" Click="ExportButton_Click"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           materialDesign:ButtonAssist.CornerRadius="8"
                           Height="40"
                           Padding="12,8"
                           Margin="8,0,0,0"
                           BorderBrush="{StaticResource PrimaryGradientBrush}"
                           Foreground="{StaticResource PrimaryGradientBrush}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileExcel" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="تصدير Excel" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <!-- تحديث -->
                    <Button x:Name="RefreshButton" Click="RefreshButton_Click"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           materialDesign:ButtonAssist.CornerRadius="8"
                           Height="40"
                           Padding="12,8"
                           Margin="8,0,0,0"
                           BorderBrush="{StaticResource SecondaryGradientBrush}"
                           Foreground="{StaticResource SecondaryGradientBrush}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- شريط البحث والفلاتر -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
            <Grid Margin="20,15,20,20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- شريط البحث -->
                <Grid Grid.Row="0" Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- مربع البحث -->
                    <TextBox Grid.Column="0" x:Name="SearchTextBox"
                             materialDesign:HintAssist.Hint="🔍 البحث في المدفوعات (رقم الإيصال، المورد، المبلغ...)"
                             Style="{StaticResource SearchBoxStyle}"
                             TextChanged="SearchTextBox_TextChanged"/>

                    <!-- زر البحث -->
                    <Button Grid.Column="1" x:Name="SearchButton"
                           Style="{StaticResource ModernButtonStyle}"
                           Background="{StaticResource PrimaryGradientBrush}"
                           Margin="0,0,10,0" MinWidth="100">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Magnify" Width="16" Height="16" Margin="0,0,6,0"/>
                            <TextBlock Text="بحث" FontSize="12"/>
                        </StackPanel>
                    </Button>

                    <!-- زر مسح البحث -->
                    <Button Grid.Column="2" x:Name="ClearSearchButton" Click="ClearSearchButton_Click"
                           Style="{StaticResource ModernButtonStyle}"
                           Background="#6B7280" MinWidth="80">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Close" Width="16" Height="16" Margin="0,0,6,0"/>
                            <TextBlock Text="مسح" FontSize="12"/>
                        </StackPanel>
                    </Button>
                </Grid>

                <!-- الفلاتر المتقدمة -->
                <Expander Grid.Row="1" x:Name="AdvancedFiltersExpander" 
                         Header="🔧 فلاتر متقدمة" FontSize="14" FontWeight="Medium"
                         Foreground="#374151" Margin="0,0,0,15">
                    <Border Margin="0,15,0,0" Background="#F7FAFC" Padding="12" CornerRadius="8">
                        <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- فلتر التاريخ من -->
                        <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,10">
                            <TextBlock Text="📅 من تاريخ:" FontSize="12" FontWeight="Medium" Foreground="#374151" Margin="0,0,0,8"/>
                            <DatePicker x:Name="FromDatePicker"
                                      Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                      FontSize="12" Height="35"/>
                        </StackPanel>

                        <!-- فلتر التاريخ إلى -->
                        <StackPanel Grid.Row="0" Grid.Column="1" Margin="0,0,10,10">
                            <TextBlock Text="📅 إلى تاريخ:" FontSize="12" FontWeight="Medium" Foreground="#374151" Margin="0,0,0,8"/>
                            <DatePicker x:Name="ToDatePicker"
                                      Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                      FontSize="12" Height="35"/>
                        </StackPanel>

                        <!-- فلتر المبلغ الأدنى -->
                        <StackPanel Grid.Row="0" Grid.Column="2" Margin="0,0,10,10">
                            <TextBlock Text="💰 المبلغ الأدنى:" FontSize="12" FontWeight="Medium" Foreground="#374151" Margin="0,0,0,8"/>
                            <TextBox x:Name="MinAmountTextBox"
                                   materialDesign:HintAssist.Hint="0"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   FontSize="12" Height="35"/>
                        </StackPanel>

                        <!-- فلتر المبلغ الأعلى -->
                        <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,10">
                            <TextBlock Text="💰 المبلغ الأعلى:" FontSize="12" FontWeight="Medium" Foreground="#374151" Margin="0,0,0,8"/>
                            <TextBox x:Name="MaxAmountTextBox"
                                   materialDesign:HintAssist.Hint="∞"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   FontSize="12" Height="35"/>
                        </StackPanel>

                        <!-- فلتر طريقة الدفع -->
                        <StackPanel Grid.Row="1" Grid.Column="1" Margin="0,0,10,10">
                            <TextBlock Text="� طريقة الدفع:" FontSize="12" FontWeight="Medium" Foreground="#374151" Margin="0,0,0,8"/>
                            <ComboBox x:Name="PaymentMethodComboBox"
                                    materialDesign:HintAssist.Hint="اختر طريقة الدفع"
                                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                    FontSize="12" Height="35">
                                <ComboBoxItem Content="الكل"/>
                                <ComboBoxItem Content="نقدي"/>
                                <ComboBoxItem Content="بطاقة ائتمان"/>
                                <ComboBoxItem Content="تحويل بنكي"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- زر مسح الفلاتر -->
                        <StackPanel Grid.Row="1" Grid.Column="2" VerticalAlignment="Center">
                            <Button x:Name="ClearFiltersButton"
                                    Content="🗑️ مسح الفلاتر"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                    materialDesign:ButtonAssist.CornerRadius="8"
                                    Height="40"
                                    Padding="16,8"
                                    FontWeight="Medium"
                                    BorderBrush="#E53E3E"
                                    Foreground="#E53E3E"
                                    Margin="0,0,0,8"/>
                        </StackPanel>

                        <!-- زر تطبيق الفلاتر -->
                        <Button Grid.Row="0" Grid.Column="3" x:Name="ApplyFiltersButton" Click="ApplyFiltersButton_Click"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="{StaticResource SecondaryGradientBrush}"
                               VerticalAlignment="Bottom">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Filter" Width="14" Height="14" Margin="0,0,6,0"/>
                                <TextBlock Text="تطبيق" FontSize="11"/>
                            </StackPanel>
                        </Button>
                        </Grid>
                    </Border>
                </Expander>
            </Grid>
        </materialDesign:Card>




        <!-- جدول المدفوعات -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- عنوان الجدول -->
                <Border Grid.Row="0"
                        Background="{StaticResource PrimaryGradientBrush}"
                        CornerRadius="8,8,0,0"
                        Padding="20,15">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="TableLarge"
                                               Width="24" Height="24"
                                               Foreground="White"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>
                        <TextBlock Text="قائمة المدفوعات"
                                   FontSize="18"
                                   FontWeight="Bold"
                                   Foreground="White"
                                   VerticalAlignment="Center"/>
                        <TextBlock x:Name="TotalCountTextBlock"
                                   Text="(0 مدفوعة)"
                                   FontSize="14"
                                   Foreground="White"
                                   Opacity="0.8"
                                   VerticalAlignment="Center"
                                   Margin="10,0,0,0"/>
                    </StackPanel>
                </Border>

                <!-- الجدول -->
                <DataGrid x:Name="PaymentsDataGrid"
                          Grid.Row="1"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          SelectionMode="Single"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          materialDesign:DataGridAssist.CellPadding="12"
                          materialDesign:DataGridAssist.ColumnHeaderPadding="12"
                          FontSize="13"
                          FontFamily="Segoe UI"
                          Background="White"
                          RowBackground="White"
                          AlternatingRowBackground="#FAFBFC"
                          FlowDirection="RightToLeft">

                    <DataGrid.ColumnHeaderStyle>
                        <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                            <Setter Property="Background" Value="#F8F9FA"/>
                            <Setter Property="Foreground" Value="#2D3748"/>
                            <Setter Property="FontWeight" Value="Bold"/>
                            <Setter Property="FontSize" Value="14"/>
                            <Setter Property="Height" Value="50"/>
                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                            <Setter Property="Padding" Value="8"/>
                        </Style>
                    </DataGrid.ColumnHeaderStyle>

                    <DataGrid.RowStyle>
                        <Style TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
                            <Setter Property="Height" Value="55"/>
                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#E6F3FF"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="#CCE7FF"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </DataGrid.RowStyle>

                    <DataGrid.CellStyle>
                        <Style TargetType="DataGridCell" BasedOn="{StaticResource MaterialDesignDataGridCell}">
                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                            <Setter Property="Padding" Value="8,4"/>
                            <Setter Property="FontSize" Value="13"/>
                            <Setter Property="FontWeight" Value="Medium"/>
                        </Style>
                    </DataGrid.CellStyle>

                    <DataGrid.Columns>
                        <!-- رقم الإيصال -->
                        <DataGridTextColumn Header="📄 رقم الإيصال"
                                            Binding="{Binding ReceiptNumber}"
                                            Width="130">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Foreground" Value="#1976D2"/>
                                    <Setter Property="FlowDirection" Value="LeftToRight"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- اسم المورد -->
                        <DataGridTextColumn Header="🏢 المورد"
                                            Binding="{Binding SupplierName}"
                                            Width="150">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="FontWeight" Value="Medium"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Margin" Value="0,0,15,0"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                    <Setter Property="Foreground" Value="#1976D2"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- تاريخ الدفع -->
                        <DataGridTextColumn Header="📅 التاريخ"
                                            Binding="{Binding PaymentDate, StringFormat='{}{0:yyyy/MM/dd}'}"
                                            Width="110">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="FontWeight" Value="Medium"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Foreground" Value="#424242"/>
                                    <Setter Property="FlowDirection" Value="LeftToRight"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- مبلغ الفاتورة -->
                        <DataGridTextColumn Header="💰 إجمالي"
                                            Binding="{Binding Invoice.Amount, StringFormat='{}{0:N0}'}"
                                            Width="100">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Foreground" Value="#1976D2"/>
                                    <Setter Property="FlowDirection" Value="LeftToRight"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- المبلغ المدفوع -->
                        <DataGridTextColumn Header="✅ مدفوع"
                                            Binding="{Binding Amount, StringFormat='{}{0:N0}'}"
                                            Width="100">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Foreground" Value="#2E7D32"/>
                                    <Setter Property="FlowDirection" Value="LeftToRight"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- المبلغ المتبقي -->
                        <DataGridTextColumn Header="⏳ متبقي"
                                            Binding="{Binding Invoice.RemainingAmount, StringFormat='{}{0:N0}'}"
                                            Width="100">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FlowDirection" Value="LeftToRight"/>
                                    <!-- اللون الافتراضي للمبلغ المتبقي (أحمر للمبالغ المستحقة) -->
                                    <Setter Property="Foreground" Value="#D32F2F"/>
                                    <Style.Triggers>
                                        <!-- إذا كان المبلغ المتبقي يساوي 0 - لون أخضر -->
                                        <DataTrigger Binding="{Binding Invoice.RemainingAmount}" Value="0">
                                            <Setter Property="Foreground" Value="#2E7D32"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- حالة التسديد -->
                        <DataGridTemplateColumn Header="📊 الحالة" Width="120" CanUserSort="False" CanUserReorder="False">
                            <DataGridTemplateColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTemplateColumn.HeaderStyle>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="15" Padding="10,5" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="5">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="FullPayment">
                                                        <Setter Property="Background" Value="#E8F5E8"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="PartialPayment">
                                                        <Setter Property="Background" Value="#FFF3E0"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="PaymentWithDiscount">
                                                        <Setter Property="Background" Value="#F3E5F5"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="PaymentWithRefund">
                                                        <Setter Property="Background" Value="#E3F2FD"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock FontWeight="SemiBold" FontSize="12" HorizontalAlignment="Center" TextAlignment="Center">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Status}" Value="FullPayment">
                                                            <Setter Property="Text" Value="تسديد كامل"/>
                                                            <Setter Property="Foreground" Value="#2E7D32"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="PartialPayment">
                                                            <Setter Property="Text" Value="تسديد جزئي"/>
                                                            <Setter Property="Foreground" Value="#EF6C00"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="PaymentWithDiscount">
                                                            <Setter Property="Text" Value="تسديد وبخصم"/>
                                                            <Setter Property="Foreground" Value="#7B1FA2"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="PaymentWithRefund">
                                                            <Setter Property="Text" Value="تسديد واسترجاع"/>
                                                            <Setter Property="Foreground" Value="#1976D2"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- طريقة الدفع -->
                        <DataGridTextColumn Header="💳 الطريقة"
                                            Binding="{Binding MethodText}"
                                            Width="100">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="FontWeight" Value="Medium"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Foreground" Value="#424242"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- الملاحظات -->
                        <DataGridTextColumn Header="📝 الملاحظات"
                                            Binding="{Binding Notes}"
                                            Width="*" MinWidth="120">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="12"/>
                                    <Setter Property="FontWeight" Value="Normal"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Foreground" Value="#424242"/>
                                    <Setter Property="Margin" Value="0,0,15,0"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                    <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                    <Setter Property="ToolTip" Value="{Binding Notes}"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- الإجراءات -->
                        <DataGridTemplateColumn Header="⚙️ الإجراءات" Width="160" CanUserSort="False" CanUserReorder="False">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <!-- تعديل -->
                                        <Button ToolTip="تعديل المدفوعة"
                                               Style="{StaticResource MaterialDesignIconButton}"
                                               Width="32" Height="32"
                                               Background="#E8F5E8"
                                               Foreground="#2E7D32"
                                               Tag="{Binding}"
                                               Click="EditPayment_Click"
                                               Margin="2">
                                            <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                        </Button>

                                        <!-- حذف -->
                                        <Button ToolTip="حذف المدفوعة"
                                               Style="{StaticResource MaterialDesignIconButton}"
                                               Width="32" Height="32"
                                               Background="#FFEBEE"
                                               Foreground="#D32F2F"
                                               Tag="{Binding}"
                                               Click="DeletePayment_Click"
                                               Margin="2">
                                            <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- إحصائيات المدفوعات - شريط سفلي -->
        <materialDesign:Card Grid.Row="3" Style="{StaticResource ModernCardStyle}" Margin="0,20,0,0">
            <Grid Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- إحصائيات المدفوعات -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Border Background="{StaticResource PrimaryGradientBrush}"
                            CornerRadius="20"
                            Padding="16,8"
                            Margin="0,0,12,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Receipt"
                                                   Width="20" Height="20"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock x:Name="TotalPaymentsTextBlock"
                                       Text="إجمالي المدفوعات: 0"
                                       Foreground="White"
                                       FontWeight="Bold"
                                       FontSize="13"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <Border Background="{StaticResource SecondaryGradientBrush}"
                            CornerRadius="20"
                            Padding="16,8"
                            Margin="0,0,12,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CashMultiple"
                                                   Width="20" Height="20"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock x:Name="TotalAmountTextBlock"
                                       Text="إجمالي المبلغ: 0 د.ع"
                                       Foreground="White"
                                       FontWeight="Bold"
                                       FontSize="13"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <Border Background="#F59E0B"
                            CornerRadius="20"
                            Padding="16,8"
                            Margin="0,0,12,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Calculator"
                                                   Width="20" Height="20"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock x:Name="AveragePaymentTextBlock"
                                       Text="متوسط المدفوعة: 0 د.ع"
                                       Foreground="White"
                                       FontWeight="Bold"
                                       FontSize="13"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <Border Background="#8B5CF6"
                            CornerRadius="20"
                            Padding="16,8"
                            Margin="0,0,12,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Cash"
                                                   Width="20" Height="20"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock x:Name="CashPaymentsTextBlock"
                                       Text="مدفوعات نقدية: 0 د.ع"
                                       Foreground="White"
                                       FontWeight="Bold"
                                       FontSize="13"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <Border Background="#7B1FA2"
                            CornerRadius="20"
                            Padding="16,8">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CreditCard"
                                                   Width="20" Height="20"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock x:Name="CardPaymentsTextBlock"
                                       Text="مدفوعات بطاقة: 0 د.ع"
                                       Foreground="White"
                                       FontWeight="Bold"
                                       FontSize="13"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- مؤشر التحميل -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <ProgressBar x:Name="LoadingProgressBar"
                                 Style="{StaticResource MaterialDesignCircularProgressBar}"
                                 Width="24"
                                 Height="24"
                                 IsIndeterminate="True"
                                 Visibility="Collapsed"
                                 Margin="0,0,10,0"/>

                    <TextBlock Text="آخر تحديث: الآن"
                               FontSize="12"
                               Foreground="#718096"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Loading Panel -->
        <Border x:Name="LoadingPanel"
                Grid.RowSpan="5"
                Background="#CC000000"
                Visibility="Collapsed">
            <materialDesign:Card HorizontalAlignment="Center" VerticalAlignment="Center"
                               Padding="40,30" Style="{StaticResource ModernCardStyle}">
                <StackPanel HorizontalAlignment="Center">
                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                Width="50" Height="50"
                                IsIndeterminate="True"
                                Foreground="{StaticResource PrimaryGradientBrush}"/>
                    <TextBlock Text="جاري تحميل المدفوعات..."
                              FontSize="14" FontWeight="Medium"
                              Foreground="#2D3748"
                              HorizontalAlignment="Center"
                              Margin="0,15,0,0"/>
                </StackPanel>
            </materialDesign:Card>
        </Border>

        <!-- Empty State Panel -->
        <materialDesign:Card x:Name="EmptyStatePanel"
                           Grid.Row="2"
                           Style="{StaticResource ModernCardStyle}"
                           Visibility="Collapsed"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Margin="0,0,0,20"
                           Padding="60,40">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="CreditCardOff" Width="80" Height="80"
                                       Foreground="#9CA3AF" Margin="0,0,0,20"/>
                <TextBlock Text="لا توجد مدفوعات"
                          FontSize="24" FontWeight="Bold"
                          Foreground="#374151"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,10"/>
                <TextBlock Text="لم يتم العثور على أي مدفوعات مطابقة للبحث"
                          FontSize="16"
                          Foreground="#6B7280"
                          HorizontalAlignment="Center"
                          TextWrapping="Wrap"
                          TextAlignment="Center"
                          MaxWidth="400"
                          Margin="0,0,0,20"/>
                <Button Content="إضافة مدفوعة جديدة"
                       Style="{StaticResource ModernButtonStyle}"
                       Background="{StaticResource SecondaryGradientBrush}"
                       Click="AddPaymentButton_Click"
                       Padding="20,10"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Multi Payment Overlay -->
        <Border x:Name="MultiPaymentOverlay"
                Background="#80000000"
                Visibility="Collapsed"
                Panel.ZIndex="1000">
            <Border.RenderTransform>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Border.RenderTransform>
            <Border.RenderTransformOrigin>
                <Point X="0.5" Y="0.5"/>
            </Border.RenderTransformOrigin>

            <!-- Container for MultiPaymentFormControl -->
            <Grid x:Name="MultiPaymentContainer"
                  HorizontalAlignment="Center"
                  VerticalAlignment="Center"
                  MouseLeftButtonDown="MultiPaymentContainer_MouseLeftButtonDown"/>
        </Border>
    </Grid>
</UserControl>
