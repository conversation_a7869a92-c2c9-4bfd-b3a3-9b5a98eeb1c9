<UserControl x:Class="HR_InvoiceArchiver.Controls.MultiPaymentFormControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="Transparent"
             FlowDirection="RightToLeft"
             HorizontalAlignment="Stretch"
             VerticalAlignment="Stretch">

    <UserControl.RenderTransform>
        <ScaleTransform ScaleX="1" ScaleY="1"/>
    </UserControl.RenderTransform>
    <UserControl.RenderTransformOrigin>
        <Point X="0.5" Y="0.5"/>
    </UserControl.RenderTransformOrigin>

    <UserControl.Resources>
        <!-- Modern Gradient Brushes -->
        <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#10B981" Offset="0"/>
            <GradientStop Color="#059669" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="MultiPaymentGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#FF6B73" Offset="0"/>
            <GradientStop Color="#009FFF" Offset="1"/>
        </LinearGradientBrush>

        <!-- Success Gradient -->
        <LinearGradientBrush x:Key="SuccessGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#4CAF50" Offset="0"/>
            <GradientStop Color="#8BC34A" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
            <Setter Property="Margin" Value="0,0,0,24"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#40000000" BlurRadius="25" ShadowDepth="10" Direction="270"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Enhanced ComboBox Style -->
        <Style x:Key="EnhancedComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="Height" Value="60"/>
            <Setter Property="Padding" Value="18,18,18,10"/>
            <Setter Property="Margin" Value="0,0,0,24"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="#667eea"/>
            <Setter Property="materialDesign:TextFieldAssist.RippleOnFocusEnabled" Value="True"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.75"/>
        </Style>

        <!-- Enhanced TextBox Style -->
        <Style x:Key="EnhancedTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="Height" Value="60"/>
            <Setter Property="Padding" Value="18,18,18,10"/>
            <Setter Property="Margin" Value="0,0,0,24"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="#667eea"/>
            <Setter Property="materialDesign:TextFieldAssist.RippleOnFocusEnabled" Value="True"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.75"/>
        </Style>

        <!-- Enhanced DatePicker Style -->
        <Style x:Key="EnhancedDatePickerStyle" TargetType="DatePicker" BasedOn="{StaticResource MaterialDesignOutlinedDatePicker}">
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="Height" Value="60"/>
            <Setter Property="Padding" Value="18,18,18,10"/>
            <Setter Property="Margin" Value="0,0,0,24"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="#667eea"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.75"/>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="52"/>
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Padding" Value="28,0"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="10"/>
        </Style>


        <!-- Enhanced Invoice Item Style -->
        <Style x:Key="EnhancedInvoiceItemStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="#E5E7EB"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#40000000" BlurRadius="8" ShadowDepth="2" Direction="270" Opacity="0.1"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="#667eea"/>
                    <Setter Property="Background" Value="#F8F9FF"/>
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="#667eea" BlurRadius="12" ShadowDepth="3" Direction="270" Opacity="0.2"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <!-- Main Container -->
    <Border Background="#F8F9FF" CornerRadius="24" Margin="24"
            MinWidth="1300" MaxWidth="1700" MinHeight="900" MaxHeight="1100"
            HorizontalAlignment="Center" VerticalAlignment="Center">
        <Border.Effect>
            <DropShadowEffect Color="#40000000" Opacity="0.3" BlurRadius="40" ShadowDepth="20" Direction="270"/>
        </Border.Effect>

        <Border.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.4"/>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                       From="0.9" To="1.0" Duration="0:0:0.4">
                            <DoubleAnimation.EasingFunction>
                                <BackEase EasingMode="EaseOut" Amplitude="0.2"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                       From="0.9" To="1.0" Duration="0:0:0.4">
                            <DoubleAnimation.EasingFunction>
                                <BackEase EasingMode="EaseOut" Amplitude="0.2"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Border.Triggers>

        <Border.RenderTransform>
            <ScaleTransform ScaleX="1" ScaleY="1"/>
        </Border.RenderTransform>
        <Border.RenderTransformOrigin>
            <Point X="0.5" Y="0.5"/>
        </Border.RenderTransformOrigin>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Modern Header -->
            <Border Grid.Row="0" Background="{StaticResource PrimaryGradientBrush}" CornerRadius="24,24,0,0" Padding="36,28">
                <Border.Effect>
                    <DropShadowEffect Color="#667eea" BlurRadius="20" ShadowDepth="8" Direction="270" Opacity="0.4"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="CreditCardMultiple" Width="36" Height="36"
                                               Foreground="White" VerticalAlignment="Center" Margin="0,0,16,0"/>
                        <StackPanel>
                            <TextBlock Text="إضافة وصل دفع متعدد" FontSize="26" FontWeight="Bold" Foreground="White"/>
                            <TextBlock Text="دفع عدة فواتير بوصل واحد مع خصم ذكي" FontSize="16" Foreground="#E8EAF6" Margin="0,6,0,0"/>
                        </StackPanel>
                    </StackPanel>

                    <Button Grid.Column="1" x:Name="CloseButton" Click="CloseButton_Click"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="44" Height="44" Foreground="White" ToolTip="إغلاق">
                        <materialDesign:PackIcon Kind="Close" Width="26" Height="26"/>
                        <Button.Effect>
                            <DropShadowEffect Color="#40000000" BlurRadius="8" ShadowDepth="2" Direction="270" Opacity="0.3"/>
                        </Button.Effect>
                    </Button>
                </Grid>
            </Border>

            <!-- Content -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="36,28">
                <StackPanel>
                    <!-- Supplier Selection -->
                    <materialDesign:Card Style="{StaticResource ModernCardStyle}">
                        <Grid Margin="28">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                    <materialDesign:PackIcon Kind="Domain" Width="28" Height="28"
                                                           Foreground="#667eea" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                    <TextBlock Text="اختيار المورد"
                                              FontSize="18" FontWeight="SemiBold"
                                              Foreground="#374151" VerticalAlignment="Center"/>
                                </StackPanel>

                                <ComboBox x:Name="SupplierComboBox"
                                         Style="{StaticResource EnhancedComboBoxStyle}"
                                         materialDesign:HintAssist.Hint="اختر المورد أو ابحث..."
                                         DisplayMemberPath="Name"
                                         SelectedValuePath="Id"
                                         IsEditable="True"
                                         IsTextSearchEnabled="True"
                                         StaysOpenOnEdit="True"
                                         TextSearch.TextPath="Name"
                                         SelectionChanged="SupplierComboBox_SelectionChanged"/>
                            </StackPanel>
                        </Grid>
                    </materialDesign:Card>

                    <!-- Invoices Selection -->
                    <materialDesign:Card Style="{StaticResource ModernCardStyle}">
                        <Grid Margin="28">
                            <StackPanel>
                                <Grid Margin="0,0,0,20">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FileDocumentMultiple" Width="28" Height="28"
                                                               Foreground="#667eea" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                        <TextBlock Text="اختيار الفواتير"
                                                  FontSize="18" FontWeight="SemiBold"
                                                  Foreground="#374151" VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                                        <Button x:Name="SelectAllButton" Content="تحديد الكل"
                                               Style="{StaticResource MaterialDesignOutlinedButton}"
                                               FontSize="13" Height="40" Padding="16,0" Margin="0,0,12,0"
                                               Click="SelectAllButton_Click"/>
                                        <Button x:Name="ClearAllButton" Content="إلغاء الكل"
                                               Style="{StaticResource MaterialDesignOutlinedButton}"
                                               FontSize="13" Height="40" Padding="16,0"
                                               Click="ClearAllButton_Click"/>
                                    </StackPanel>
                                </Grid>

                                <ScrollViewer x:Name="InvoicesScrollViewer" MaxHeight="450" VerticalScrollBarVisibility="Auto">
                                    <ItemsControl x:Name="InvoicesItemsControl">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Border Style="{StaticResource EnhancedInvoiceItemStyle}">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <CheckBox Grid.Column="0" IsChecked="{Binding IsSelected}"
                                                             VerticalAlignment="Center" Margin="0,0,16,0"
                                                             Checked="InvoiceCheckBox_Changed"
                                                             Unchecked="InvoiceCheckBox_Changed"
                                                             Style="{StaticResource MaterialDesignCheckBox}"/>

                                                    <StackPanel Grid.Column="1">
                                                        <TextBlock Text="{Binding InvoiceNumber}" FontWeight="SemiBold" FontSize="16" Foreground="#374151"/>
                                                        <TextBlock Text="{Binding InvoiceDate, StringFormat=yyyy/MM/dd}"
                                                                  FontSize="14" Foreground="#6B7280" Margin="0,4,0,0"/>
                                                    </StackPanel>

                                                    <StackPanel Grid.Column="2" HorizontalAlignment="Left">
                                                        <TextBlock Text="{Binding RemainingAmount, StringFormat={}{0:N0} د.ع}"
                                                                  FontWeight="SemiBold" FontSize="16" Foreground="#EF4444"/>
                                                        <TextBlock Text="{Binding StatusText}"
                                                                  FontSize="13" Foreground="#9CA3AF"/>
                                                    </StackPanel>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </ScrollViewer>
                            </StackPanel>
                        </Grid>
                    </materialDesign:Card>

                    <!-- Payment Summary -->
                    <materialDesign:Card x:Name="PaymentSummaryPanel" Style="{StaticResource ModernCardStyle}" Visibility="Collapsed">
                        <Grid Margin="28">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                    <materialDesign:PackIcon Kind="CurrencyUsd" Width="28" Height="28"
                                                           Foreground="#10B981" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                    <TextBlock Text="ملخص الدفع"
                                              FontSize="18" FontWeight="SemiBold"
                                              Foreground="#374151" VerticalAlignment="Center"/>
                                </StackPanel>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Left Column -->
                                    <StackPanel Grid.Column="0" Margin="0,0,24,0">
                                        <Border Background="#F0F9FF" BorderBrush="#0EA5E9" BorderThickness="2" CornerRadius="12" Padding="20" Margin="0,0,0,16">
                                            <StackPanel>
                                                <TextBlock Text="عدد الفواتير" FontSize="14" Foreground="#0369A1" FontWeight="Medium"/>
                                                <TextBlock x:Name="InvoicesCountText" Text="0" FontSize="24" FontWeight="Bold" Foreground="#0EA5E9"/>
                                            </StackPanel>
                                        </Border>

                                        <Border Background="#FEF3C7" BorderBrush="#F59E0B" BorderThickness="2" CornerRadius="12" Padding="20">
                                            <StackPanel>
                                                <TextBlock Text="إجمالي المبلغ" FontSize="14" Foreground="#92400E" FontWeight="Medium"/>
                                                <TextBlock x:Name="TotalAmountText" Text="0 د.ع" FontSize="22" FontWeight="Bold" Foreground="#F59E0B"/>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>

                                    <!-- Right Column -->
                                    <StackPanel Grid.Column="1">
                                        <!-- Discount Section -->
                                        <Border Background="#F0FDF4" BorderBrush="#10B981" BorderThickness="2" CornerRadius="12" Padding="20" Margin="0,0,0,16">
                                            <StackPanel>
                                                <TextBlock Text="الخصم المكتسب" FontSize="14" Foreground="#047857" FontWeight="Medium" Margin="0,0,0,12"/>

                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBox x:Name="DiscountPercentageTextBox" Grid.Column="0"
                                                            Style="{StaticResource EnhancedTextBoxStyle}"
                                                            materialDesign:HintAssist.Hint="النسبة %"
                                                            FontSize="15" Margin="0,0,12,0"
                                                            TextChanged="DiscountPercentage_TextChanged"/>

                                                    <Button Grid.Column="1" Content="ذكي"
                                                           Style="{StaticResource MaterialDesignOutlinedButton}"
                                                           FontSize="13" Height="40" Padding="16,0"
                                                           Click="SmartDiscountButton_Click"/>
                                                </Grid>

                                                <TextBlock x:Name="DiscountAmountText" Text="0 د.ع"
                                                          FontSize="18" FontWeight="SemiBold" Foreground="#10B981"
                                                          HorizontalAlignment="Center" Margin="0,12,0,0"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- Final Amount -->
                                        <Border Background="{StaticResource SecondaryGradientBrush}" CornerRadius="12" Padding="20">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="المبلغ النهائي" FontSize="14" Foreground="White" HorizontalAlignment="Center" FontWeight="Medium"/>
                                                <TextBlock x:Name="FinalAmountText" Text="0 د.ع"
                                                          FontSize="24" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Grid>
                    </materialDesign:Card>

                    <!-- Payment Details -->
                    <materialDesign:Card x:Name="PaymentDetailsPanel" Style="{StaticResource ModernCardStyle}" Visibility="Collapsed">
                        <Grid Margin="28">
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                                    <materialDesign:PackIcon Kind="FileDocumentEdit" Width="28" Height="28"
                                                           Foreground="#667eea" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                    <TextBlock Text="تفاصيل الدفع"
                                              FontSize="18" FontWeight="SemiBold"
                                              Foreground="#374151" VerticalAlignment="Center"/>
                                </StackPanel>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Left Column -->
                                    <StackPanel Grid.Column="0" Margin="0,0,24,0">
                                        <TextBox x:Name="ReceiptNumberTextBox"
                                                Style="{StaticResource EnhancedTextBoxStyle}"
                                                materialDesign:HintAssist.Hint="رقم الوصل"/>

                                        <DatePicker x:Name="PaymentDatePicker"
                                                   Style="{StaticResource EnhancedDatePickerStyle}"
                                                   materialDesign:HintAssist.Hint="تاريخ الدفع"/>

                                        <ComboBox x:Name="PaymentMethodComboBox"
                                                 Style="{StaticResource EnhancedComboBoxStyle}"
                                                 materialDesign:HintAssist.Hint="طريقة الدفع">
                                            <ComboBoxItem Content="نقدي"/>
                                            <ComboBoxItem Content="بطاقة بنكية"/>
                                        </ComboBox>
                                    </StackPanel>

                                    <!-- Right Column -->
                                    <StackPanel Grid.Column="1">
                                        <TextBox x:Name="NotesTextBox"
                                                Style="{StaticResource EnhancedTextBoxStyle}"
                                                materialDesign:HintAssist.Hint="ملاحظات"
                                                AcceptsReturn="True"
                                                TextWrapping="Wrap"
                                                Height="140"/>

                                        <!-- Attachment Section -->
                                        <Border Background="#F8FAFC" BorderBrush="#E2E8F0" BorderThickness="2" CornerRadius="12" Padding="20" Margin="0,0,0,0">
                                            <StackPanel>
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                                    <materialDesign:PackIcon Kind="Attachment" Width="20" Height="20"
                                                                           Foreground="#667eea" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                                    <TextBlock Text="مرفق الوصل" FontSize="15" FontWeight="SemiBold"
                                                             Foreground="#374151" VerticalAlignment="Center"/>
                                                </StackPanel>

                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBox x:Name="AttachmentPathTextBox" Grid.Column="0"
                                                            Style="{StaticResource EnhancedTextBoxStyle}"
                                                            IsReadOnly="True"
                                                            materialDesign:HintAssist.Hint="لم يتم اختيار ملف"
                                                            Margin="0,0,16,0"/>

                                                    <Button Grid.Column="1" Content="اختيار ملف"
                                                           Style="{StaticResource MaterialDesignOutlinedButton}"
                                                           FontSize="14" Height="48" Padding="20,0"
                                                           Click="SelectAttachmentButton_Click"/>
                                                </Grid>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Grid>
                    </materialDesign:Card>
                </StackPanel>
            </ScrollViewer>

            <!-- Footer Buttons -->
            <Border Grid.Row="2" Background="White" BorderBrush="#E5E7EB" BorderThickness="0,2,0,0" CornerRadius="0,0,24,24" Padding="36,28" MinHeight="90">
                <Border.Effect>
                    <DropShadowEffect Color="#40000000" BlurRadius="15" ShadowDepth="8" Direction="90" Opacity="0.1"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Status Text -->
                    <TextBlock x:Name="StatusTextBlock" Grid.Column="0"
                              Text="اختر المورد والفواتير للمتابعة"
                              FontSize="16" Foreground="#6B7280"
                              VerticalAlignment="Center"/>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <Button x:Name="CancelButton" Content="إلغاء"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="White"
                               Foreground="#6B7280"
                               BorderBrush="#D1D5DB"
                               BorderThickness="2"
                               FontSize="15" Height="56" Padding="32,0" Margin="0,0,16,0"
                               Click="CancelButton_Click"/>

                        <Button x:Name="SaveButton"
                               Style="{StaticResource ModernButtonStyle}"
                               Background="{StaticResource PrimaryGradientBrush}"
                               Foreground="White"
                               FontSize="15" Height="56" Padding="32,0"
                               IsEnabled="False"
                               Click="SaveButton_Click">
                            <Button.Effect>
                                <DropShadowEffect Color="#667eea" BlurRadius="12" ShadowDepth="4" Direction="270" Opacity="0.3"/>
                            </Button.Effect>
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ContentSave" Width="20" Height="20" Margin="0,0,10,0"/>
                                    <TextBlock Text="حفظ الوصل المتعدد" FontWeight="Medium"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Border>
</UserControl>
