using System;
using System.IO;
using System.Threading.Tasks;
using Xunit;
using Microsoft.Extensions.Logging;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Tests.Services
{
    /// <summary>
    /// اختبارات أساسية لنظام التخزين السحابي
    /// </summary>
    public class CloudSystemBasicTests : IDisposable
    {
        private readonly string _testDirectory;

        public CloudSystemBasicTests()
        {
            _testDirectory = Path.Combine(Path.GetTempPath(), "CloudSystemBasicTests");
            Directory.CreateDirectory(_testDirectory);
        }

        [Fact]
        public void CloudFileEncryptionService_ShouldInitialize()
        {
            // Arrange & Act
            var service = new CloudFileEncryptionService();

            // Assert
            Assert.NotNull(service);
        }

        [Fact]
        public void CloudPerformanceMonitorService_ShouldInitialize()
        {
            // Arrange & Act
            var service = new CloudPerformanceMonitorService();

            // Assert
            Assert.NotNull(service);
            Assert.NotNull(service.CurrentStats);
            Assert.Equal(0, service.CurrentStats.TotalOperations);
        }

        [Fact]
        public void GoogleDriveService_ShouldInitialize()
        {
            // Arrange & Act
            var service = new GoogleDriveService();

            // Assert
            Assert.NotNull(service);
        }

        [Fact]
        public async Task CloudFileEncryptionService_ShouldValidateInputs()
        {
            // Arrange
            var service = new CloudFileEncryptionService();

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(async () =>
            {
                await service.EncryptFileForCloudAsync("");
            });

            await Assert.ThrowsAsync<ArgumentException>(async () =>
            {
                await service.EncryptFileForCloudAsync(null!);
            });

            await Assert.ThrowsAsync<FileNotFoundException>(async () =>
            {
                await service.EncryptFileForCloudAsync("nonexistent_file.txt");
            });
        }

        [Fact]
        public void CloudPerformanceMonitorService_ShouldValidateInputs()
        {
            // Arrange
            var service = new CloudPerformanceMonitorService();

            // Act & Assert
            Assert.Throws<ArgumentException>(() =>
            {
                service.StartOperation("");
            });

            Assert.Throws<ArgumentException>(() =>
            {
                service.StartOperation(null!);
            });
        }

        [Fact]
        public void PerformanceTracker_ShouldTrackBasicInfo()
        {
            // Arrange
            var service = new CloudPerformanceMonitorService();

            // Act
            using var tracker = service.StartOperation("TestOperation", "test.txt", 1024);

            // Assert
            Assert.Equal("TestOperation", tracker.OperationType);
            Assert.Equal("test.txt", tracker.FileName);
            Assert.Equal(1024, tracker.FileSize);
            Assert.True(tracker.Success);
            Assert.True(tracker.ElapsedTime.TotalMilliseconds >= 0);
        }

        [Fact]
        public void PerformanceTracker_ShouldHandleFailure()
        {
            // Arrange
            var service = new CloudPerformanceMonitorService();

            // Act
            using var tracker = service.StartOperation("TestOperation", "test.txt", 1024);
            tracker.MarkAsFailure("Test error");

            // Assert
            Assert.False(tracker.Success);
            Assert.Equal("Test error", tracker.ErrorMessage);
        }

        [Fact]
        public async Task CloudFileEncryptionService_ShouldCalculateHash()
        {
            // Arrange
            var service = new CloudFileEncryptionService();
            var testFile = Path.Combine(_testDirectory, "hash_test.txt");
            File.WriteAllText(testFile, "Test content for hash calculation");

            // Act
            var hash1 = await service.CalculateFileHashAsync(testFile);
            var hash2 = await service.CalculateFileHashAsync(testFile);

            // Assert
            Assert.NotNull(hash1);
            Assert.NotNull(hash2);
            Assert.Equal(hash1, hash2);
            Assert.True(hash1.Length > 0);

            // Cleanup
            File.Delete(testFile);
        }

        [Fact]
        public async Task CloudFileEncryptionService_ShouldEncryptAndDecrypt()
        {
            // Arrange
            var service = new CloudFileEncryptionService();
            var originalFile = Path.Combine(_testDirectory, "original.txt");
            var originalContent = "This is test content for encryption";
            File.WriteAllText(originalFile, originalContent);

            // Act
            var encryptedFile = await service.EncryptFileForCloudAsync(originalFile);
            var outputDir = Path.Combine(_testDirectory, "decrypted");
            Directory.CreateDirectory(outputDir);
            var decryptedFile = await service.DecryptFileFromCloudAsync(encryptedFile, outputDir);

            // Assert
            Assert.True(File.Exists(encryptedFile));
            Assert.True(File.Exists(decryptedFile));
            
            var decryptedContent = File.ReadAllText(decryptedFile);
            Assert.Equal(originalContent, decryptedContent);

            // Cleanup
            File.Delete(originalFile);
            File.Delete(encryptedFile);
            File.Delete(decryptedFile);
        }

        [Fact]
        public void CloudPerformanceMonitorService_ShouldGenerateReport()
        {
            // Arrange
            var service = new CloudPerformanceMonitorService();

            // Act
            using (var tracker = service.StartOperation("Upload", "file1.txt", 1024))
            {
                // Simulate some work
                System.Threading.Thread.Sleep(10);
            }

            using (var tracker = service.StartOperation("Download", "file2.txt", 2048))
            {
                tracker.MarkAsFailure("Test failure");
            }

            var report = service.GetPerformanceReport(TimeSpan.FromMinutes(1));

            // Assert
            Assert.NotNull(report);
            Assert.True(report.TotalOperations >= 2);
            Assert.True(report.SuccessfulOperations >= 1);
            Assert.True(report.FailedOperations >= 1);
            Assert.Contains("Upload", report.OperationsByType.Keys);
            Assert.Contains("Download", report.OperationsByType.Keys);
        }

        [Fact]
        public void CloudPerformanceMonitorService_ShouldClearStats()
        {
            // Arrange
            var service = new CloudPerformanceMonitorService();

            // Add some operations
            using (var tracker = service.StartOperation("TestOp", "file.txt", 1024))
            {
                // Simulate work
            }

            // Act
            service.ClearStats();

            // Assert
            var stats = service.CurrentStats;
            Assert.Equal(0, stats.TotalOperations);
            Assert.Equal(0, stats.RecentOperations);
            
            var recentOps = service.GetRecentOperations(10);
            Assert.Empty(recentOps);
        }

        [Fact]
        public async Task CloudPerformanceMonitorService_ShouldExportStats()
        {
            // Arrange
            var service = new CloudPerformanceMonitorService();
            var exportPath = Path.Combine(_testDirectory, "stats_export.json");

            // Add some test data
            using (var tracker = service.StartOperation("Export", "test.txt", 1024))
            {
                System.Threading.Thread.Sleep(5);
            }

            // Act
            var resultPath = await service.ExportStatsAsync(exportPath);

            // Assert
            Assert.Equal(exportPath, resultPath);
            Assert.True(File.Exists(exportPath));
            
            var content = File.ReadAllText(exportPath);
            Assert.Contains("TotalOperations", content);
            Assert.Contains("GeneratedAt", content);

            // Cleanup
            File.Delete(exportPath);
        }

        public void Dispose()
        {
            try
            {
                if (Directory.Exists(_testDirectory))
                {
                    Directory.Delete(_testDirectory, true);
                }
            }
            catch
            {
                // تجاهل أخطاء التنظيف
            }
        }
    }
}
