<UserControl x:Class="HR_InvoiceArchiver.Pages.CloudStoragePage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:controls="clr-namespace:HR_InvoiceArchiver.Controls"
             FlowDirection="RightToLeft"
             Background="Transparent">

    <UserControl.Resources>
        <!-- Page Header Style -->
        <Style x:Key="PageHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="28"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>

        <!-- Breadcrumb Style -->
        <Style x:Key="BreadcrumbStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#6C757D"/>
            <Setter Property="Margin" Value="0,0,0,30"/>
        </Style>
    </UserControl.Resources>

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
            <materialDesign:PackIcon Kind="CloudUpload" 
                                   Width="36" Height="36" 
                                   Foreground="#2196F3"
                                   VerticalAlignment="Center"/>
            <TextBlock Text="التخزين السحابي" 
                      Style="{StaticResource PageHeaderStyle}"
                      Margin="15,0,0,0"
                      VerticalAlignment="Center"/>
        </StackPanel>

        <!-- Breadcrumb -->
        <TextBlock Grid.Row="1" 
                  Text="الرئيسية > التخزين السحابي"
                  Style="{StaticResource BreadcrumbStyle}"/>

        <!-- Cloud Storage Control -->
        <ScrollViewer Grid.Row="2" 
                     VerticalScrollBarVisibility="Auto" 
                     HorizontalScrollBarVisibility="Disabled">
            <controls:CloudStorageControl x:Name="CloudStorageControlInstance"/>
        </ScrollViewer>
    </Grid>
</UserControl>
