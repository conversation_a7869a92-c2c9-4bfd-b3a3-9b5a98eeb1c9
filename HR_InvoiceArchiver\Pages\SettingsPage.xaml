<UserControl x:Class="HR_InvoiceArchiver.Pages.SettingsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:controls="clr-namespace:HR_InvoiceArchiver.Controls"
             FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Enhanced Modern Header -->
        <Border Grid.Row="0"
                Background="{StaticResource SidebarGradient}"
                Margin="0,0,0,8"
                CornerRadius="0,0,20,20">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="3"/>
            </Border.Effect>

            <Grid Margin="24,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Icon with glow effect -->
                <Border Grid.Column="0"
                       Background="#30FFFFFF"
                       Width="50" Height="50"
                       CornerRadius="25"
                       VerticalAlignment="Center">
                    <Border.Effect>
                        <DropShadowEffect Color="White" Opacity="0.3" BlurRadius="8" ShadowDepth="0"/>
                    </Border.Effect>
                    <materialDesign:PackIcon Kind="Settings"
                                           Width="28" Height="28"
                                           Foreground="White"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"/>
                </Border>

                <StackPanel Grid.Column="1" Margin="20,0,0,0" VerticalAlignment="Center">
                    <TextBlock Text="إعدادات النظام"
                             FontSize="24"
                             FontWeight="Bold"
                             Foreground="White">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.3" BlurRadius="4" ShadowDepth="1"/>
                        </TextBlock.Effect>
                    </TextBlock>
                    <TextBlock Text="إدارة وتخصيص جميع إعدادات التطبيق"
                             FontSize="14"
                             FontWeight="Medium"
                             Foreground="#E0FFFFFF"
                             Margin="0,4,0,0">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.2" BlurRadius="3" ShadowDepth="1"/>
                        </TextBlock.Effect>
                    </TextBlock>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <!-- Enhanced Action Buttons -->
                    <Button x:Name="ImportButton"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="45" Height="35"
                            Margin="5,0"
                            ToolTip="استيراد الإعدادات"
                            Click="ImportButton_Click">
                        <materialDesign:PackIcon Kind="Import"
                                               Width="18" Height="18"
                                               Foreground="White"/>
                    </Button>

                    <Button x:Name="ExportButton"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="45" Height="35"
                            Margin="5,0"
                            ToolTip="تصدير الإعدادات"
                            Click="ExportButton_Click">
                        <materialDesign:PackIcon Kind="Export"
                                               Width="18" Height="18"
                                               Foreground="White"/>
                    </Button>

                    <Button x:Name="ResetButton"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="45" Height="35"
                            Margin="5,0"
                            ToolTip="إعادة تعيين الإعدادات"
                            Click="ResetButton_Click">
                        <materialDesign:PackIcon Kind="Restore"
                                               Width="18" Height="18"
                                               Foreground="White"/>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Enhanced Settings Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <TabControl x:Name="SettingsTabControl"
                      Style="{StaticResource MaterialDesignTabControl}"
                      Margin="20,8,20,8"
                      Background="Transparent">
                <TabControl.Resources>
                    <!-- Enhanced Tab Style -->
                    <Style TargetType="TabItem" BasedOn="{StaticResource MaterialDesignTabItem}">
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="Padding" Value="16,12"/>
                        <Setter Property="Margin" Value="4,0"/>
                    </Style>
                </TabControl.Resources>

                <!-- General Settings -->
                <TabItem Header="عام">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Settings" Margin="0,0,8,0"/>
                                <TextBlock Text="عام"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>

                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                        <StackPanel>
                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <TextBlock Text="معلومات التطبيق"
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             Margin="0,0,0,16"/>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <TextBox x:Name="ApplicationNameTextBox"
                                               Grid.Row="0" Grid.Column="0"
                                               materialDesign:HintAssist.Hint="اسم التطبيق"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="0,0,8,16"/>

                                        <TextBox x:Name="CompanyNameTextBox"
                                               Grid.Row="0" Grid.Column="1"
                                               materialDesign:HintAssist.Hint="اسم الشركة"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="8,0,0,16"/>

                                        <TextBox x:Name="CompanyAddressTextBox"
                                               Grid.Row="1" Grid.Column="0"
                                               materialDesign:HintAssist.Hint="عنوان الشركة"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="0,0,8,16"/>

                                        <TextBox x:Name="CompanyPhoneTextBox"
                                               Grid.Row="1" Grid.Column="1"
                                               materialDesign:HintAssist.Hint="هاتف الشركة"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="8,0,0,16"/>

                                        <TextBox x:Name="CompanyEmailTextBox"
                                               Grid.Row="2" Grid.ColumnSpan="2"
                                               materialDesign:HintAssist.Hint="بريد الشركة الإلكتروني"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="0,0,0,16"/>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>

                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <TextBlock Text="إعدادات الواجهة"
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             Margin="0,0,0,16"/>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <ComboBox x:Name="LanguageComboBox"
                                                Grid.Row="0" Grid.Column="0"
                                                materialDesign:HintAssist.Hint="اللغة"
                                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                Margin="0,0,8,16">
                                            <ComboBoxItem Content="العربية" Tag="ar-SA"/>
                                            <ComboBoxItem Content="English" Tag="en-US"/>
                                        </ComboBox>

                                        <ComboBox x:Name="ThemeComboBox"
                                                Grid.Row="0" Grid.Column="1"
                                                materialDesign:HintAssist.Hint="السمة"
                                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                Margin="8,0,0,16">
                                            <ComboBoxItem Content="فاتح" Tag="Light"/>
                                            <ComboBoxItem Content="داكن" Tag="Dark"/>
                                        </ComboBox>

                                        <CheckBox x:Name="EnableNotificationsCheckBox"
                                                Grid.Row="1" Grid.Column="0"
                                                Content="تفعيل الإشعارات"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="0,0,8,0"/>

                                        <CheckBox x:Name="EnableSoundsCheckBox"
                                                Grid.Row="1" Grid.Column="1"
                                                Content="تفعيل الأصوات"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="8,0,0,0"/>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Performance Settings Card -->
                            <materialDesign:Card Padding="16" Margin="0,16,0,0">
                                <StackPanel>
                                    <TextBlock Text="إعدادات الأداء والتحسين"
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             Margin="0,0,0,16"/>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <CheckBox x:Name="EnablePerformanceMonitoringCheckBox"
                                                Grid.Row="0" Grid.Column="0"
                                                Content="تفعيل مراقبة الأداء"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="0,0,8,16"/>

                                        <CheckBox x:Name="EnableCachingCheckBox"
                                                Grid.Row="0" Grid.Column="1"
                                                Content="تفعيل التخزين المؤقت"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="8,0,0,16"/>

                                        <TextBox x:Name="MaxLogEntriesTextBox"
                                               Grid.Row="1" Grid.Column="0"
                                               materialDesign:HintAssist.Hint="الحد الأقصى لإدخالات السجل"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="0,0,8,16"/>

                                        <TextBox x:Name="LogRetentionDaysTextBox"
                                               Grid.Row="1" Grid.Column="1"
                                               materialDesign:HintAssist.Hint="مدة الاحتفاظ بالسجلات (أيام)"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="8,0,0,16"/>

                                        <CheckBox x:Name="EnableAutoOptimizationCheckBox"
                                                Grid.Row="2" Grid.Column="0"
                                                Content="تفعيل التحسين التلقائي"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="0,0,8,0"/>

                                        <TextBox x:Name="CacheSizeLimitTextBox"
                                               Grid.Row="2" Grid.Column="1"
                                               materialDesign:HintAssist.Hint="حد حجم التخزين المؤقت (MB)"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="8,0,0,0"/>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- Database Settings -->
                <TabItem Header="قاعدة البيانات">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Database" Margin="0,0,8,0"/>
                                <TextBlock Text="قاعدة البيانات"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>

                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                        <StackPanel>
                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                        <materialDesign:PackIcon Kind="Database"
                                                               Width="24" Height="24"
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,12,0"/>
                                        <TextBlock Text="إعدادات قاعدة البيانات والنسخ الاحتياطي"
                                                 Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <Grid Grid.Row="0" Margin="0,0,0,16">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBox x:Name="DatabasePathTextBox"
                                                   Grid.Column="0"
                                                   materialDesign:HintAssist.Hint="مسار قاعدة البيانات"
                                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                   IsReadOnly="True"
                                                   Margin="0,0,8,0"/>

                                            <Button x:Name="BrowseDatabaseButton"
                                                  Grid.Column="1"
                                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                                  Click="BrowseDatabaseButton_Click">
                                                <materialDesign:PackIcon Kind="FolderOpen"/>
                                            </Button>
                                        </Grid>

                                        <Grid Grid.Row="1">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <CheckBox x:Name="EnableDatabaseBackupCheckBox"
                                                    Grid.Column="0"
                                                    Content="تفعيل النسخ الاحتياطي"
                                                    Style="{StaticResource MaterialDesignCheckBox}"
                                                    Margin="0,0,8,16"/>

                                            <CheckBox x:Name="EnableDatabaseEncryptionCheckBox"
                                                    Grid.Column="1"
                                                    Content="تفعيل التشفير"
                                                    Style="{StaticResource MaterialDesignCheckBox}"
                                                    Margin="8,0,0,16"/>
                                        </Grid>

                                        <Grid Grid.Row="2">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBox x:Name="BackupIntervalTextBox"
                                                   Grid.Column="0"
                                                   materialDesign:HintAssist.Hint="فترة النسخ الاحتياطي (ساعات)"
                                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                   Margin="0,0,8,0"/>

                                            <TextBox x:Name="MaxBackupFilesTextBox"
                                                   Grid.Column="1"
                                                   materialDesign:HintAssist.Hint="الحد الأقصى لملفات النسخ"
                                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                   Margin="8,0,0,0"/>
                                        </Grid>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Backup Management Card -->
                            <materialDesign:Card Padding="16" Margin="0,16,0,0">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                        <materialDesign:PackIcon Kind="Backup"
                                                               Width="24" Height="24"
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,12,0"/>
                                        <TextBlock Text="إدارة النسخ الاحتياطي"
                                                 Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBox x:Name="BackupLocationTextBox"
                                               Grid.Column="0"
                                               materialDesign:HintAssist.Hint="مجلد النسخ الاحتياطي"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               IsReadOnly="True"
                                               Margin="0,0,8,0"/>

                                        <Button x:Name="BrowseBackupLocationButton"
                                              Grid.Column="1"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              ToolTip="تصفح المجلد"
                                              Margin="0,0,8,0"
                                              Click="BrowseBackupLocationButton_Click">
                                            <materialDesign:PackIcon Kind="FolderOpen" Width="18" Height="18"/>
                                        </Button>

                                        <Button x:Name="BackupNowButton"
                                              Grid.Column="2"
                                              Style="{StaticResource MaterialDesignRaisedButton}"
                                              Click="BackupNowButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Backup" Margin="0,0,8,0"/>
                                                <TextBlock Text="نسخ احتياطي الآن"/>
                                            </StackPanel>
                                        </Button>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- Enhanced Cloud Storage Settings -->
                <TabItem Header="التخزين السحابي">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CloudUpload" Margin="0,0,8,0"/>
                                <TextBlock Text="التخزين السحابي"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>

                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="0">
                        <!-- Embed CloudStorageControl -->
                        <controls:CloudStorageControl x:Name="CloudStorageControlInstance"/>
                    </ScrollViewer>
                </TabItem>

                <!-- Security Settings -->
                <TabItem Header="الأمان">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Security" Margin="0,0,8,0"/>
                                <TextBlock Text="الأمان"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>

                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                        <StackPanel>
                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                        <materialDesign:PackIcon Kind="Security"
                                                               Width="24" Height="24"
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,12,0"/>
                                        <TextBlock Text="إعدادات الأمان والحماية"
                                                 Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <CheckBox x:Name="EnableAuditLogCheckBox"
                                                Grid.Row="0" Grid.Column="0"
                                                Content="تفعيل سجل المراجعة"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="0,0,8,16"/>

                                        <CheckBox x:Name="EnableDataEncryptionCheckBox"
                                                Grid.Row="0" Grid.Column="1"
                                                Content="تفعيل تشفير البيانات"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="8,0,0,16"/>

                                        <TextBox x:Name="SessionTimeoutTextBox"
                                               Grid.Row="1" Grid.Column="0"
                                               materialDesign:HintAssist.Hint="مهلة انتهاء الجلسة (دقائق)"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="0,0,8,16"/>

                                        <TextBox x:Name="MaxLoginAttemptsTextBox"
                                               Grid.Row="1" Grid.Column="1"
                                               materialDesign:HintAssist.Hint="الحد الأقصى لمحاولات تسجيل الدخول"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="8,0,0,16"/>

                                        <CheckBox x:Name="RequirePasswordOnStartupCheckBox"
                                                Grid.Row="2" Grid.Column="0"
                                                Content="طلب كلمة مرور عند بدء التشغيل"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="0,0,8,0"/>

                                        <TextBox x:Name="LockoutDurationTextBox"
                                               Grid.Row="2" Grid.Column="1"
                                               materialDesign:HintAssist.Hint="مدة القفل (دقائق)"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="8,0,0,0"/>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>



                <!-- System Info -->
                <TabItem Header="معلومات النظام">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Information" Margin="0,0,8,0"/>
                                <TextBlock Text="معلومات النظام"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>

                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                        <StackPanel>
                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                        <materialDesign:PackIcon Kind="Information"
                                                               Width="24" Height="24"
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,12,0"/>
                                        <TextBlock Text="معلومات التطبيق والنظام"
                                                 Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <TextBlock Text="تفاصيل شاملة حول التطبيق والنظام"
                                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                                             Opacity="0.7"
                                             Margin="0,0,0,16"/>

                                    <Grid x:Name="SystemInfoGrid">
                                        <!-- سيتم ملء هذا الجزء برمجياً -->
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>
            </TabControl>
        </ScrollViewer>

        <!-- Enhanced Action Footer -->
        <Border Grid.Row="2"
                Background="{StaticResource SidebarGradient}"
                Margin="0,8,0,0"
                CornerRadius="20,20,0,0">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="-3"/>
            </Border.Effect>

            <Grid Margin="24,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Quick Actions -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="TestConnectionButton"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="45" Height="35"
                            Margin="5,0"
                            ToolTip="اختبار الاتصال"
                            Click="TestConnectionButton_Click">
                        <materialDesign:PackIcon Kind="TestTube"
                                               Width="18" Height="18"
                                               Foreground="White"/>
                    </Button>

                    <Button x:Name="QuickBackupButton"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="45" Height="35"
                            Margin="5,0"
                            ToolTip="نسخ احتياطي الآن"
                            Click="QuickBackupButton_Click">
                        <materialDesign:PackIcon Kind="Backup"
                                               Width="18" Height="18"
                                               Foreground="White"/>
                    </Button>
                </StackPanel>

                <!-- Status Info -->
                <StackPanel Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                    <TextBlock Text="جاهز للحفظ"
                             FontSize="16"
                             FontWeight="SemiBold"
                             Foreground="White"
                             HorizontalAlignment="Center">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.3" BlurRadius="3" ShadowDepth="1"/>
                        </TextBlock.Effect>
                    </TextBlock>
                    <TextBlock Text="تأكد من مراجعة جميع الإعدادات"
                             FontSize="12"
                             Foreground="#E0FFFFFF"
                             HorizontalAlignment="Center"
                             Margin="0,2,0,0">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.2" BlurRadius="2" ShadowDepth="1"/>
                        </TextBlock.Effect>
                    </TextBlock>
                </StackPanel>

                <!-- Main Actions -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="CancelButton"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Width="100" Height="40"
                            Margin="8,0"
                            Click="CancelButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Cancel"
                                                   Width="16" Height="16"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="إلغاء"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="SaveButton"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Width="100" Height="40"
                            Margin="8,0"
                            Background="#4CAF50"
                            Click="SaveButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ContentSave"
                                                   Width="16" Height="16"
                                                   Foreground="White"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="حفظ"
                                     Foreground="White"
                                     FontWeight="Bold"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
