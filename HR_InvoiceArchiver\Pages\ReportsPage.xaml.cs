using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Models;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Pages
{
    public partial class ReportsPage : UserControl
    {
        private readonly ToastService _toastService;
        private readonly IInvoiceService _invoiceService;
        private readonly ISupplierService _supplierService;
        private readonly IPaymentService _paymentService;

        public ReportsPage()
        {
            InitializeComponent();
            _toastService = new ToastService();

            // Initialize services using dependency injection
            var serviceProvider = App.ServiceProvider;
            _invoiceService = serviceProvider.GetRequiredService<IInvoiceService>();
            _supplierService = serviceProvider.GetRequiredService<ISupplierService>();
            _paymentService = serviceProvider.GetRequiredService<IPaymentService>();

            InitializeAsync();
        }

        private void InitializeAsync()
        {
            try
            {
                System.Console.WriteLine("ReportsPage: Initializing...");
                
                // Set initial status
                if (StatusInfoText != null)
                {
                    StatusInfoText.Text = "جاهز لعرض التقارير";
                }
                
                if (CurrentReportText != null)
                {
                    CurrentReportText.Text = "اختر تبويب التقرير المطلوب";
                }
                
                if (LastUpdateText != null)
                {
                    LastUpdateText.Text = $"آخر تحديث: {DateTime.Now:HH:mm:ss}";
                }
                
                System.Console.WriteLine("ReportsPage: Initialized successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"ReportsPage: Exception in InitializeAsync: {ex.Message}");
                _toastService?.ShowError("خطأ", $"فشل في تهيئة صفحة التقارير: {ex.Message}");
            }
        }

        // Header Button Events
        private async void RefreshDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Console.WriteLine("ReportsPage: RefreshDataButton_Click triggered");
                RefreshDataButton.IsEnabled = false;
                
                _toastService?.ShowInfo("تحديث", "جاري تحديث بيانات التقارير...");
                
                // محاكاة عملية التحديث
                await Task.Delay(2000);
                
                _toastService?.ShowSuccess("تم التحديث", "تم تحديث جميع بيانات التقارير بنجاح");
                System.Console.WriteLine("ReportsPage: Data refreshed successfully");
                
                // Update status
                if (LastUpdateText != null)
                {
                    LastUpdateText.Text = $"آخر تحديث: {DateTime.Now:HH:mm:ss}";
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"ReportsPage: Exception in RefreshDataButton_Click: {ex.Message}");
                _toastService?.ShowError("خطأ", $"فشل في تحديث البيانات: {ex.Message}");
            }
            finally
            {
                RefreshDataButton.IsEnabled = true;
            }
        }

        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Console.WriteLine("ReportsPage: ExportButton_Click triggered");
                
                ExportButton.IsEnabled = false;
                _toastService?.ShowInfo("تصدير", "جاري تحضير ملف التصدير...");

                // محاكاة عملية التحضير
                await Task.Delay(1000);

                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "تصدير التقرير",
                    Filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv|PDF Files (*.pdf)|*.pdf|HTML Files (*.html)|*.html|All Files (*.*)|*.*",
                    DefaultExt = "xlsx",
                    FileName = $"تقرير_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    try
                    {
                        // محاكاة عملية التصدير
                        await Task.Delay(2000);
                        
                        _toastService?.ShowSuccess("تم التصدير", $"تم حفظ التقرير في: {saveFileDialog.FileName}");
                        System.Console.WriteLine($"ReportsPage: Report exported to: {saveFileDialog.FileName}");
                    }
                    catch (Exception ex)
                    {
                        System.Console.WriteLine($"ReportsPage: Export error: {ex.Message}");
                        _toastService?.ShowError("خطأ في التصدير", ex.Message);
                    }
                }
                else
                {
                    _toastService?.ShowInfo("تم الإلغاء", "تم إلغاء عملية التصدير");
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"ReportsPage: Exception in ExportButton_Click: {ex.Message}");
                _toastService?.ShowError("خطأ", $"فشل في تصدير التقرير: {ex.Message}");
            }
            finally
            {
                ExportButton.IsEnabled = true;
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Console.WriteLine("ReportsPage: PrintButton_Click triggered");
                
                // محاكاة عملية الطباعة
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    _toastService?.ShowSuccess("تم الإرسال للطباعة", "تم إرسال التقرير للطابعة بنجاح");
                    System.Console.WriteLine("ReportsPage: Report sent to printer");
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"ReportsPage: Exception in PrintButton_Click: {ex.Message}");
                _toastService?.ShowError("خطأ", $"فشل في طباعة التقرير: {ex.Message}");
            }
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Console.WriteLine("ReportsPage: SettingsButton_Click triggered");
                _toastService?.ShowInfo("إعدادات", "ستتم إضافة إعدادات التقارير قريباً");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"ReportsPage: Exception in SettingsButton_Click: {ex.Message}");
                _toastService?.ShowError("خطأ", $"فشل في فتح الإعدادات: {ex.Message}");
            }
        }

        // Tab Control Event Handler
        private void ReportsTabControl_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (ReportsTabControl.SelectedItem is TabItem selectedTab)
                {
                    System.Console.WriteLine($"ReportsPage: Tab changed to: {selectedTab.Name}");
                    
                    // Update status bar
                    if (CurrentReportText != null)
                    {
                        CurrentReportText.Text = $"التبويب النشط: {selectedTab.Header}";
                    }
                    
                    if (LastUpdateText != null)
                    {
                        LastUpdateText.Text = $"آخر تحديث: {DateTime.Now:HH:mm:ss}";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"ReportsPage: Exception in ReportsTabControl_SelectionChanged: {ex.Message}");
            }
        }

        // Load Invoices Button
        private async void LoadInvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Console.WriteLine("ReportsPage: LoadInvoicesButton_Click triggered");
                LoadInvoicesButton.IsEnabled = false;

                _toastService?.ShowInfo("تحميل", "جاري تحميل بيانات الفواتير...");

                // تحميل البيانات الحقيقية من قاعدة البيانات
                var invoices = await _invoiceService.GetAllInvoicesAsync();

                // تحويل البيانات للعرض في الجدول
                var invoicesData = invoices.Select(invoice => new
                {
                    InvoiceNumber = invoice.InvoiceNumber,
                    Date = invoice.InvoiceDate.ToString("yyyy-MM-dd"),
                    SupplierName = invoice.Supplier?.Name ?? "غير محدد",
                    Amount = $"{invoice.Amount:N0} د.ع",
                    Status = GetInvoiceStatusText(invoice.Status)
                }).ToList();

                // حفظ البيانات الأصلية للتصفية
                _originalInvoicesData = invoicesData.Cast<object>().ToList();
                InvoicesDataGrid.ItemsSource = invoicesData;

                _toastService?.ShowSuccess("تم التحميل", $"تم تحميل {invoicesData.Count} فاتورة بنجاح");
                System.Console.WriteLine($"ReportsPage: {invoicesData.Count} invoices loaded successfully");

                // Update status
                if (StatusInfoText != null)
                {
                    StatusInfoText.Text = $"تم تحميل {invoicesData.Count} فاتورة";
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"ReportsPage: Exception in LoadInvoicesButton_Click: {ex.Message}");
                _toastService?.ShowError("خطأ", $"فشل في تحميل بيانات الفواتير: {ex.Message}");
            }
            finally
            {
                LoadInvoicesButton.IsEnabled = true;
            }
        }

        // Load Payments Button
        private async void LoadPaymentsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Console.WriteLine("ReportsPage: LoadPaymentsButton_Click triggered");
                LoadPaymentsButton.IsEnabled = false;

                _toastService?.ShowInfo("تحميل", "جاري تحميل بيانات المدفوعات...");

                // تحميل البيانات الحقيقية من قاعدة البيانات
                var payments = await _paymentService.GetAllPaymentsAsync();

                // تحويل البيانات للعرض في الجدول
                var paymentsData = payments.Select(payment => new
                {
                    ReceiptNumber = payment.ReceiptNumber,
                    Date = payment.PaymentDate.ToString("yyyy-MM-dd"),
                    SupplierName = payment.Invoice?.Supplier?.Name ?? "غير محدد",
                    Amount = $"{payment.Amount:N0} د.ع",
                    PaymentMethod = GetPaymentMethodText(payment.Method)
                }).ToList();

                // حفظ البيانات الأصلية للتصفية
                _originalPaymentsData = paymentsData.Cast<object>().ToList();
                PaymentsDataGrid.ItemsSource = paymentsData;

                _toastService?.ShowSuccess("تم التحميل", $"تم تحميل {paymentsData.Count} مدفوعة بنجاح");
                System.Console.WriteLine($"ReportsPage: {paymentsData.Count} payments loaded successfully");

                // Update status
                if (StatusInfoText != null)
                {
                    StatusInfoText.Text = $"تم تحميل {paymentsData.Count} مدفوعة";
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"ReportsPage: Exception in LoadPaymentsButton_Click: {ex.Message}");
                _toastService?.ShowError("خطأ", $"فشل في تحميل بيانات المدفوعات: {ex.Message}");
            }
            finally
            {
                LoadPaymentsButton.IsEnabled = true;
            }
        }

        // Load Suppliers Button
        private async void LoadSuppliersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Console.WriteLine("ReportsPage: LoadSuppliersButton_Click triggered");
                LoadSuppliersButton.IsEnabled = false;

                _toastService?.ShowInfo("تحميل", "جاري تحميل بيانات الموردين...");

                // تحميل البيانات الحقيقية من قاعدة البيانات
                var suppliers = await _supplierService.GetAllSuppliersAsync();

                // تحويل البيانات للعرض في الجدول مع حساب الإحصائيات
                var suppliersData = suppliers.Select(supplier =>
                {
                    var invoiceCount = supplier.Invoices?.Count ?? 0;
                    var totalAmount = supplier.Invoices?.Sum(i => i.Amount) ?? 0;
                    var paidAmount = supplier.Invoices?.SelectMany(i => i.Payments ?? new List<Payment>()).Sum(p => p.Amount) ?? 0;
                    var outstandingAmount = totalAmount - paidAmount;

                    return new
                    {
                        SupplierName = supplier.Name,
                        InvoiceCount = invoiceCount,
                        TotalAmount = $"{totalAmount:N0} د.ع",
                        PaidAmount = $"{paidAmount:N0} د.ع",
                        OutstandingAmount = $"{outstandingAmount:N0} د.ع"
                    };
                }).ToList();

                SuppliersDataGrid.ItemsSource = suppliersData;

                _toastService?.ShowSuccess("تم التحميل", $"تم تحميل {suppliersData.Count} مورد بنجاح");
                System.Console.WriteLine($"ReportsPage: {suppliersData.Count} suppliers loaded successfully");

                // Update status
                if (StatusInfoText != null)
                {
                    StatusInfoText.Text = $"تم تحميل {suppliersData.Count} مورد";
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"ReportsPage: Exception in LoadSuppliersButton_Click: {ex.Message}");
                _toastService?.ShowError("خطأ", $"فشل في تحميل بيانات الموردين: {ex.Message}");
            }
            finally
            {
                LoadSuppliersButton.IsEnabled = true;
            }
        }

        // Load Monthly Button
        private async void LoadMonthlyButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Console.WriteLine("ReportsPage: LoadMonthlyButton_Click triggered");
                LoadMonthlyButton.IsEnabled = false;

                _toastService?.ShowInfo("تحميل", "جاري تحميل التقرير الشهري...");

                // الحصول على الشهر والسنة المحددين
                var selectedMonth = MonthSelector.SelectedIndex + 1; // يناير = 1
                var selectedYear = int.Parse(((ComboBoxItem)YearSelector.SelectedItem)?.Content?.ToString() ?? "2024");

                // تحميل البيانات الحقيقية من قاعدة البيانات
                var invoices = await _invoiceService.GetAllInvoicesAsync();
                var payments = await _paymentService.GetAllPaymentsAsync();

                // فلترة البيانات حسب الشهر والسنة المحددين
                var monthlyInvoices = invoices.Where(i =>
                    i.InvoiceDate.Month == selectedMonth &&
                    i.InvoiceDate.Year == selectedYear).ToList();

                var monthlyPayments = payments.Where(p =>
                    p.PaymentDate.Month == selectedMonth &&
                    p.PaymentDate.Year == selectedYear).ToList();

                // إنشاء التقرير اليومي
                var monthlyData = GenerateRealMonthlyData(monthlyInvoices, monthlyPayments, selectedYear, selectedMonth);
                MonthlyDataGrid.ItemsSource = monthlyData;

                // تحديث بطاقات الملخص بالبيانات الحقيقية
                UpdateRealMonthlySummary(monthlyInvoices, monthlyPayments);

                _toastService?.ShowSuccess("تم التحميل", $"تم تحميل التقرير الشهري ({monthlyData.Count} يوم) بنجاح");
                System.Console.WriteLine($"ReportsPage: Monthly data loaded successfully ({monthlyData.Count} days)");

                // Update status
                if (StatusInfoText != null)
                {
                    StatusInfoText.Text = $"تم تحميل التقرير الشهري - {selectedMonth:00}/{selectedYear}";
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"ReportsPage: Exception in LoadMonthlyButton_Click: {ex.Message}");
                _toastService?.ShowError("خطأ", $"فشل في تحميل التقرير الشهري: {ex.Message}");
            }
            finally
            {
                LoadMonthlyButton.IsEnabled = true;
            }
        }

        // Helper methods for real data processing
        private string GetInvoiceStatusText(InvoiceStatus status)
        {
            return status switch
            {
                InvoiceStatus.Unpaid => "غير مسددة",
                InvoiceStatus.PartiallyPaid => "تسديد جزئي",
                InvoiceStatus.Paid => "مسددة",
                InvoiceStatus.PaidWithDiscount => "مسددة وبخصم",
                InvoiceStatus.Pending => "معلقة",
                _ => "غير محدد"
            };
        }

        private string GetPaymentMethodText(PaymentMethod method)
        {
            return method switch
            {
                PaymentMethod.Cash => "نقداً",
                PaymentMethod.CreditCard => "بطاقة ائتمان",
                PaymentMethod.Check => "شيك",
                PaymentMethod.BankTransfer => "تحويل بنكي",
                PaymentMethod.Other => "أخرى",
                _ => "غير محدد"
            };
        }

        private List<dynamic> GenerateRealMonthlyData(List<Invoice> monthlyInvoices, List<Payment> monthlyPayments, int year, int month)
        {
            var data = new List<dynamic>();
            var daysInMonth = DateTime.DaysInMonth(year, month);

            for (int day = 1; day <= daysInMonth; day++)
            {
                var currentDate = new DateTime(year, month, day);

                // الفواتير في هذا اليوم
                var dayInvoices = monthlyInvoices.Where(i => i.InvoiceDate.Date == currentDate.Date).ToList();
                var invoiceCount = dayInvoices.Count;
                var totalAmount = dayInvoices.Sum(i => i.Amount);

                // المدفوعات في هذا اليوم
                var dayPayments = monthlyPayments.Where(p => p.PaymentDate.Date == currentDate.Date).ToList();
                var paidAmount = dayPayments.Sum(p => p.Amount);

                var outstandingAmount = totalAmount - paidAmount;
                var collectionRate = totalAmount > 0 ? $"{(paidAmount * 100 / totalAmount):F1}%" : "0%";

                data.Add(new
                {
                    Day = $"اليوم {day}",
                    InvoiceCount = invoiceCount,
                    TotalAmount = $"{totalAmount:N0} د.ع",
                    PaidAmount = $"{paidAmount:N0} د.ع",
                    OutstandingAmount = $"{outstandingAmount:N0} د.ع",
                    CollectionRate = collectionRate
                });
            }

            return data;
        }

        private void UpdateRealMonthlySummary(List<Invoice> monthlyInvoices, List<Payment> monthlyPayments)
        {
            var totalInvoices = monthlyInvoices.Count;
            var totalAmount = monthlyInvoices.Sum(i => i.Amount);
            var totalPaid = monthlyPayments.Sum(p => p.Amount);
            var totalOutstanding = totalAmount - totalPaid;

            if (MonthlyInvoicesCount != null)
                MonthlyInvoicesCount.Text = totalInvoices.ToString();

            if (MonthlyTotalAmount != null)
                MonthlyTotalAmount.Text = $"{totalAmount:N0} د.ع";

            if (MonthlyPaidAmount != null)
                MonthlyPaidAmount.Text = $"{totalPaid:N0} د.ع";

            if (MonthlyOutstandingAmount != null)
                MonthlyOutstandingAmount.Text = $"{totalOutstanding:N0} د.ع";
        }

        // Search and Filter Event Handlers
        private void InvoicesSearchBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            var searchBox = sender as TextBox;
            if (searchBox != null)
            {
                ClearInvoicesSearchButton.Visibility = string.IsNullOrEmpty(searchBox.Text) ?
                    Visibility.Collapsed : Visibility.Visible;

                // Auto-search after 500ms delay
                if (_searchTimer != null)
                {
                    _searchTimer.Stop();
                }
                _searchTimer = new System.Windows.Threading.DispatcherTimer
                {
                    Interval = TimeSpan.FromMilliseconds(500)
                };
                _searchTimer.Tick += (s, args) =>
                {
                    _searchTimer.Stop();
                    ApplyInvoicesFilter();
                };
                _searchTimer.Start();
            }
        }

        private void ClearInvoicesSearchButton_Click(object sender, RoutedEventArgs e)
        {
            InvoicesSearchBox.Text = string.Empty;
            ClearInvoicesSearchButton.Visibility = Visibility.Collapsed;
            ApplyInvoicesFilter();
        }

        private void InvoicesStatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyInvoicesFilter();
        }

        private void InvoicesDateFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyInvoicesFilter();
        }

        private void RefreshInvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            LoadInvoicesButton_Click(sender, e);
        }

        private void PaymentsSearchBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            var searchBox = sender as TextBox;
            if (searchBox != null)
            {
                ClearPaymentsSearchButton.Visibility = string.IsNullOrEmpty(searchBox.Text) ?
                    Visibility.Collapsed : Visibility.Visible;

                // Auto-search after 500ms delay
                if (_paymentsSearchTimer != null)
                {
                    _paymentsSearchTimer.Stop();
                }
                _paymentsSearchTimer = new System.Windows.Threading.DispatcherTimer
                {
                    Interval = TimeSpan.FromMilliseconds(500)
                };
                _paymentsSearchTimer.Tick += (s, args) =>
                {
                    _paymentsSearchTimer.Stop();
                    ApplyPaymentsFilter();
                };
                _paymentsSearchTimer.Start();
            }
        }

        private void ClearPaymentsSearchButton_Click(object sender, RoutedEventArgs e)
        {
            PaymentsSearchBox.Text = string.Empty;
            ClearPaymentsSearchButton.Visibility = Visibility.Collapsed;
            ApplyPaymentsFilter();
        }

        private void PaymentsMethodFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyPaymentsFilter();
        }

        private void PaymentsDateFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyPaymentsFilter();
        }

        private void RefreshPaymentsButton_Click(object sender, RoutedEventArgs e)
        {
            LoadPaymentsButton_Click(sender, e);
        }

        // Filter Application Methods
        private System.Windows.Threading.DispatcherTimer? _searchTimer;
        private System.Windows.Threading.DispatcherTimer? _paymentsSearchTimer;
        private List<object>? _originalInvoicesData;
        private List<object>? _originalPaymentsData;

        private void ApplyInvoicesFilter()
        {
            if (_originalInvoicesData == null || InvoicesDataGrid == null) return;

            var filteredData = _originalInvoicesData.AsEnumerable();

            // Apply search filter
            var searchText = InvoicesSearchBox?.Text?.Trim().ToLower();
            if (!string.IsNullOrEmpty(searchText))
            {
                filteredData = filteredData.Where(item =>
                {
                    var invoiceNumber = GetPropertyValue(item, "InvoiceNumber")?.ToString()?.ToLower() ?? "";
                    var supplierName = GetPropertyValue(item, "SupplierName")?.ToString()?.ToLower() ?? "";
                    return invoiceNumber.Contains(searchText) || supplierName.Contains(searchText);
                });
            }

            // Apply status filter
            var statusFilter = (InvoicesStatusFilter?.SelectedItem as ComboBoxItem)?.Tag?.ToString();
            if (!string.IsNullOrEmpty(statusFilter) && statusFilter != "All")
            {
                filteredData = filteredData.Where(item =>
                {
                    var status = GetPropertyValue(item, "Status")?.ToString() ?? "";
                    return status.Contains(GetStatusFilterText(statusFilter));
                });
            }

            InvoicesDataGrid.ItemsSource = filteredData.ToList();
        }

        private void ApplyPaymentsFilter()
        {
            if (_originalPaymentsData == null || PaymentsDataGrid == null) return;

            var filteredData = _originalPaymentsData.AsEnumerable();

            // Apply search filter
            var searchText = PaymentsSearchBox?.Text?.Trim().ToLower();
            if (!string.IsNullOrEmpty(searchText))
            {
                filteredData = filteredData.Where(item =>
                {
                    var receiptNumber = GetPropertyValue(item, "ReceiptNumber")?.ToString()?.ToLower() ?? "";
                    var supplierName = GetPropertyValue(item, "SupplierName")?.ToString()?.ToLower() ?? "";
                    return receiptNumber.Contains(searchText) || supplierName.Contains(searchText);
                });
            }

            // Apply method filter
            var methodFilter = (PaymentsMethodFilter?.SelectedItem as ComboBoxItem)?.Tag?.ToString();
            if (!string.IsNullOrEmpty(methodFilter) && methodFilter != "All")
            {
                filteredData = filteredData.Where(item =>
                {
                    var paymentMethod = GetPropertyValue(item, "PaymentMethod")?.ToString() ?? "";
                    return paymentMethod.Contains(GetMethodFilterText(methodFilter));
                });
            }

            PaymentsDataGrid.ItemsSource = filteredData.ToList();
        }

        private string GetStatusFilterText(string filterTag)
        {
            return filterTag switch
            {
                "Unpaid" => "غير مسددة",
                "PartiallyPaid" => "تسديد جزئي",
                "Paid" => "مسددة",
                "PaidWithDiscount" => "مسددة وبخصم",
                "Pending" => "معلقة",
                _ => ""
            };
        }

        private string GetMethodFilterText(string filterTag)
        {
            return filterTag switch
            {
                "Cash" => "نقداً",
                "CreditCard" => "بطاقة ائتمان",
                "Check" => "شيك",
                "BankTransfer" => "تحويل بنكي",
                "Other" => "أخرى",
                _ => ""
            };
        }

        // Helper method to get property value from anonymous objects
        private object? GetPropertyValue(object obj, string propertyName)
        {
            try
            {
                var property = obj.GetType().GetProperty(propertyName);
                return property?.GetValue(obj);
            }
            catch
            {
                return null;
            }
        }
    }
}
