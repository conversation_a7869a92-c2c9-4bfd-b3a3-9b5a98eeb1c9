<UserControl x:Class="HR_InvoiceArchiver.Pages.InvoicesPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:controls="clr-namespace:HR_InvoiceArchiver.Controls"
             xmlns:converters="clr-namespace:HR_InvoiceArchiver.Converters"
             Background="#F8F9FF"
             FontFamily="{DynamicResource MaterialDesignFont}"
             TextElement.Foreground="{DynamicResource MaterialDesignBody}"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <!-- Converters -->
        <converters:FirstLetterConverter x:Key="FirstLetterConverter"/>
        <converters:StringNullOrEmptyConverter x:Key="StringNullOrEmptyConverter"/>

        <!-- Modern Gradient Brushes -->
        <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#10B981" Offset="0"/>
            <GradientStop Color="#059669" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#40000000" BlurRadius="20" ShadowDepth="8" Direction="270"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#40000000" BlurRadius="8" ShadowDepth="2" Direction="270"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Search Box Style -->
        <Style x:Key="SearchBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="45"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="10">
                            <Grid>
                                <ScrollViewer x:Name="PART_ContentHost"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                                <TextBlock x:Name="PlaceholderText"
                                         Text="🔍 البحث في الفواتير..."
                                         Foreground="#9E9E9E"
                                         VerticalAlignment="Center"
                                         Margin="{TemplateBinding Padding}"
                                         IsHitTestVisible="False"/>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="Text" Value="">
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                            </Trigger>
                            <Trigger Property="Text" Value="{x:Null}">
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                            </Trigger>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#667eea"/>
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Collapsed"/>
                            </Trigger>
                            <MultiTrigger>
                                <MultiTrigger.Conditions>
                                    <Condition Property="Text" Value=""/>
                                    <Condition Property="IsFocused" Value="False"/>
                                </MultiTrigger.Conditions>
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                            </MultiTrigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان والأزرار -->
        <materialDesign:Card Grid.Row="0" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
            <Grid Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- العنوان مع أيقونة -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="FileDocumentOutline"
                                           Width="32" Height="32"
                                           Foreground="{StaticResource PrimaryGradientBrush}"
                                           VerticalAlignment="Center"
                                           Margin="0,0,10,0"/>
                    <StackPanel>
                        <TextBlock Text="إدارة الفواتير"
                                   FontSize="24" FontWeight="Bold"
                                   Foreground="#2D3748"/>
                        <TextBlock Text="إدارة ومتابعة جميع الفواتير والمدفوعات"
                                   FontSize="14"
                                   Foreground="#718096"
                                   Margin="0,2,0,0"/>
                    </StackPanel>
                </StackPanel>

                <!-- الأزرار -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="AddInvoiceButton"
                            Click="AddInvoiceButton_Click"
                            Style="{StaticResource ModernButtonStyle}"
                            Background="{StaticResource PrimaryGradientBrush}"
                            Margin="8,0,0,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة فاتورة جديدة" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="ExportButton"
                            Click="ExportButton_Click"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            materialDesign:ButtonAssist.CornerRadius="8"
                            Height="40"
                            Padding="12,8"
                            Margin="8,0,0,0"
                            BorderBrush="{StaticResource PrimaryGradientBrush}"
                            Foreground="{StaticResource PrimaryGradientBrush}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileExcel" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="تصدير Excel" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="PrintButton"
                            Click="PrintButton_Click"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            materialDesign:ButtonAssist.CornerRadius="8"
                            Height="40"
                            Padding="12,8"
                            Margin="8,0,0,0"
                            BorderBrush="{StaticResource SecondaryGradientBrush}"
                            Foreground="{StaticResource SecondaryGradientBrush}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Printer" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="طباعة" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="RefreshButton"
                            Click="RefreshButton_Click"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            materialDesign:ButtonAssist.CornerRadius="8"
                            Height="40"
                            Padding="12,8"
                            Margin="8,0,0,0"
                            BorderBrush="{StaticResource SecondaryGradientBrush}"
                            Foreground="{StaticResource SecondaryGradientBrush}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- شريط البحث والفلاتر -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
            <Grid Margin="20,15,20,20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- شريط البحث -->
                <Grid Grid.Row="0" Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- مربع البحث -->
                    <TextBox Grid.Column="0" x:Name="SearchTextBox"
                             materialDesign:HintAssist.Hint="🔍 البحث في الفواتير (رقم الفاتورة، المورد، المبلغ...)"
                             Style="{StaticResource SearchBoxStyle}"
                             TextChanged="SearchTextBox_TextChanged"/>

                    <!-- زر البحث -->
                    <Button Grid.Column="1" x:Name="FilterButton"
                            Click="FilterButton_Click"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            materialDesign:ButtonAssist.CornerRadius="8"
                            Height="40"
                            Padding="12,8"
                            Margin="8,0,0,0"
                            BorderBrush="{StaticResource PrimaryGradientBrush}"
                            Foreground="{StaticResource PrimaryGradientBrush}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FilterVariant" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="فلترة متقدمة" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <!-- زر مسح الفلاتر -->
                    <Button Grid.Column="2" x:Name="ClearFiltersButton"
                            Click="ClearFiltersButton_Click"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            materialDesign:ButtonAssist.CornerRadius="8"
                            Height="40"
                            Padding="12,8"
                            Margin="8,0,0,0"
                            BorderBrush="{StaticResource SecondaryGradientBrush}"
                            Foreground="{StaticResource SecondaryGradientBrush}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FilterRemove" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="مسح الفلاتر" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>
                </Grid>

                <!-- فلاتر متقدمة -->
                <Expander Grid.Row="1" x:Name="AdvancedFiltersExpander"
                          Header="🔧 فلاتر متقدمة"
                          IsExpanded="False"
                          materialDesign:ExpanderAssist.HorizontalHeaderPadding="0"
                          FontSize="14" FontWeight="Medium">
                    <Grid Margin="0,15,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- فلتر الحالة -->
                        <StackPanel Grid.Column="0" Margin="0,0,15,0">
                            <TextBlock Text="الحالة" FontWeight="SemiBold" Margin="0,0,0,8" Foreground="#495057"/>
                            <ComboBox x:Name="StatusFilterComboBox"
                                     Height="40"
                                     materialDesign:HintAssist.Hint="جميع الحالات"
                                     Style="{StaticResource MaterialDesignComboBox}"/>
                        </StackPanel>

                        <!-- فلتر من تاريخ -->
                        <StackPanel Grid.Column="1" Margin="0,0,15,0">
                            <TextBlock Text="من تاريخ" FontWeight="SemiBold" Margin="0,0,0,8" Foreground="#495057"/>
                            <DatePicker x:Name="FromDatePicker"
                                       Height="40"
                                       materialDesign:HintAssist.Hint="من تاريخ"
                                       Style="{StaticResource MaterialDesignDatePicker}"/>
                        </StackPanel>

                        <!-- فلتر إلى تاريخ -->
                        <StackPanel Grid.Column="2" Margin="0,0,15,0">
                            <TextBlock Text="إلى تاريخ" FontWeight="SemiBold" Margin="0,0,0,8" Foreground="#495057"/>
                            <DatePicker x:Name="ToDatePicker"
                                       Height="40"
                                       materialDesign:HintAssist.Hint="إلى تاريخ"
                                       Style="{StaticResource MaterialDesignDatePicker}"/>
                        </StackPanel>

                        <!-- زر تطبيق الفلاتر -->
                        <StackPanel Grid.Column="3" VerticalAlignment="Bottom">
                            <Button x:Name="ApplyFiltersButton"
                                    Style="{StaticResource ModernButtonStyle}"
                                    Background="{StaticResource PrimaryGradientBrush}"
                                    Height="40">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Check" Width="16" Height="16" Margin="0,0,6,0"/>
                                    <TextBlock Text="تطبيق الفلاتر"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </Grid>
                </Expander>
            </Grid>
        </materialDesign:Card>

        <!-- جدول الفواتير -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- عنوان الجدول -->
                <Border Grid.Row="0"
                        Background="{StaticResource PrimaryGradientBrush}"
                        CornerRadius="8,8,0,0"
                        Padding="20,15">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="TableLarge"
                                               Width="24" Height="24"
                                               Foreground="White"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>
                        <TextBlock Text="قائمة الفواتير"
                                   FontSize="18"
                                   FontWeight="Bold"
                                   Foreground="White"
                                   VerticalAlignment="Center"/>
                        <TextBlock x:Name="TotalInvoicesCountTextBlock"
                                   Text="(0 فاتورة)"
                                   FontSize="14"
                                   Foreground="White"
                                   Opacity="0.8"
                                   VerticalAlignment="Center"
                                   Margin="10,0,0,0"/>
                    </StackPanel>
                </Border>

                <!-- الجدول -->
                <DataGrid x:Name="InvoicesDataGrid"
                          Grid.Row="1"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          SelectionMode="Single"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          materialDesign:DataGridAssist.CellPadding="12"
                          materialDesign:DataGridAssist.ColumnHeaderPadding="12"
                          FontSize="13"
                          FontFamily="Segoe UI"
                          Background="White"
                          RowBackground="White"
                          AlternatingRowBackground="#FAFBFC"
                          FlowDirection="RightToLeft"
                          MouseDoubleClick="InvoicesDataGrid_MouseDoubleClick">

                    <DataGrid.Columns>
                        <!-- رقم الفاتورة -->
                        <DataGridTextColumn Header="📄 رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="140">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Foreground" Value="#1976D2"/>
                                    <Setter Property="FlowDirection" Value="LeftToRight"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- المورد -->
                        <DataGridTextColumn Header="🏢 اسم المورد" Binding="{Binding Supplier.Name}" Width="180">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="FontWeight" Value="Medium"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Foreground" Value="#1976D2"/>
                                    <Setter Property="Margin" Value="0,0,15,0"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- تاريخ الفاتورة -->
                        <DataGridTextColumn Header="📅 تاريخ الفاتورة" Binding="{Binding InvoiceDate, StringFormat=yyyy/MM/dd}" Width="130">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Foreground" Value="#424242"/>
                                    <Setter Property="FlowDirection" Value="LeftToRight"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- المبلغ الإجمالي -->
                        <DataGridTextColumn Header="💰 المبلغ الإجمالي" Binding="{Binding Amount, StringFormat=N0}" Width="120">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Foreground" Value="#1976D2"/>
                                    <Setter Property="FlowDirection" Value="LeftToRight"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- المبلغ المدفوع -->
                        <DataGridTextColumn Header="✅ المدفوع" Binding="{Binding PaidAmount, StringFormat=N0}" Width="110">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Foreground" Value="#2E7D32"/>
                                    <Setter Property="FlowDirection" Value="LeftToRight"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- المبلغ المتبقي -->
                        <DataGridTextColumn Header="⏳ المتبقي" Binding="{Binding RemainingAmount, StringFormat=N0}" Width="110">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FlowDirection" Value="LeftToRight"/>
                                    <!-- اللون الافتراضي للمبلغ المتبقي (أحمر للمبالغ المستحقة) -->
                                    <Setter Property="Foreground" Value="#D32F2F"/>
                                    <Style.Triggers>
                                        <!-- إذا كان المبلغ المتبقي يساوي 0 - لون أخضر -->
                                        <DataTrigger Binding="{Binding RemainingAmount}" Value="0">
                                            <Setter Property="Foreground" Value="#2E7D32"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- الحالة -->
                        <DataGridTemplateColumn Header="📊 الحالة" Width="130" CanUserSort="False" CanUserReorder="False">
                            <DataGridTemplateColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTemplateColumn.HeaderStyle>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="15" Padding="10,5" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="5">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="Unpaid">
                                                        <Setter Property="Background" Value="#FFEBEE"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="PartiallyPaid">
                                                        <Setter Property="Background" Value="#FFF3E0"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="Paid">
                                                        <Setter Property="Background" Value="#E8F5E8"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="PaidWithDiscount">
                                                        <Setter Property="Background" Value="#E8F5E8"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock FontWeight="SemiBold" FontSize="12" HorizontalAlignment="Center" TextAlignment="Center">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Status}" Value="Unpaid">
                                                            <Setter Property="Text" Value="غير مسددة"/>
                                                            <Setter Property="Foreground" Value="#C62828"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="PartiallyPaid">
                                                            <Setter Property="Text" Value="مسددة جزئياً"/>
                                                            <Setter Property="Foreground" Value="#EF6C00"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="Paid">
                                                            <Setter Property="Text" Value="مسددة"/>
                                                            <Setter Property="Foreground" Value="#2E7D32"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="PaidWithDiscount">
                                                            <Setter Property="Text" Value="مسددة بخصم"/>
                                                            <Setter Property="Foreground" Value="#2E7D32"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- وصف الفاتورة -->
                        <DataGridTextColumn Header="📝 وصف الفاتورة" Binding="{Binding Description}" Width="*" MinWidth="180">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="12"/>
                                    <Setter Property="FontWeight" Value="Normal"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Foreground" Value="#424242"/>
                                    <Setter Property="Margin" Value="0,0,15,0"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                    <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                    <Setter Property="ToolTip" Value="{Binding Description}"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- الإجراءات -->
                        <DataGridTemplateColumn Header="⚙️ الإجراءات" Width="220" CanUserSort="False" CanUserReorder="False">
                            <DataGridTemplateColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTemplateColumn.HeaderStyle>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <!-- تعديل -->
                                        <Button ToolTip="تعديل الفاتورة"
                                               Style="{StaticResource MaterialDesignIconButton}"
                                               Width="32" Height="32"
                                               Background="#E8F5E8"
                                               Foreground="#2E7D32"
                                               Tag="{Binding}"
                                               Click="EditInvoice_Click"
                                               Margin="2">
                                            <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                        </Button>

                                        <!-- عرض التفاصيل -->
                                        <Button ToolTip="عرض تفاصيل الفاتورة"
                                               Style="{StaticResource MaterialDesignIconButton}"
                                               Width="32" Height="32"
                                               Background="#E3F2FD"
                                               Foreground="#1976D2"
                                               Tag="{Binding}"
                                               Click="ViewInvoiceDetails_Click"
                                               Margin="2">
                                            <materialDesign:PackIcon Kind="Eye" Width="16" Height="16"/>
                                        </Button>

                                        <!-- عرض المرفق -->
                                        <Button ToolTip="عرض مرفق الفاتورة"
                                               Width="32" Height="32"
                                               Background="#FFF3E0"
                                               Foreground="#F57C00"
                                               Tag="{Binding}"
                                               Click="ViewInvoiceAttachment_Click"
                                               Margin="2">
                                            <Button.Style>
                                                <Style TargetType="Button" BasedOn="{StaticResource MaterialDesignIconButton}">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding AttachmentPath}" Value="{x:Null}">
                                                            <Setter Property="Visibility" Value="Collapsed"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding AttachmentPath}" Value="">
                                                            <Setter Property="Visibility" Value="Collapsed"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Button.Style>
                                            <materialDesign:PackIcon Kind="Attachment" Width="16" Height="16"/>
                                        </Button>

                                        <!-- عرض مرفق الوصل -->
                                        <Button ToolTip="عرض مرفقات الوصولات"
                                               Width="32" Height="32"
                                               Background="#F3E5F5"
                                               Foreground="#7B1FA2"
                                               Tag="{Binding}"
                                               Click="ViewReceiptAttachment_Click"
                                               Margin="2">
                                            <Button.Style>
                                                <Style TargetType="Button" BasedOn="{StaticResource MaterialDesignIconButton}">
                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                    <Style.Triggers>
                                                        <!-- إظهار الزر فقط إذا كانت الفاتورة مدفوعة جزئياً أو كلياً -->
                                                        <DataTrigger Binding="{Binding Status}" Value="PartiallyPaid">
                                                            <Setter Property="Visibility" Value="Visible"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="Paid">
                                                            <Setter Property="Visibility" Value="Visible"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="PaidWithDiscount">
                                                            <Setter Property="Visibility" Value="Visible"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Button.Style>
                                            <materialDesign:PackIcon Kind="Receipt" Width="16" Height="16"/>
                                        </Button>

                                        <!-- حذف -->
                                        <Button ToolTip="حذف الفاتورة"
                                               Style="{StaticResource MaterialDesignIconButton}"
                                               Width="32" Height="32"
                                               Background="#FFEBEE"
                                               Foreground="#D32F2F"
                                               Tag="{Binding}"
                                               Click="DeleteInvoice_Click"
                                               Margin="2">
                                            <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- Loading Panel -->
        <Border x:Name="LoadingPanel"
                Grid.Row="2"
                Background="White"
                CornerRadius="10"
                Visibility="Collapsed">
            <materialDesign:Card HorizontalAlignment="Center" VerticalAlignment="Center"
                               Padding="40,30" Style="{StaticResource ModernCardStyle}">
                <StackPanel HorizontalAlignment="Center">
                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                Width="50" Height="50"
                                IsIndeterminate="True"
                                Foreground="{StaticResource PrimaryGradientBrush}"/>
                    <TextBlock Text="جاري تحميل بيانات الفواتير..."
                              FontSize="14" FontWeight="Medium"
                              Foreground="#2D3748"
                              HorizontalAlignment="Center"
                              Margin="0,15,0,0"/>
                </StackPanel>
            </materialDesign:Card>
        </Border>

        <!-- Empty State Panel -->
        <materialDesign:Card x:Name="EmptyStatePanel"
                           Grid.Row="2"
                           Style="{StaticResource ModernCardStyle}"
                           Visibility="Collapsed"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Margin="0,0,0,20"
                           Padding="60,40">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="FileDocumentOutline"
                                       Width="80" Height="80"
                                       Foreground="#CED4DA"
                                       HorizontalAlignment="Center"/>
                <TextBlock Text="لا توجد فواتير مسجلة"
                         FontSize="20" FontWeight="SemiBold"
                         Foreground="#6C757D"
                         HorizontalAlignment="Center"
                         Margin="0,20,0,8"/>
                <TextBlock Text="ابدأ بإضافة أول فاتورة لك لعرض البيانات هنا"
                         FontSize="14" Foreground="#ADB5BD"
                         HorizontalAlignment="Center"
                         TextWrapping="Wrap" TextAlignment="Center"
                         Margin="0,0,0,20"/>
                <Button Style="{StaticResource ModernButtonStyle}"
                        Background="{StaticResource PrimaryGradientBrush}"
                        Click="AddInvoiceButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,6,0"/>
                        <TextBlock Text="إضافة فاتورة جديدة"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </materialDesign:Card>

    </Grid>
</UserControl>
