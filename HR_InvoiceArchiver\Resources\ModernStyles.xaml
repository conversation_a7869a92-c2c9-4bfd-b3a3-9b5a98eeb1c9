<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- Modern Flat Color Palette -->
    <Color x:Key="PrimaryColor">#1E40AF</Color>
    <Color x:Key="AccentColor">#10B981</Color>
    <Color x:Key="ErrorColor">#EF4444</Color>
    <Color x:Key="WarningColor">#F59E0B</Color>
    <Color x:Key="InfoColor">#3B82F6</Color>
    <Color x:Key="SuccessColor">#10B981</Color>
    
    <!-- Surface Colors -->
    <Color x:Key="BackgroundColor">#F8FAFC</Color>
    <Color x:Key="SurfaceColor">#FFFFFF</Color>
    <Color x:Key="CardColor">#FFFFFF</Color>
    
    <!-- Text Colors -->
    <Color x:Key="OnPrimaryColor">#FFFFFF</Color>
    <Color x:Key="OnSurfaceColor">#1F2937</Color>
    <Color x:Key="OnBackgroundColor">#374151</Color>
    <Color x:Key="SecondaryTextColor">#6B7280</Color>
    
    <!-- Brushes -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor}"/>
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>
    
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
    <SolidColorBrush x:Key="CardBrush" Color="{StaticResource CardColor}"/>
    
    <SolidColorBrush x:Key="OnPrimaryBrush" Color="{StaticResource OnPrimaryColor}"/>
    <SolidColorBrush x:Key="OnSurfaceBrush" Color="{StaticResource OnSurfaceColor}"/>
    <SolidColorBrush x:Key="OnBackgroundBrush" Color="{StaticResource OnBackgroundColor}"/>
    <SolidColorBrush x:Key="SecondaryTextBrush" Color="{StaticResource SecondaryTextColor}"/>
    
    <!-- Gradients -->
    <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#667eea" Offset="0"/>
        <GradientStop Color="#764ba2" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#10B981" Offset="0"/>
        <GradientStop Color="#059669" Offset="1"/>
    </LinearGradientBrush>
    
    <!-- Modern Card Style -->
    <Style x:Key="ModernCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource CardBrush}"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Opacity="0.08" BlurRadius="12" ShadowDepth="3"/>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- Modern Button Style -->
    <Style x:Key="ModernPrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource OnPrimaryBrush}"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Padding" Value="24,0"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{StaticResource PrimaryColor}" Opacity="0.3" BlurRadius="8" ShadowDepth="2"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern Secondary Button Style -->
    <Style x:Key="ModernSecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Padding" Value="24,0"/>
    </Style>
    
    <!-- Modern TextBox Style -->
    <Style x:Key="ModernTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Padding" Value="12"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
    </Style>
    
    <!-- Modern ComboBox Style -->
    <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Padding" Value="12"/>
        <Setter Property="Height" Value="48"/>
    </Style>
    
    <!-- Modern DataGrid Style -->
    <Style x:Key="ModernDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="RowBackground" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="AlternatingRowBackground" Value="#F9FAFB"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HorizontalGridLinesBrush" Value="#E5E7EB"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="FontSize" Value="13"/>
    </Style>

</ResourceDictionary>
