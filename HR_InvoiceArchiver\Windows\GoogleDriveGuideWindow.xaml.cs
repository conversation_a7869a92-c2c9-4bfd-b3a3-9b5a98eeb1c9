using System;
using System.Diagnostics;
using System.Windows;

namespace HR_InvoiceArchiver.Windows
{
    /// <summary>
    /// نافذة دليل إعداد Google Drive
    /// </summary>
    public partial class GoogleDriveGuideWindow : Window
    {
        public GoogleDriveGuideWindow()
        {
            InitializeComponent();
        }

        /// <summary>
        /// فتح Google Cloud Console
        /// </summary>
        private void OpenGoogleCloudConsole_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = "https://console.cloud.google.com",
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في فتح الرابط: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// فتح مكتبة APIs
        /// </summary>
        private void OpenAPIsLibrary_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = "https://console.cloud.google.com/apis/library",
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في فتح الرابط: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
