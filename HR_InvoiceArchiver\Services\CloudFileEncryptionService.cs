using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// خدمة تشفير الملفات للتخزين السحابي
    /// </summary>
    public class CloudFileEncryptionService
    {
        private readonly ILogger<CloudFileEncryptionService>? _logger;
        private readonly string _defaultKey;

        public CloudFileEncryptionService(ILogger<CloudFileEncryptionService>? logger = null)
        {
            _logger = logger;
            _defaultKey = GenerateDefaultKey();
        }

        /// <summary>
        /// تشفير ملف قبل الرفع إلى السحابة
        /// </summary>
        public async Task<string> EncryptFileForCloudAsync(string inputFilePath, string? customKey = null)
        {
            // التحقق من صحة المدخلات
            if (string.IsNullOrWhiteSpace(inputFilePath))
                throw new ArgumentException("مسار الملف لا يمكن أن يكون فارغاً", nameof(inputFilePath));

            if (!File.Exists(inputFilePath))
                throw new FileNotFoundException($"الملف غير موجود: {inputFilePath}");

            try
            {
                var encryptedFilePath = Path.ChangeExtension(inputFilePath, ".encrypted");
                var keyToUse = customKey ?? _defaultKey;

                using var aes = Aes.Create();
                aes.Key = DeriveKeyFromPassword(keyToUse);
                aes.GenerateIV();

                using var inputFile = File.OpenRead(inputFilePath);
                using var outputFile = File.Create(encryptedFilePath);

                // كتابة IV في بداية الملف
                await outputFile.WriteAsync(aes.IV, 0, aes.IV.Length);

                // كتابة معلومات إضافية (اسم الملف الأصلي، الحجم، التاريخ)
                var metadata = CreateFileMetadata(inputFilePath);
                var metadataBytes = Encoding.UTF8.GetBytes(metadata);
                var metadataLength = BitConverter.GetBytes(metadataBytes.Length);
                
                await outputFile.WriteAsync(metadataLength, 0, metadataLength.Length);
                await outputFile.WriteAsync(metadataBytes, 0, metadataBytes.Length);

                // تشفير محتوى الملف
                using var encryptor = aes.CreateEncryptor();
                using var cryptoStream = new CryptoStream(outputFile, encryptor, CryptoStreamMode.Write);

                await inputFile.CopyToAsync(cryptoStream);
                await cryptoStream.FlushFinalBlockAsync();

                _logger?.LogInformation($"تم تشفير الملف بنجاح: {Path.GetFileName(inputFilePath)}");
                return encryptedFilePath;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تشفير الملف: {inputFilePath}");
                throw new InvalidOperationException($"فشل في تشفير الملف: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// فك تشفير ملف بعد التحميل من السحابة
        /// </summary>
        public async Task<string> DecryptFileFromCloudAsync(string encryptedFilePath, string outputDirectory, string? customKey = null)
        {
            try
            {
                var keyToUse = customKey ?? _defaultKey;

                using var aes = Aes.Create();
                aes.Key = DeriveKeyFromPassword(keyToUse);

                using var inputFile = File.OpenRead(encryptedFilePath);

                // قراءة IV
                var iv = new byte[16];
                await inputFile.ReadAsync(iv, 0, iv.Length);
                aes.IV = iv;

                // قراءة معلومات الملف
                var metadataLengthBytes = new byte[4];
                await inputFile.ReadAsync(metadataLengthBytes, 0, 4);
                var metadataLength = BitConverter.ToInt32(metadataLengthBytes, 0);

                var metadataBytes = new byte[metadataLength];
                await inputFile.ReadAsync(metadataBytes, 0, metadataLength);
                var metadata = Encoding.UTF8.GetString(metadataBytes);

                var originalFileName = ExtractOriginalFileName(metadata);
                var outputFilePath = Path.Combine(outputDirectory, originalFileName);

                // فك تشفير محتوى الملف
                using var decryptor = aes.CreateDecryptor();
                using var cryptoStream = new CryptoStream(inputFile, decryptor, CryptoStreamMode.Read);
                using var outputFile = File.Create(outputFilePath);

                await cryptoStream.CopyToAsync(outputFile);

                _logger?.LogInformation($"تم فك تشفير الملف بنجاح: {originalFileName}");
                return outputFilePath;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في فك تشفير الملف: {encryptedFilePath}");
                throw new InvalidOperationException($"فشل في فك تشفير الملف: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// التحقق من سلامة الملف المشفر
        /// </summary>
        public async Task<bool> ValidateEncryptedFileAsync(string encryptedFilePath, string originalFileHash)
        {
            try
            {
                // فك تشفير مؤقت للتحقق من السلامة
                var tempDir = Path.GetTempPath();
                var decryptedFile = await DecryptFileFromCloudAsync(encryptedFilePath, tempDir);

                var currentHash = await CalculateFileHashAsync(decryptedFile);
                var isValid = string.Equals(currentHash, originalFileHash, StringComparison.OrdinalIgnoreCase);

                // حذف الملف المؤقت
                if (File.Exists(decryptedFile))
                {
                    File.Delete(decryptedFile);
                }

                return isValid;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في التحقق من سلامة الملف: {encryptedFilePath}");
                return false;
            }
        }

        /// <summary>
        /// حساب hash للملف
        /// </summary>
        public async Task<string> CalculateFileHashAsync(string filePath)
        {
            try
            {
                using var sha256 = SHA256.Create();
                using var stream = File.OpenRead(filePath);
                var hashBytes = await Task.Run(() => sha256.ComputeHash(stream));
                return Convert.ToBase64String(hashBytes);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في حساب hash للملف: {filePath}");
                throw;
            }
        }

        /// <summary>
        /// ضغط الملف قبل التشفير (للملفات الكبيرة)
        /// </summary>
        public async Task<string> CompressAndEncryptFileAsync(string inputFilePath, string? customKey = null)
        {
            try
            {
                var compressedFilePath = Path.ChangeExtension(inputFilePath, ".compressed");
                
                // ضغط الملف أولاً
                using var inputFile = File.OpenRead(inputFilePath);
                using var compressedFile = File.Create(compressedFilePath);
                using var gzipStream = new System.IO.Compression.GZipStream(compressedFile, System.IO.Compression.CompressionMode.Compress);
                
                await inputFile.CopyToAsync(gzipStream);
                await gzipStream.FlushAsync();

                // ثم تشفير الملف المضغوط
                var encryptedFilePath = await EncryptFileForCloudAsync(compressedFilePath, customKey);

                // حذف الملف المضغوط المؤقت
                if (File.Exists(compressedFilePath))
                {
                    File.Delete(compressedFilePath);
                }

                _logger?.LogInformation($"تم ضغط وتشفير الملف: {Path.GetFileName(inputFilePath)}");
                return encryptedFilePath;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في ضغط وتشفير الملف: {inputFilePath}");
                throw;
            }
        }

        /// <summary>
        /// فك تشفير وإلغاء ضغط الملف
        /// </summary>
        public async Task<string> DecryptAndDecompressFileAsync(string encryptedFilePath, string outputDirectory, string? customKey = null)
        {
            try
            {
                // فك التشفير أولاً
                var tempDir = Path.GetTempPath();
                var compressedFilePath = await DecryptFileFromCloudAsync(encryptedFilePath, tempDir, customKey);

                // ثم إلغاء الضغط
                var originalFileName = Path.GetFileNameWithoutExtension(Path.GetFileNameWithoutExtension(compressedFilePath));
                var outputFilePath = Path.Combine(outputDirectory, originalFileName);

                using var compressedFile = File.OpenRead(compressedFilePath);
                using var gzipStream = new System.IO.Compression.GZipStream(compressedFile, System.IO.Compression.CompressionMode.Decompress);
                using var outputFile = File.Create(outputFilePath);

                await gzipStream.CopyToAsync(outputFile);

                // حذف الملف المضغوط المؤقت
                if (File.Exists(compressedFilePath))
                {
                    File.Delete(compressedFilePath);
                }

                _logger?.LogInformation($"تم فك تشفير وإلغاء ضغط الملف: {originalFileName}");
                return outputFilePath;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في فك تشفير وإلغاء ضغط الملف: {encryptedFilePath}");
                throw;
            }
        }

        /// <summary>
        /// توليد مفتاح افتراضي
        /// </summary>
        private string GenerateDefaultKey()
        {
            // في التطبيق الحقيقي، يجب الحصول على هذا من إعدادات آمنة
            // يمكن تحسينه بإضافة معرف الجهاز أو معلومات المستخدم
            var baseKey = "HR_InvoiceArchiver_DefaultKey_2024";
            var machineKey = Environment.MachineName;
            return $"{baseKey}_{machineKey}";
        }

        /// <summary>
        /// اشتقاق مفتاح من كلمة مرور
        /// </summary>
        private byte[] DeriveKeyFromPassword(string password)
        {
            using var pbkdf2 = new Rfc2898DeriveBytes(password, Encoding.UTF8.GetBytes("HR_Salt_2024"), 10000, HashAlgorithmName.SHA256);
            return pbkdf2.GetBytes(32); // 256-bit key
        }

        /// <summary>
        /// إنشاء معلومات الملف
        /// </summary>
        private string CreateFileMetadata(string filePath)
        {
            var fileInfo = new FileInfo(filePath);
            var metadata = new
            {
                OriginalName = fileInfo.Name,
                Size = fileInfo.Length,
                CreatedDate = fileInfo.CreationTime,
                ModifiedDate = fileInfo.LastWriteTime
            };

            return Newtonsoft.Json.JsonConvert.SerializeObject(metadata);
        }

        /// <summary>
        /// استخراج اسم الملف الأصلي من المعلومات
        /// </summary>
        private string ExtractOriginalFileName(string metadata)
        {
            try
            {
                dynamic metadataObj = Newtonsoft.Json.JsonConvert.DeserializeObject(metadata)!;
                return metadataObj.OriginalName;
            }
            catch
            {
                return "unknown_file";
            }
        }

        /// <summary>
        /// تنظيف الملفات المؤقتة
        /// </summary>
        public void CleanupTempFiles(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, $"فشل في حذف الملف المؤقت: {filePath}");
            }
        }
    }
}
