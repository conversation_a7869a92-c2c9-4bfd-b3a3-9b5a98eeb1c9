using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using Microsoft.Extensions.DependencyInjection;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace HR_InvoiceArchiver.Windows
{
    /// <summary>
    /// نافذة كشف حساب المورد
    /// </summary>
    public partial class SupplierStatementWindow : Window, INotifyPropertyChanged
    {
        private readonly IInvoiceService _invoiceService;
        private readonly IToastService _toastService;
        private readonly Supplier _supplier;
        private List<Invoice> _supplierInvoices = new();
        private List<Invoice> _filteredInvoices = new();

        public event PropertyChangedEventHandler? PropertyChanged;

        public SupplierStatementWindow(Supplier supplier)
        {
            InitializeComponent();
            
            // الحصول على الخدمات
            _invoiceService = App.ServiceProvider.GetRequiredService<IInvoiceService>();
            _toastService = App.ServiceProvider.GetRequiredService<IToastService>();
            
            _supplier = supplier ?? throw new ArgumentNullException(nameof(supplier));
            
            // تحديث معلومات المورد في الواجهة
            UpdateSupplierInfo();
            
            // تحديد التواريخ الافتراضية
            SetDefaultDateRange();
            
            // تحميل البيانات
            Loaded += async (s, e) => await LoadSupplierStatementAsync();
            
            DataContext = this;
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void UpdateSupplierInfo()
        {
            SupplierNameTextBlock.Text = _supplier.Name;
            SupplierDetailsTextBlock.Text = $"📞 {_supplier.Phone} | 📧 {_supplier.Email}";
            WindowTitleTextBlock.Text = $"كشف حساب المورد - {_supplier.Name}";
        }

        private void SetDefaultDateRange()
        {
            // تحديد آخر 6 أشهر كفترة افتراضية
            ToDatePicker.SelectedDate = DateTime.Now;
            FromDatePicker.SelectedDate = DateTime.Now.AddMonths(-6);
        }

        private async Task LoadSupplierStatementAsync()
        {
            try
            {
                ShowLoading(true);
                
                // تحميل جميع فواتير المورد
                var allInvoices = await _invoiceService.GetAllInvoicesAsync();
                _supplierInvoices = allInvoices.Where(i => i.SupplierId == _supplier.Id).ToList();
                
                // تطبيق الفلتر
                ApplyDateFilter();
                
                _toastService.ShowSuccess("تم التحميل", $"تم تحميل كشف حساب المورد '{_supplier.Name}' بنجاح");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في التحميل", $"حدث خطأ أثناء تحميل كشف الحساب: {ex.Message}");
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private void ApplyDateFilter()
        {
            var fromDate = FromDatePicker.SelectedDate ?? DateTime.MinValue;
            var toDate = ToDatePicker.SelectedDate ?? DateTime.MaxValue;
            
            // تطبيق الفلتر على التواريخ
            _filteredInvoices = _supplierInvoices
                .Where(i => i.InvoiceDate >= fromDate && i.InvoiceDate <= toDate)
                .OrderByDescending(i => i.InvoiceDate)
                .ToList();
            
            // تحديث الجدول
            InvoicesDataGrid.ItemsSource = _filteredInvoices;
            
            // تحديث الإحصائيات
            UpdateStatistics();
        }

        private void UpdateStatistics()
        {
            var totalInvoices = _filteredInvoices.Count;
            var totalAmount = _filteredInvoices.Sum(i => i.Amount);
            var paidAmount = _filteredInvoices.Sum(i => i.PaidAmount);
            var outstandingAmount = _filteredInvoices.Sum(i => i.RemainingAmount);

            // تحديث بطاقات الإحصائيات
            TotalInvoicesText.Text = totalInvoices.ToString("N0");
            TotalAmountText.Text = $"{totalAmount:N0} د.ع";
            PaidAmountText.Text = $"{paidAmount:N0} د.ع";
            OutstandingAmountText.Text = $"{outstandingAmount:N0} د.ع";

            // تحديث عداد الفواتير في الجدول
            InvoicesCountTextBlock.Text = $"({totalInvoices} فاتورة)";

            // تحديث الملخص السفلي
            SummaryTextBlock.Text = $"إجمالي: {totalInvoices} فاتورة | المبلغ: {totalAmount:N0} د.ع | المسدد: {paidAmount:N0} د.ع | المستحق: {outstandingAmount:N0} د.ع";
        }

        private void ShowLoading(bool show)
        {
            LoadingPanel.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
        }

        #region Event Handlers

        private async void FilterButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ShowLoading(true);

                // تطبيق الفلتر الجديد
                await Task.Run(() => ApplyDateFilter());

                _toastService.ShowInfo("تم التطبيق", "تم تطبيق فلتر التاريخ بنجاح");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في الفلتر", $"حدث خطأ أثناء تطبيق الفلتر: {ex.Message}");
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _toastService.ShowInfo("طباعة", "ميزة الطباعة قيد التطوير...");
                // TODO: Implement print functionality
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في الطباعة", ex.Message);
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _toastService.ShowInfo("تصدير", "ميزة التصدير قيد التطوير...");
                // TODO: Implement export functionality
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في التصدير", ex.Message);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                DragMove();
            }
        }

        #endregion
    }
}
