<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="HR_InvoiceArchiver.Pages.DashboardPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             FlowDirection="RightToLeft"
             FontFamily="{DynamicResource MaterialDesignFont}"
             TextElement.Foreground="{DynamicResource MaterialDesignBody}"
             Background="#F8F9FF">

    <UserControl.Resources>
        <ResourceDictionary>

            <!-- Modern Gradient Brushes - Updated Colors -->
            <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#667eea" Offset="0"/>
                <GradientStop Color="#764ba2" Offset="1"/>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#10B981" Offset="0"/>
                <GradientStop Color="#059669" Offset="1"/>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="WarningGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#FF6B6B" Offset="0"/>
                <GradientStop Color="#EE5A52" Offset="1"/>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="ErrorGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#8B5CF6" Offset="0"/>
                <GradientStop Color="#7C3AED" Offset="1"/>
            </LinearGradientBrush>

            <!-- Modern Card Style -->
            <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
                <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="#40000000" BlurRadius="20" ShadowDepth="8" Direction="270"/>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="#60000000" BlurRadius="25" ShadowDepth="10" Direction="270"/>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- Animated Statistics Card Style -->
            <Style x:Key="StatCardStyle" TargetType="materialDesign:Card" BasedOn="{StaticResource ModernCardStyle}">
                <Setter Property="RenderTransform">
                    <Setter.Value>
                        <ScaleTransform ScaleX="1" ScaleY="1"/>
                    </Setter.Value>
                </Setter>
                <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Trigger.EnterActions>
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                                                   To="1.05" Duration="0:0:0.2"/>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                                                   To="1.05" Duration="0:0:0.2"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </Trigger.EnterActions>
                        <Trigger.ExitActions>
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                                                   To="1" Duration="0:0:0.2"/>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                                                   To="1" Duration="0:0:0.2"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </Trigger.ExitActions>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- Modern Button Style -->
            <Style x:Key="ModernActionButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Height" Value="90"/>
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Padding" Value="20"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="RenderTransform">
                    <Setter.Value>
                        <ScaleTransform ScaleX="1" ScaleY="1"/>
                    </Setter.Value>
                </Setter>
                <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Trigger.EnterActions>
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                                                   To="1.03" Duration="0:0:0.15"/>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                                                   To="1.03" Duration="0:0:0.15"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </Trigger.EnterActions>
                        <Trigger.ExitActions>
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                                                   To="1" Duration="0:0:0.15"/>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                                                   To="1" Duration="0:0:0.15"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </Trigger.ExitActions>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- Loading Animation -->
            <Storyboard x:Key="LoadingAnimation" RepeatBehavior="Forever">
                <DoubleAnimation Storyboard.TargetName="LoadingIcon"
                               Storyboard.TargetProperty="RenderTransform.Angle"
                               From="0" To="360" Duration="0:0:2"/>
            </Storyboard>

            <!-- Fade In Animation -->
            <Storyboard x:Key="FadeInAnimation">
                <DoubleAnimation Storyboard.TargetProperty="Opacity"
                               From="0" To="1" Duration="0:0:0.5"/>
                <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Y"
                               From="20" To="0" Duration="0:0:0.5"/>
            </Storyboard>
        </ResourceDictionary>
    </UserControl.Resources>

    <!-- Main Container Grid -->
    <Grid>
        <!-- Loading Overlay -->
        <Grid x:Name="LoadingOverlay" Background="#80FFFFFF" Visibility="Collapsed" Panel.ZIndex="1000">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon x:Name="LoadingIcon" Kind="Loading" Width="48" Height="48"
                                       Foreground="{StaticResource PrimaryGradientBrush}">
                    <materialDesign:PackIcon.RenderTransform>
                        <RotateTransform Angle="0"/>
                    </materialDesign:PackIcon.RenderTransform>
                </materialDesign:PackIcon>
                <TextBlock Text="جاري تحميل البيانات..." Margin="0,16,0,0"
                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                         HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>

        <!-- Main Content -->
        <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <Grid Margin="24" x:Name="MainContent">
            <Grid.RenderTransform>
                <TranslateTransform Y="0"/>
            </Grid.RenderTransform>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Enhanced Header with Unified Design -->
            <Border Grid.Row="0"
                   Background="{StaticResource SidebarGradient}"
                   CornerRadius="20"
                   Margin="0,0,0,20"
                   Padding="28,20">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.08" BlurRadius="25" ShadowDepth="10"/>
                </Border.Effect>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Icon with Glow Effect -->
                    <Border Grid.Column="0"
                           Background="#30FFFFFF"
                           Width="48" Height="48"
                           CornerRadius="24"
                           VerticalAlignment="Center"
                           Margin="0,0,20,0">
                        <Border.Effect>
                            <DropShadowEffect Color="White" Opacity="0.3" BlurRadius="10" ShadowDepth="0"/>
                        </Border.Effect>
                        <materialDesign:PackIcon Kind="ViewDashboard"
                                               Width="24" Height="24"
                                               Foreground="White"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                    </Border>

                    <!-- Title and Description -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                        <TextBlock Text="📊 لوحة التحكم الرئيسية"
                                 FontSize="28"
                                 FontWeight="Bold"
                                 Foreground="White"
                                 Margin="0,0,0,4"/>
                        <TextBlock x:Name="WelcomeSubtitle"
                                 Text="إدارة شاملة وذكية لفواتيرك ومدفوعاتك مع تحليلات متقدمة"
                                 FontSize="16"
                                 Foreground="#E0E7FF"
                                 Opacity="0.9"/>
                        <TextBlock x:Name="LastUpdateText"
                                 Text="آخر تحديث: الآن"
                                 FontSize="14"
                                 Foreground="#E0E7FF"
                                 Opacity="0.7"
                                 Margin="0,4,0,0"/>
                    </StackPanel>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                        <Button x:Name="RefreshButton"
                               Style="{StaticResource MaterialDesignIconButton}"
                               Foreground="White"
                               ToolTip="تحديث البيانات"
                               Click="RefreshButton_Click"
                               Width="48" Height="48"
                               Margin="0,0,8,0">
                            <materialDesign:PackIcon Kind="Refresh" Width="24" Height="24"/>
                        </Button>
                        <Button x:Name="SettingsQuickButton"
                               Style="{StaticResource MaterialDesignIconButton}"
                               Foreground="White"
                               ToolTip="الإعدادات السريعة"
                               Click="SettingsButton_Click"
                               Width="48" Height="48">
                            <materialDesign:PackIcon Kind="Settings" Width="24" Height="24"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>



            <!-- Enhanced Quick Stats Summary -->
            <Grid Grid.Row="1" Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Enhanced Total Invoices Quick Card -->
                <Border Grid.Column="0" Background="White" CornerRadius="16" Margin="0,0,8,0" Cursor="Hand"
                        MouseLeftButtonUp="TotalInvoicesCard_Click">
                    <Border.Effect>
                        <DropShadowEffect Color="#667eea" Opacity="0.15" BlurRadius="15" ShadowDepth="5"/>
                    </Border.Effect>
                    <Border.Style>
                        <Style TargetType="Border">
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Effect">
                                        <Setter.Value>
                                            <DropShadowEffect Color="#667eea" Opacity="0.25" BlurRadius="20" ShadowDepth="8"/>
                                        </Setter.Value>
                                    </Setter>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>
                    <Grid Margin="24,20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Border Grid.Row="0" Background="#667eea" CornerRadius="20" Width="40" Height="40"
                                HorizontalAlignment="Center" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="FileDocument" Width="20" Height="20" Foreground="White"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <StackPanel Grid.Row="1" HorizontalAlignment="Center">
                            <TextBlock x:Name="QuickTotalInvoices" Text="0" FontSize="32" FontWeight="Bold"
                                     Foreground="#2D3748" HorizontalAlignment="Center"/>
                            <TextBlock Text="إجمالي الفواتير" FontSize="14" FontWeight="Medium"
                                     Foreground="#718096" HorizontalAlignment="Center" Margin="0,4,0,0"/>
                        </StackPanel>

                        <!-- Progress Indicator -->
                        <ProgressBar Grid.Row="2" x:Name="InvoicesProgressBar"
                                   Height="4" Margin="0,12,0,8"
                                   Background="#F0F4FF"
                                   Foreground="#667eea"
                                   Value="0" Maximum="100"/>

                        <Border Grid.Row="3" Background="#F0F4FF" CornerRadius="8" Padding="8,4">
                            <TextBlock x:Name="InvoicesChangeText" Text="جميع الفواتير" FontSize="12" Foreground="#667eea"
                                     FontWeight="Medium" HorizontalAlignment="Center"/>
                        </Border>
                    </Grid>
                </Border>

                <!-- Enhanced Total Amount Quick Card -->
                <Border Grid.Column="1" Background="White" CornerRadius="16" Margin="4,0,4,0" Cursor="Hand"
                        MouseLeftButtonUp="TotalAmountCard_Click">
                    <Border.Effect>
                        <DropShadowEffect Color="#10B981" Opacity="0.15" BlurRadius="15" ShadowDepth="5"/>
                    </Border.Effect>
                    <Border.Style>
                        <Style TargetType="Border">
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Effect">
                                        <Setter.Value>
                                            <DropShadowEffect Color="#10B981" Opacity="0.25" BlurRadius="20" ShadowDepth="8"/>
                                        </Setter.Value>
                                    </Setter>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>
                    <Grid Margin="24,20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Border Grid.Row="0" Background="#10B981" CornerRadius="20" Width="40" Height="40"
                                HorizontalAlignment="Center" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="CurrencyUsd" Width="20" Height="20" Foreground="White"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <StackPanel Grid.Row="1" HorizontalAlignment="Center">
                            <TextBlock x:Name="QuickTotalAmount" Text="0" FontSize="32" FontWeight="Bold"
                                     Foreground="#2D3748" HorizontalAlignment="Center"/>
                            <TextBlock Text="إجمالي المبلغ" FontSize="14" FontWeight="Medium"
                                     Foreground="#718096" HorizontalAlignment="Center" Margin="0,4,0,0"/>
                        </StackPanel>

                        <!-- Progress Indicator -->
                        <ProgressBar Grid.Row="2" x:Name="AmountProgressBar"
                                   Height="4" Margin="0,12,0,8"
                                   Background="#F0FDF4"
                                   Foreground="#10B981"
                                   Value="0" Maximum="100"/>

                        <Border Grid.Row="3" Background="#F0FDF4" CornerRadius="8" Padding="8,4">
                            <TextBlock x:Name="AmountChangeText" Text="دينار عراقي" FontSize="12" Foreground="#10B981"
                                     FontWeight="Medium" HorizontalAlignment="Center"/>
                        </Border>
                    </Grid>
                </Border>

                <!-- Enhanced Paid Amount Quick Card -->
                <Border Grid.Column="2" Background="White" CornerRadius="16" Margin="4,0,4,0" Cursor="Hand"
                        MouseLeftButtonUp="PaidAmountCard_Click">
                    <Border.Effect>
                        <DropShadowEffect Color="#FF6B6B" Opacity="0.15" BlurRadius="15" ShadowDepth="5"/>
                    </Border.Effect>
                    <Border.Style>
                        <Style TargetType="Border">
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Effect">
                                        <Setter.Value>
                                            <DropShadowEffect Color="#FF6B6B" Opacity="0.25" BlurRadius="20" ShadowDepth="8"/>
                                        </Setter.Value>
                                    </Setter>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>
                    <Grid Margin="24,20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Border Grid.Row="0" Background="#FF6B6B" CornerRadius="20" Width="40" Height="40"
                                HorizontalAlignment="Center" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="20" Height="20" Foreground="White"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <StackPanel Grid.Row="1" HorizontalAlignment="Center">
                            <TextBlock x:Name="QuickPaidAmount" Text="0" FontSize="32" FontWeight="Bold"
                                     Foreground="#2D3748" HorizontalAlignment="Center"/>
                            <TextBlock Text="المبلغ المسدد" FontSize="14" FontWeight="Medium"
                                     Foreground="#718096" HorizontalAlignment="Center" Margin="0,4,0,0"/>
                        </StackPanel>

                        <!-- Progress Indicator -->
                        <ProgressBar Grid.Row="2" x:Name="PaidProgressBar"
                                   Height="4" Margin="0,12,0,8"
                                   Background="#FFF5F5"
                                   Foreground="#FF6B6B"
                                   Value="0" Maximum="100"/>

                        <Border Grid.Row="3" Background="#FFF5F5" CornerRadius="8" Padding="8,4">
                            <TextBlock x:Name="PaidChangeText" Text="مدفوع" FontSize="12" Foreground="#FF6B6B"
                                     FontWeight="Medium" HorizontalAlignment="Center"/>
                        </Border>
                    </Grid>
                </Border>

                <!-- Enhanced Outstanding Amount Quick Card -->
                <Border Grid.Column="3" Background="White" CornerRadius="16" Margin="8,0,0,0" Cursor="Hand"
                        MouseLeftButtonUp="OutstandingAmountCard_Click">
                    <Border.Effect>
                        <DropShadowEffect Color="#8B5CF6" Opacity="0.15" BlurRadius="15" ShadowDepth="5"/>
                    </Border.Effect>
                    <Border.Style>
                        <Style TargetType="Border">
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Effect">
                                        <Setter.Value>
                                            <DropShadowEffect Color="#8B5CF6" Opacity="0.25" BlurRadius="20" ShadowDepth="8"/>
                                        </Setter.Value>
                                    </Setter>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>
                    <Grid Margin="24,20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Border Grid.Row="0" Background="#8B5CF6" CornerRadius="20" Width="40" Height="40"
                                HorizontalAlignment="Center" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="AlertCircle" Width="20" Height="20" Foreground="White"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <StackPanel Grid.Row="1" HorizontalAlignment="Center">
                            <TextBlock x:Name="QuickOutstandingAmount" Text="0" FontSize="32" FontWeight="Bold"
                                     Foreground="#2D3748" HorizontalAlignment="Center"/>
                            <TextBlock Text="المبلغ المتبقي" FontSize="14" FontWeight="Medium"
                                     Foreground="#718096" HorizontalAlignment="Center" Margin="0,4,0,0"/>
                        </StackPanel>

                        <!-- Progress Indicator -->
                        <ProgressBar Grid.Row="3" x:Name="OutstandingProgressBar"
                                   Height="4" Margin="0,12,0,8"
                                   Background="#FAF5FF"
                                   Foreground="#8B5CF6"
                                   Value="0" Maximum="100"/>

                        <Border Grid.Row="3" Background="#FAF5FF" CornerRadius="8" Padding="8,4">
                            <TextBlock x:Name="OutstandingChangeText" Text="متبقي" FontSize="12" Foreground="#8B5CF6"
                                     FontWeight="Medium" HorizontalAlignment="Center"/>
                        </Border>
                    </Grid>
                </Border>
            </Grid>

            <!-- Enhanced Quick Actions -->
            <Border Grid.Row="2"
                    Background="White"
                    CornerRadius="16"
                    Padding="24,20"
                    Margin="0,0,0,24">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.06" BlurRadius="12" ShadowDepth="4"/>
                </Border.Effect>

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="16"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Header -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Flash"
                                               Width="20" Height="20"
                                               Foreground="{StaticResource PrimaryGradientBrush}"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="⚡ الإجراءات السريعة"
                                 FontSize="18"
                                 FontWeight="SemiBold"
                                 Foreground="#1E293B"
                                 VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- Simple Action Buttons -->
                    <Grid Grid.Row="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="12"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="12"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="12"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="12"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Add Invoice Button -->
                        <Button Grid.Column="0"
                               x:Name="AddInvoiceQuickButton"
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Background="#667eea"
                               BorderBrush="#667eea"
                               Foreground="White"
                               FontSize="13"
                               Height="44"
                               Click="AddInvoiceButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus"
                                                       Width="16" Height="16"
                                                       Margin="0,0,6,0"/>
                                <TextBlock Text="إضافة فاتورة"/>
                            </StackPanel>
                        </Button>

                        <!-- Add Payment Button -->
                        <Button Grid.Column="2"
                               x:Name="AddPaymentQuickButton"
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Background="#10B981"
                               BorderBrush="#10B981"
                               Foreground="White"
                               FontSize="13"
                               Height="44"
                               Click="AddPaymentButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CreditCard"
                                                       Width="16" Height="16"
                                                       Margin="0,0,6,0"/>
                                <TextBlock Text="تسجيل دفعة"/>
                            </StackPanel>
                        </Button>

                        <!-- View Reports Button -->
                        <Button Grid.Column="4"
                               x:Name="ViewReportsQuickButton"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               BorderBrush="#667eea"
                               Foreground="#667eea"
                               FontSize="13"
                               Height="44"
                               Click="ViewReportsButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ChartLine"
                                                       Width="16" Height="16"
                                                       Margin="0,0,6,0"/>
                                <TextBlock Text="التقارير"/>
                            </StackPanel>
                        </Button>

                        <!-- Manage Suppliers Button -->
                        <Button Grid.Column="6"
                               x:Name="ManageSuppliersQuickButton"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               BorderBrush="#10B981"
                               Foreground="#10B981"
                               FontSize="13"
                               Height="44"
                               Click="ManageSuppliersButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AccountGroup"
                                                       Width="16" Height="16"
                                                       Margin="0,0,6,0"/>
                                <TextBlock Text="الموردين"/>
                            </StackPanel>
                        </Button>

                        <!-- Settings Button -->
                        <Button Grid.Column="8"
                               x:Name="SettingsQuickActionButton"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               BorderBrush="#8B5CF6"
                               Foreground="#8B5CF6"
                               FontSize="13"
                               Height="44"
                               Click="SettingsButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Settings"
                                                       Width="16" Height="16"
                                                       Margin="0,0,6,0"/>
                                <TextBlock Text="الإعدادات"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </Grid>
            </Border>

            <!-- Recent Activities and Alerts -->
            <Grid Grid.Row="3" Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Enhanced Recent Invoices -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource ModernCardStyle}">
                    <Grid Margin="24">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="350"/>
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0" Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="الفواتير الحديثة"
                                         Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                         Foreground="{StaticResource PrimaryGradientBrush}" FontWeight="Bold"/>
                                <TextBlock Text="آخر الفواتير المضافة إلى النظام"
                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         Opacity="0.7" Margin="0,2,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Button x:Name="FilterRecentButton" Style="{StaticResource MaterialDesignIconButton}"
                                      ToolTip="فلترة الفواتير" Click="FilterRecentButton_Click">
                                    <materialDesign:PackIcon Kind="Filter" Width="18" Height="18"/>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignFlatButton}"
                                      Content="عرض الكل" Click="ViewAllInvoicesButton_Click" Margin="8,0,0,0"/>
                            </StackPanel>
                        </Grid>

                        <!-- Filter Options -->
                        <Grid Grid.Row="1" x:Name="RecentInvoicesFilterPanel" Visibility="Collapsed" Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="الحالة:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <ComboBox Grid.Column="1" x:Name="RecentStatusFilterComboBox" Width="120"
                                    SelectionChanged="RecentStatusFilter_Changed">
                                <ComboBoxItem Content="الكل" IsSelected="True"/>
                                <ComboBoxItem Content="غير مسددة"/>
                                <ComboBoxItem Content="تسديد جزئي"/>
                                <ComboBoxItem Content="مسددة"/>
                            </ComboBox>

                            <TextBlock Grid.Column="2" Text="الفترة:" VerticalAlignment="Center" Margin="16,0,8,0"/>
                            <ComboBox Grid.Column="3" x:Name="RecentPeriodFilterComboBox" Width="120"
                                    SelectionChanged="RecentPeriodFilter_Changed">
                                <ComboBoxItem Content="آخر أسبوع" IsSelected="True"/>
                                <ComboBoxItem Content="آخر شهر"/>
                                <ComboBoxItem Content="آخر 3 أشهر"/>
                            </ComboBox>
                        </Grid>

                        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                            <ItemsControl x:Name="RecentInvoicesItemsControl">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="{DynamicResource MaterialDesignCardBackground}"
                                              CornerRadius="8" Margin="0,0,0,12" Padding="16" Cursor="Hand"
                                              MouseLeftButtonUp="RecentInvoiceItem_Click">
                                            <Border.Effect>
                                                <DropShadowEffect Color="#20000000" BlurRadius="8" ShadowDepth="2" Direction="270"/>
                                            </Border.Effect>
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Background" Value="{DynamicResource MaterialDesignSelection}"/>
                                                        </Trigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Grid.Column="0" Kind="FileDocument" Width="24" Height="24"
                                                                       Foreground="{StaticResource PrimaryGradientBrush}"
                                                                       VerticalAlignment="Top" Margin="0,0,12,0"/>

                                                <StackPanel Grid.Column="1">
                                                    <TextBlock Text="{Binding InvoiceNumber}" FontWeight="Medium" FontSize="14"/>
                                                    <TextBlock Text="{Binding Supplier.Name}" FontSize="12" Opacity="0.7" Margin="0,2,0,0"/>
                                                    <TextBlock Text="{Binding InvoiceDate, StringFormat='{}{0:yyyy/MM/dd}'}"
                                                             FontSize="11" Opacity="0.6" Margin="0,2,0,0"/>
                                                </StackPanel>

                                                <StackPanel Grid.Column="2" HorizontalAlignment="Right">
                                                    <TextBlock Text="{Binding Amount, StringFormat='{}{0:N0} د.ع'}"
                                                             FontWeight="Medium" HorizontalAlignment="Right" FontSize="14"/>
                                                    <Border Background="{Binding StatusColor}" CornerRadius="12" Padding="8,4" Margin="0,4,0,0">
                                                        <TextBlock Text="{Binding StatusText}" Foreground="White"
                                                                 FontSize="10" HorizontalAlignment="Center" FontWeight="Medium"/>
                                                    </Border>
                                                </StackPanel>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Grid>
                </materialDesign:Card>

                <!-- Enhanced Alerts and Notifications -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource ModernCardStyle}">
                    <Grid Margin="24">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0" Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="التنبيهات والإشعارات"
                                         Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                         Foreground="{StaticResource PrimaryGradientBrush}" FontWeight="Bold"/>
                                <TextBlock Text="تنبيهات مهمة تتطلب انتباهك"
                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         Opacity="0.7" Margin="0,2,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Button x:Name="AlertSettingsButton" Style="{StaticResource MaterialDesignIconButton}"
                                      ToolTip="إعدادات التنبيهات" Click="AlertSettingsButton_Click">
                                    <materialDesign:PackIcon Kind="BellSettings" Width="18" Height="18"/>
                                </Button>
                                <Button x:Name="ClearAlertsButton" Style="{StaticResource MaterialDesignIconButton}"
                                      ToolTip="مسح جميع التنبيهات" Click="ClearAlertsButton_Click" Margin="8,0,0,0">
                                    <materialDesign:PackIcon Kind="BellOff" Width="18" Height="18"/>
                                </Button>
                            </StackPanel>
                        </Grid>

                        <!-- Alert Summary -->
                        <Grid Grid.Row="1" Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock x:Name="CriticalAlertsCount" Text="0"
                                         Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                         Foreground="#F44336" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="حرجة" Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         HorizontalAlignment="Center" Opacity="0.7"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <TextBlock x:Name="WarningAlertsCount" Text="0"
                                         Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                         Foreground="#FF9800" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="تحذيرية" Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         HorizontalAlignment="Center" Opacity="0.7"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <TextBlock x:Name="InfoAlertsCount" Text="0"
                                         Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                         Foreground="#2196F3" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="معلوماتية" Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         HorizontalAlignment="Center" Opacity="0.7"/>
                            </StackPanel>
                        </Grid>

                        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                            <StackPanel x:Name="AlertsStackPanel">
                                <!-- Alerts will be populated dynamically -->
                            </StackPanel>
                        </ScrollViewer>
                    </Grid>
                </materialDesign:Card>
            </Grid>





        </Grid>
        </ScrollViewer>
    </Grid>
</UserControl>
