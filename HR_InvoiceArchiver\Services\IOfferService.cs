using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// واجهة خدمة العروض - نسخة مبسطة
    /// </summary>
    public interface IOfferService
    {
        // العمليات الأساسية
        Task<IEnumerable<Offer>> GetAllOffersAsync();
        Task<IEnumerable<Offer>> GetActiveOffersAsync();
        Task<Offer?> GetOfferByIdAsync(int id);
        Task<Offer> CreateOfferAsync(Offer offer);
        Task<Offer> UpdateOfferAsync(Offer offer);
        Task<bool> DeleteOfferAsync(int id);
        Task<bool> ValidateOfferAsync(Offer offer);

        // البحث الأساسي
        Task<IEnumerable<Offer>> SearchOffersAsync(string searchTerm);
        Task<IEnumerable<Offer>> GetOffersByScientificNameAsync(string scientificName);

        // إحصائيات أساسية
        Task<OfferStatistics> GetOfferStatisticsAsync();

        // أفضل العروض
        Task<IEnumerable<Offer>> GetBestOffersAsync();

        // التحليل المتقدم
        Task<List<BestOfferResult>> GetBestOffersWithAnalysisAsync();
        Task<OfferAnalysisResult> AnalyzeOfferAsync(int offerId);
        Task<List<OfferComparison>> CompareOffersForMaterialAsync(string scientificName);

        // تصدير البيانات
        Task<byte[]> ExportOffersToExcelAsync(IEnumerable<Offer> offers, string title = "تقرير العروض");

        // خدمات المواد العلمية الأساسية
        Task<IEnumerable<ScientificName>> GetAllScientificNamesAsync();
        Task<ScientificName> GetOrCreateScientificNameAsync(string name, string? category = null, string? description = null);

        // خدمات المكاتب العلمية
        Task<IEnumerable<string>> GetAllScientificOfficesAsync();
    }

    /// <summary>
    /// إحصائيات العروض الأساسية
    /// </summary>
    public class OfferStatistics
    {
        public int TotalOffers { get; set; }
        public int ActiveOffers { get; set; }
        public int UniqueScientificNames { get; set; }
        public int UniqueOffices { get; set; }
        public decimal AveragePrice { get; set; }
        public DateTime? LastOfferDate { get; set; }
    }
}
