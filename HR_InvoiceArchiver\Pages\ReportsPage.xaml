<UserControl x:Class="HR_InvoiceArchiver.Pages.ReportsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="#F8FAFC">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Enhanced Header -->
        <Border Grid.Row="0" 
                Background="{StaticResource SidebarGradient}" 
                CornerRadius="20" 
                Margin="0,0,0,20" 
                Padding="28,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Icon with Glow Effect -->
                <Border Grid.Column="0"
                       Background="#30FFFFFF"
                       Width="48" Height="48"
                       CornerRadius="24"
                       VerticalAlignment="Center"
                       Margin="0,0,20,0">
                    <Border.Effect>
                        <DropShadowEffect Color="White" Opacity="0.3" BlurRadius="10" ShadowDepth="0"/>
                    </Border.Effect>
                    <materialDesign:PackIcon Kind="ChartBox"
                                           Width="24" Height="24"
                                           Foreground="White"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"/>
                </Border>

                <!-- Title and Subtitle -->
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="📊 التقارير والإحصائيات"
                             FontSize="24"
                             FontWeight="Bold"
                             Foreground="White">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.3" BlurRadius="4" ShadowDepth="2"/>
                        </TextBlock.Effect>
                    </TextBlock>
                    <TextBlock Text="عرض وتحليل البيانات المالية والإحصائيات التفصيلية"
                             FontSize="14"
                             Foreground="#E0FFFFFF"
                             Margin="0,4,0,0">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="Black" Opacity="0.2" BlurRadius="3" ShadowDepth="1"/>
                        </TextBlock.Effect>
                    </TextBlock>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="RefreshDataButton"
                           Style="{StaticResource MaterialDesignIconButton}"
                           Width="44" Height="44"
                           Margin="0,0,8,0"
                           ToolTip="تحديث البيانات"
                           Click="RefreshDataButton_Click">
                        <materialDesign:PackIcon Kind="Refresh" 
                                               Width="20" Height="20" 
                                               Foreground="White"/>
                    </Button>
                    
                    <Button x:Name="ExportButton"
                           Style="{StaticResource MaterialDesignIconButton}"
                           Width="44" Height="44"
                           Margin="0,0,8,0"
                           ToolTip="تصدير التقرير"
                           Click="ExportButton_Click">
                        <materialDesign:PackIcon Kind="Download" 
                                               Width="20" Height="20" 
                                               Foreground="White"/>
                    </Button>
                    
                    <Button x:Name="PrintButton"
                           Style="{StaticResource MaterialDesignIconButton}"
                           Width="44" Height="44"
                           Margin="0,0,8,0"
                           ToolTip="طباعة التقرير"
                           Click="PrintButton_Click">
                        <materialDesign:PackIcon Kind="Printer" 
                                               Width="20" Height="20" 
                                               Foreground="White"/>
                    </Button>
                    
                    <Button x:Name="SettingsButton"
                           Style="{StaticResource MaterialDesignIconButton}"
                           Width="44" Height="44"
                           ToolTip="إعدادات التقارير"
                           Click="SettingsButton_Click">
                        <materialDesign:PackIcon Kind="Settings" 
                                               Width="20" Height="20" 
                                               Foreground="White"/>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Enhanced Reports TabControl -->
        <Border Grid.Row="1"
                CornerRadius="20"
                Margin="0,0,0,20"
                Padding="0,8,0,0">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.08" BlurRadius="25" ShadowDepth="10"/>
            </Border.Effect>

            <!-- Tab Header Background -->
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,0.1">
                    <GradientStop Color="#FAFBFC" Offset="0"/>
                    <GradientStop Color="White" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            
            <TabControl x:Name="ReportsTabControl"
                        Background="Transparent"
                        BorderThickness="0"
                        Margin="0"
                        SelectionChanged="ReportsTabControl_SelectionChanged">

                <!-- Enhanced Tab Control Style -->
                <TabControl.Style>
                    <Style TargetType="TabControl">
                        <Setter Property="Background" Value="Transparent"/>
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="Padding" Value="0"/>
                        <Setter Property="TabStripPlacement" Value="Top"/>
                    </Style>
                </TabControl.Style>

                <!-- Custom Tab Item Style -->
                <TabControl.Resources>
                    <Style TargetType="TabItem">
                        <Setter Property="Background" Value="#F8FAFC"/>
                        <Setter Property="Foreground" Value="#64748B"/>
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="Padding" Value="0"/>
                        <Setter Property="Margin" Value="0,0,4,0"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                        <Setter Property="MinHeight" Value="60"/>
                        <Setter Property="MinWidth" Value="180"/>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="TabItem">
                                    <Border x:Name="Border"
                                           Background="{TemplateBinding Background}"
                                           BorderBrush="{TemplateBinding BorderBrush}"
                                           BorderThickness="{TemplateBinding BorderThickness}"
                                           CornerRadius="12,12,0,0"
                                           Margin="{TemplateBinding Margin}"
                                           Padding="20,16">
                                        <Border.Effect>
                                            <DropShadowEffect Color="Black" Opacity="0.05" BlurRadius="8" ShadowDepth="2"/>
                                        </Border.Effect>
                                        <ContentPresenter x:Name="ContentSite"
                                                        VerticalAlignment="Center"
                                                        HorizontalAlignment="Center"
                                                        ContentSource="Header"
                                                        Margin="0"/>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="White"/>
                                            <Setter Property="Foreground" Value="#1E293B"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter TargetName="Border" Property="BorderBrush" Value="#667eea"/>
                                            <Setter TargetName="Border" Property="BorderThickness" Value="0,0,0,3"/>
                                            <Setter TargetName="Border" Property="Effect">
                                                <Setter.Value>
                                                    <DropShadowEffect Color="#667eea" Opacity="0.25" BlurRadius="15" ShadowDepth="3"/>
                                                </Setter.Value>
                                            </Setter>
                                        </Trigger>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="#F1F5F9"/>
                                            <Setter Property="Foreground" Value="#475569"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </TabControl.Resources>

                <!-- Invoices Report Tab -->
                <TabItem x:Name="InvoicesTab">
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal" Margin="4,0">
                            <Border Background="{StaticResource PrimaryGradientBrush}"
                                   CornerRadius="10"
                                   Width="32" Height="32"
                                   Margin="0,0,12,0">
                                <Border.Effect>
                                    <DropShadowEffect Color="#667eea" Opacity="0.3" BlurRadius="6" ShadowDepth="2"/>
                                </Border.Effect>
                                <materialDesign:PackIcon Kind="FileDocument"
                                                       Width="18" Height="18"
                                                       Foreground="White"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="📄 تقرير الفواتير"
                                         FontSize="15"
                                         FontWeight="Bold"
                                         Margin="0,0,0,2"/>
                                <TextBlock Text="جميع الفواتير والتفاصيل"
                                         FontSize="11"
                                         Opacity="0.7"/>
                            </StackPanel>
                        </StackPanel>
                    </TabItem.Header>
                    
                    <Grid Margin="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Enhanced Search and Filter Bar for Invoices -->
                        <Border Grid.Row="0"
                               Background="White"
                               CornerRadius="12"
                               Padding="24,20"
                               Margin="20,0,20,16">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Opacity="0.06" BlurRadius="12" ShadowDepth="4"/>
                            </Border.Effect>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="16"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Header -->
                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,0">
                                    <materialDesign:PackIcon Kind="FilterVariant"
                                                           Width="20" Height="20"
                                                           Foreground="{StaticResource PrimaryGradientBrush}"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="🔍 البحث والتصفية"
                                             FontSize="16"
                                             FontWeight="SemiBold"
                                             Foreground="#1E293B"
                                             VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- Search Controls -->
                                <Grid Grid.Row="2">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="16"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="16"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="16"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Enhanced Search Box -->
                                    <Border Grid.Column="0"
                                           Background="#F8FAFC"
                                           CornerRadius="10"
                                           Padding="16,12"
                                           BorderBrush="#E2E8F0"
                                           BorderThickness="1">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <materialDesign:PackIcon Grid.Column="0"
                                                                   Kind="Magnify"
                                                                   Width="18" Height="18"
                                                                   Foreground="#64748B"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,12,0"/>

                                            <TextBox x:Name="InvoicesSearchBox"
                                                   Grid.Column="1"
                                                   Background="Transparent"
                                                   BorderThickness="0"
                                                   FontSize="14"
                                                   VerticalAlignment="Center"
                                                   materialDesign:HintAssist.Hint="البحث برقم الفاتورة أو اسم المورد..."
                                                   materialDesign:HintAssist.Foreground="#94A3B8"
                                                   TextChanged="InvoicesSearchBox_TextChanged"/>

                                            <Button Grid.Column="2"
                                                   x:Name="ClearInvoicesSearchButton"
                                                   Style="{StaticResource MaterialDesignIconButton}"
                                                   Width="24" Height="24"
                                                   Padding="0"
                                                   Visibility="Collapsed"
                                                   Click="ClearInvoicesSearchButton_Click">
                                                <materialDesign:PackIcon Kind="Close"
                                                                       Width="14" Height="14"
                                                                       Foreground="#94A3B8"/>
                                            </Button>
                                        </Grid>
                                    </Border>

                                    <!-- Status Filter -->
                                    <ComboBox Grid.Column="2"
                                             x:Name="InvoicesStatusFilter"
                                             materialDesign:HintAssist.Hint="حالة الفاتورة"
                                             FontSize="14"
                                             Background="#F8FAFC"
                                             SelectionChanged="InvoicesStatusFilter_SelectionChanged">
                                        <ComboBoxItem Content="جميع الحالات" Tag="All" IsSelected="True"/>
                                        <ComboBoxItem Content="غير مسددة" Tag="Unpaid"/>
                                        <ComboBoxItem Content="تسديد جزئي" Tag="PartiallyPaid"/>
                                        <ComboBoxItem Content="مسددة" Tag="Paid"/>
                                        <ComboBoxItem Content="مسددة وبخصم" Tag="PaidWithDiscount"/>
                                        <ComboBoxItem Content="معلقة" Tag="Pending"/>
                                    </ComboBox>

                                    <!-- Date Filter -->
                                    <ComboBox Grid.Column="4"
                                             x:Name="InvoicesDateFilter"
                                             materialDesign:HintAssist.Hint="فترة التقرير"
                                             FontSize="14"
                                             Background="#F8FAFC"
                                             SelectionChanged="InvoicesDateFilter_SelectionChanged">
                                        <ComboBoxItem Content="جميع الفترات" Tag="All"/>
                                        <ComboBoxItem Content="اليوم" Tag="Today"/>
                                        <ComboBoxItem Content="هذا الأسبوع" Tag="ThisWeek"/>
                                        <ComboBoxItem Content="هذا الشهر" Tag="ThisMonth" IsSelected="True"/>
                                        <ComboBoxItem Content="الشهر الماضي" Tag="LastMonth"/>
                                        <ComboBoxItem Content="آخر 3 أشهر" Tag="Last3Months"/>
                                        <ComboBoxItem Content="هذا العام" Tag="ThisYear"/>
                                    </ComboBox>

                                    <!-- Action Buttons -->
                                    <StackPanel Grid.Column="6" Orientation="Horizontal">
                                        <Button x:Name="LoadInvoicesButton"
                                               Style="{StaticResource MaterialDesignRaisedButton}"
                                               Background="{StaticResource PrimaryGradientBrush}"
                                               BorderBrush="{StaticResource PrimaryGradientBrush}"
                                               Foreground="White"
                                               FontSize="14"
                                               FontWeight="Medium"
                                               Padding="20,10"

                                               Click="LoadInvoicesButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="FileDocument"
                                                                       Width="18" Height="18"
                                                                       Margin="0,0,8,0"/>
                                                <TextBlock Text="تحميل الفواتير"/>
                                            </StackPanel>
                                        </Button>

                                        <Button x:Name="RefreshInvoicesButton"
                                               Style="{StaticResource MaterialDesignOutlinedButton}"
                                               BorderBrush="{StaticResource PrimaryGradientBrush}"
                                               Foreground="{StaticResource PrimaryGradientBrush}"
                                               FontSize="14"
                                               Padding="16,10"

                                               Margin="8,0,0,0"
                                               Click="RefreshInvoicesButton_Click">
                                            <materialDesign:PackIcon Kind="Refresh"
                                                                   Width="18" Height="18"/>
                                        </Button>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </Border>

                        <!-- Invoices Data Grid -->
                        <Border Grid.Row="1" 
                               Background="White" 
                               Margin="20,0,20,20">
                            <DataGrid x:Name="InvoicesDataGrid"
                                     Background="Transparent"
                                     BorderThickness="0"
                                     GridLinesVisibility="Horizontal"
                                     HeadersVisibility="Column"
                                     AutoGenerateColumns="False"
                                     IsReadOnly="True"
                                     AlternatingRowBackground="#F8FAFC"
                                     RowBackground="White"
                                     FontSize="13"
                                     FlowDirection="RightToLeft"
                                     MinHeight="300"
                                     materialDesign:DataGridAssist.CellPadding="12,8">
                                
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                                    <DataGridTextColumn Header="التاريخ" Binding="{Binding Date}" Width="100"/>
                                    <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="150"/>
                                    <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount}" Width="100"/>
                                    <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Border>
                    </Grid>
                </TabItem>

                <!-- Payments Report Tab -->
                <TabItem x:Name="PaymentsTab">
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal" Margin="4,0">
                            <Border Background="{StaticResource SecondaryGradientBrush}"
                                   CornerRadius="10"
                                   Width="32" Height="32"
                                   Margin="0,0,12,0">
                                <Border.Effect>
                                    <DropShadowEffect Color="#10B981" Opacity="0.3" BlurRadius="6" ShadowDepth="2"/>
                                </Border.Effect>
                                <materialDesign:PackIcon Kind="CreditCard"
                                                       Width="18" Height="18"
                                                       Foreground="White"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="💳 تقرير المدفوعات"
                                         FontSize="15"
                                         FontWeight="Bold"
                                         Margin="0,0,0,2"/>
                                <TextBlock Text="جميع المدفوعات والإيصالات"
                                         FontSize="11"
                                         Opacity="0.7"/>
                            </StackPanel>
                        </StackPanel>
                    </TabItem.Header>

                    <Grid Margin="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Enhanced Search and Filter Bar for Payments -->
                        <Border Grid.Row="0"
                               Background="White"
                               CornerRadius="12"
                               Padding="24,20"
                               Margin="20,0,20,16">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Opacity="0.06" BlurRadius="12" ShadowDepth="4"/>
                            </Border.Effect>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="16"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Header -->
                                <StackPanel Grid.Row="0" Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FilterVariant"
                                                           Width="20" Height="20"
                                                           Foreground="{StaticResource SecondaryGradientBrush}"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="💳 البحث والتصفية"
                                             FontSize="16"
                                             FontWeight="SemiBold"
                                             Foreground="#1E293B"
                                             VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- Search Controls -->
                                <Grid Grid.Row="2">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="2*"/>
                                        <ColumnDefinition Width="16"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="16"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="16"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Enhanced Search Box -->
                                    <Border Grid.Column="0"
                                           Background="#F0FDF4"
                                           CornerRadius="10"
                                           Padding="16,12"
                                           BorderBrush="#D1FAE5"
                                           BorderThickness="1">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <materialDesign:PackIcon Grid.Column="0"
                                                                   Kind="Magnify"
                                                                   Width="18" Height="18"
                                                                   Foreground="#059669"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,12,0"/>

                                            <TextBox x:Name="PaymentsSearchBox"
                                                   Grid.Column="1"
                                                   Background="Transparent"
                                                   BorderThickness="0"
                                                   FontSize="14"
                                                   VerticalAlignment="Center"
                                                   materialDesign:HintAssist.Hint="البحث برقم الإيصال أو اسم المورد..."
                                                   materialDesign:HintAssist.Foreground="#6B7280"
                                                   TextChanged="PaymentsSearchBox_TextChanged"/>

                                            <Button Grid.Column="2"
                                                   x:Name="ClearPaymentsSearchButton"
                                                   Style="{StaticResource MaterialDesignIconButton}"
                                                   Width="24" Height="24"
                                                   Padding="0"
                                                   Visibility="Collapsed"
                                                   Click="ClearPaymentsSearchButton_Click">
                                                <materialDesign:PackIcon Kind="Close"
                                                                       Width="14" Height="14"
                                                                       Foreground="#6B7280"/>
                                            </Button>
                                        </Grid>
                                    </Border>

                                    <!-- Payment Method Filter -->
                                    <ComboBox Grid.Column="2"
                                             x:Name="PaymentsMethodFilter"
                                             materialDesign:HintAssist.Hint="طريقة الدفع"
                                             FontSize="14"
                                             Background="#F0FDF4"
                                             SelectionChanged="PaymentsMethodFilter_SelectionChanged">
                                        <ComboBoxItem Content="جميع الطرق" Tag="All" IsSelected="True"/>
                                        <ComboBoxItem Content="نقداً" Tag="Cash"/>
                                        <ComboBoxItem Content="بطاقة ائتمان" Tag="CreditCard"/>
                                        <ComboBoxItem Content="شيك" Tag="Check"/>
                                        <ComboBoxItem Content="تحويل بنكي" Tag="BankTransfer"/>
                                        <ComboBoxItem Content="أخرى" Tag="Other"/>
                                    </ComboBox>

                                    <!-- Date Filter -->
                                    <ComboBox Grid.Column="4"
                                             x:Name="PaymentsDateFilter"
                                             materialDesign:HintAssist.Hint="فترة التقرير"
                                             FontSize="14"
                                             Background="#F0FDF4"
                                             SelectionChanged="PaymentsDateFilter_SelectionChanged">
                                        <ComboBoxItem Content="جميع الفترات" Tag="All"/>
                                        <ComboBoxItem Content="اليوم" Tag="Today"/>
                                        <ComboBoxItem Content="هذا الأسبوع" Tag="ThisWeek"/>
                                        <ComboBoxItem Content="هذا الشهر" Tag="ThisMonth" IsSelected="True"/>
                                        <ComboBoxItem Content="الشهر الماضي" Tag="LastMonth"/>
                                        <ComboBoxItem Content="آخر 3 أشهر" Tag="Last3Months"/>
                                        <ComboBoxItem Content="هذا العام" Tag="ThisYear"/>
                                    </ComboBox>

                                    <!-- Action Buttons -->
                                    <StackPanel Grid.Column="6" Orientation="Horizontal">
                                        <Button x:Name="LoadPaymentsButton"
                                               Style="{StaticResource MaterialDesignRaisedButton}"
                                               Background="{StaticResource SecondaryGradientBrush}"
                                               BorderBrush="{StaticResource SecondaryGradientBrush}"
                                               Foreground="White"
                                               FontSize="14"
                                               FontWeight="Medium"
                                               Padding="20,10"

                                               Click="LoadPaymentsButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="CreditCard"
                                                                       Width="18" Height="18"
                                                                       Margin="0,0,8,0"/>
                                                <TextBlock Text="تحميل المدفوعات"/>
                                            </StackPanel>
                                        </Button>

                                        <Button x:Name="RefreshPaymentsButton"
                                               Style="{StaticResource MaterialDesignOutlinedButton}"
                                               BorderBrush="{StaticResource SecondaryGradientBrush}"
                                               Foreground="{StaticResource SecondaryGradientBrush}"
                                               FontSize="14"
                                               Padding="16,10"

                                               Margin="8,0,0,0"
                                               Click="RefreshPaymentsButton_Click">
                                            <materialDesign:PackIcon Kind="Refresh"
                                                                   Width="18" Height="18"/>
                                        </Button>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </Border>

                        <!-- Payments Data Grid -->
                        <Border Grid.Row="1"
                               Background="White"
                               Margin="20,0,20,20">
                            <DataGrid x:Name="PaymentsDataGrid"
                                     Background="Transparent"
                                     BorderThickness="0"
                                     GridLinesVisibility="Horizontal"
                                     HeadersVisibility="Column"
                                     AutoGenerateColumns="False"
                                     IsReadOnly="True"
                                     AlternatingRowBackground="#F8FAFC"
                                     RowBackground="White"
                                     FontSize="13"
                                     FlowDirection="RightToLeft"
                                     MinHeight="300"
                                     materialDesign:DataGridAssist.CellPadding="12,8">

                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="رقم الإيصال" Binding="{Binding ReceiptNumber}" Width="120"/>
                                    <DataGridTextColumn Header="التاريخ" Binding="{Binding Date}" Width="100"/>
                                    <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="150"/>
                                    <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount}" Width="100"/>
                                    <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethod}" Width="120"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Border>
                    </Grid>
                </TabItem>

                <!-- Suppliers Report Tab -->
                <TabItem x:Name="SuppliersTab">
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal" Margin="4,0">
                            <Border Background="#4ECDC4"
                                   CornerRadius="10"
                                   Width="32" Height="32"
                                   Margin="0,0,12,0">
                                <Border.Effect>
                                    <DropShadowEffect Color="#4ECDC4" Opacity="0.3" BlurRadius="6" ShadowDepth="2"/>
                                </Border.Effect>
                                <materialDesign:PackIcon Kind="AccountGroup"
                                                       Width="18" Height="18"
                                                       Foreground="White"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="👥 تقرير الموردين"
                                         FontSize="15"
                                         FontWeight="Bold"
                                         Margin="0,0,0,2"/>
                                <TextBlock Text="إحصائيات وأداء الموردين"
                                         FontSize="11"
                                         Opacity="0.7"/>
                            </StackPanel>
                        </StackPanel>
                    </TabItem.Header>

                    <Grid Margin="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Search and Filter Bar for Suppliers -->
                        <Border Grid.Row="0" Background="#F0FDFA" Padding="20,16">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="2*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- Search Box -->
                                <Border Grid.Column="0"
                                       Background="White"
                                       CornerRadius="8"
                                       Padding="12,8"
                                       Margin="0,0,12,0">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <materialDesign:PackIcon Grid.Column="0"
                                                               Kind="Magnify"
                                                               Width="16" Height="16"
                                                               Foreground="#64748B"
                                                               VerticalAlignment="Center"
                                                               Margin="0,0,8,0"/>

                                        <TextBox x:Name="SuppliersSearchBox"
                                               Grid.Column="1"
                                               Background="Transparent"
                                               BorderThickness="0"
                                               FontSize="13"
                                               VerticalAlignment="Center"
                                               materialDesign:HintAssist.Hint="البحث في الموردين..."
                                               materialDesign:HintAssist.Foreground="#94A3B8"/>
                                    </Grid>
                                </Border>

                                <!-- Date Filter -->
                                <ComboBox Grid.Column="1"
                                         x:Name="SuppliersDateFilter"
                                         materialDesign:HintAssist.Hint="فترة التقرير"
                                         FontSize="13"
                                         Margin="0,0,12,0">
                                    <ComboBoxItem Content="اليوم"/>
                                    <ComboBoxItem Content="هذا الأسبوع"/>
                                    <ComboBoxItem Content="هذا الشهر" IsSelected="True"/>
                                    <ComboBoxItem Content="الشهر الماضي"/>
                                    <ComboBoxItem Content="هذا العام"/>
                                </ComboBox>

                                <!-- Load Button -->
                                <Button Grid.Column="2"
                                       x:Name="LoadSuppliersButton"
                                       Style="{StaticResource MaterialDesignRaisedButton}"
                                       Background="#4ECDC4"
                                       BorderBrush="#4ECDC4"
                                       Foreground="White"
                                       FontSize="13"
                                       Padding="16,8"
                                       Click="LoadSuppliersButton_Click">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="AccountGroup"
                                                               Width="16" Height="16"
                                                               Margin="0,0,6,0"/>
                                        <TextBlock Text="تحميل الموردين"/>
                                    </StackPanel>
                                </Button>
                            </Grid>
                        </Border>

                        <!-- Suppliers Data Grid -->
                        <Border Grid.Row="1"
                               Background="White"
                               Margin="20,0,20,20">
                            <DataGrid x:Name="SuppliersDataGrid"
                                     Background="Transparent"
                                     BorderThickness="0"
                                     GridLinesVisibility="Horizontal"
                                     HeadersVisibility="Column"
                                     AutoGenerateColumns="False"
                                     IsReadOnly="True"
                                     AlternatingRowBackground="#F8FAFC"
                                     RowBackground="White"
                                     FontSize="13"
                                     FlowDirection="RightToLeft"
                                     MinHeight="300"
                                     materialDesign:DataGridAssist.CellPadding="12,8">

                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="اسم المورد" Binding="{Binding SupplierName}" Width="200"/>
                                    <DataGridTextColumn Header="عدد الفواتير" Binding="{Binding InvoiceCount}" Width="120"/>
                                    <DataGridTextColumn Header="إجمالي المبالغ" Binding="{Binding TotalAmount}" Width="150"/>
                                    <DataGridTextColumn Header="المبالغ المدفوعة" Binding="{Binding PaidAmount}" Width="150"/>
                                    <DataGridTextColumn Header="المبالغ المستحقة" Binding="{Binding OutstandingAmount}" Width="150"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Border>
                    </Grid>
                </TabItem>

                <!-- Monthly Report Tab -->
                <TabItem x:Name="MonthlyTab">
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal" Margin="4,0">
                            <Border Background="#8B5CF6"
                                   CornerRadius="10"
                                   Width="32" Height="32"
                                   Margin="0,0,12,0">
                                <Border.Effect>
                                    <DropShadowEffect Color="#8B5CF6" Opacity="0.3" BlurRadius="6" ShadowDepth="2"/>
                                </Border.Effect>
                                <materialDesign:PackIcon Kind="ChartLine"
                                                       Width="18" Height="18"
                                                       Foreground="White"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="📊 التقرير الشهري"
                                         FontSize="15"
                                         FontWeight="Bold"
                                         Margin="0,0,0,2"/>
                                <TextBlock Text="تحليل شهري مفصل ومقارنات"
                                         FontSize="11"
                                         Opacity="0.7"/>
                            </StackPanel>
                        </StackPanel>
                    </TabItem.Header>

                    <Grid Margin="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Search and Filter Bar for Monthly Report -->
                        <Border Grid.Row="0" Background="#FAF5FF" Padding="20,16">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- Month Selector -->
                                <ComboBox Grid.Column="0"
                                         x:Name="MonthSelector"
                                         materialDesign:HintAssist.Hint="اختر الشهر"
                                         FontSize="13"
                                         Margin="0,0,12,0">
                                    <ComboBoxItem Content="يناير"/>
                                    <ComboBoxItem Content="فبراير"/>
                                    <ComboBoxItem Content="مارس"/>
                                    <ComboBoxItem Content="أبريل"/>
                                    <ComboBoxItem Content="مايو"/>
                                    <ComboBoxItem Content="يونيو"/>
                                    <ComboBoxItem Content="يوليو"/>
                                    <ComboBoxItem Content="أغسطس"/>
                                    <ComboBoxItem Content="سبتمبر"/>
                                    <ComboBoxItem Content="أكتوبر"/>
                                    <ComboBoxItem Content="نوفمبر"/>
                                    <ComboBoxItem Content="ديسمبر" IsSelected="True"/>
                                </ComboBox>

                                <!-- Year Selector -->
                                <ComboBox Grid.Column="1"
                                         x:Name="YearSelector"
                                         materialDesign:HintAssist.Hint="اختر السنة"
                                         FontSize="13"
                                         Margin="0,0,12,0">
                                    <ComboBoxItem Content="2023"/>
                                    <ComboBoxItem Content="2024" IsSelected="True"/>
                                    <ComboBoxItem Content="2025"/>
                                </ComboBox>

                                <!-- Load Button -->
                                <Button Grid.Column="2"
                                       x:Name="LoadMonthlyButton"
                                       Style="{StaticResource MaterialDesignRaisedButton}"
                                       Background="#8B5CF6"
                                       BorderBrush="#8B5CF6"
                                       Foreground="White"
                                       FontSize="13"
                                       Padding="16,8"
                                       Click="LoadMonthlyButton_Click">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="ChartLine"
                                                               Width="16" Height="16"
                                                               Margin="0,0,6,0"/>
                                        <TextBlock Text="تحميل التقرير"/>
                                    </StackPanel>
                                </Button>
                            </Grid>
                        </Border>

                        <!-- Monthly Report Content -->
                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                            <StackPanel Margin="20">

                                <!-- Summary Cards -->
                                <Grid Margin="0,0,0,20">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Total Invoices Card -->
                                    <Border Grid.Column="0"
                                           Background="White"
                                           CornerRadius="12"
                                           Padding="16"
                                           Margin="0,0,8,0">
                                        <Border.Effect>
                                            <DropShadowEffect Color="Black" Opacity="0.05" BlurRadius="10" ShadowDepth="3"/>
                                        </Border.Effect>
                                        <StackPanel>
                                            <TextBlock Text="إجمالي الفواتير"
                                                     FontSize="12"
                                                     Foreground="#64748B"
                                                     Margin="0,0,0,8"/>
                                            <TextBlock x:Name="MonthlyInvoicesCount"
                                                     Text="0"
                                                     FontSize="24"
                                                     FontWeight="Bold"
                                                     Foreground="#1E293B"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- Total Amount Card -->
                                    <Border Grid.Column="1"
                                           Background="White"
                                           CornerRadius="12"
                                           Padding="16"
                                           Margin="0,0,8,0">
                                        <Border.Effect>
                                            <DropShadowEffect Color="Black" Opacity="0.05" BlurRadius="10" ShadowDepth="3"/>
                                        </Border.Effect>
                                        <StackPanel>
                                            <TextBlock Text="إجمالي المبالغ"
                                                     FontSize="12"
                                                     Foreground="#64748B"
                                                     Margin="0,0,0,8"/>
                                            <TextBlock x:Name="MonthlyTotalAmount"
                                                     Text="0 د.ع"
                                                     FontSize="24"
                                                     FontWeight="Bold"
                                                     Foreground="#1E293B"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- Paid Amount Card -->
                                    <Border Grid.Column="2"
                                           Background="White"
                                           CornerRadius="12"
                                           Padding="16"
                                           Margin="0,0,8,0">
                                        <Border.Effect>
                                            <DropShadowEffect Color="Black" Opacity="0.05" BlurRadius="10" ShadowDepth="3"/>
                                        </Border.Effect>
                                        <StackPanel>
                                            <TextBlock Text="المبالغ المدفوعة"
                                                     FontSize="12"
                                                     Foreground="#64748B"
                                                     Margin="0,0,0,8"/>
                                            <TextBlock x:Name="MonthlyPaidAmount"
                                                     Text="0 د.ع"
                                                     FontSize="24"
                                                     FontWeight="Bold"
                                                     Foreground="#10B981"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- Outstanding Amount Card -->
                                    <Border Grid.Column="3"
                                           Background="White"
                                           CornerRadius="12"
                                           Padding="16">
                                        <Border.Effect>
                                            <DropShadowEffect Color="Black" Opacity="0.05" BlurRadius="10" ShadowDepth="3"/>
                                        </Border.Effect>
                                        <StackPanel>
                                            <TextBlock Text="المبالغ المستحقة"
                                                     FontSize="12"
                                                     Foreground="#64748B"
                                                     Margin="0,0,0,8"/>
                                            <TextBlock x:Name="MonthlyOutstandingAmount"
                                                     Text="0 د.ع"
                                                     FontSize="24"
                                                     FontWeight="Bold"
                                                     Foreground="#F59E0B"/>
                                        </StackPanel>
                                    </Border>
                                </Grid>

                                <!-- Monthly Data Grid -->
                                <Border Background="White"
                                       CornerRadius="12"
                                       Padding="0">
                                    <Border.Effect>
                                        <DropShadowEffect Color="Black" Opacity="0.05" BlurRadius="10" ShadowDepth="3"/>
                                    </Border.Effect>

                                    <DataGrid x:Name="MonthlyDataGrid"
                                             Background="Transparent"
                                             BorderThickness="0"
                                             GridLinesVisibility="Horizontal"
                                             HeadersVisibility="Column"
                                             AutoGenerateColumns="False"
                                             IsReadOnly="True"
                                             AlternatingRowBackground="#F8FAFC"
                                             RowBackground="White"
                                             FontSize="13"
                                             FlowDirection="RightToLeft"
                                             MinHeight="300"
                                             materialDesign:DataGridAssist.CellPadding="12,8">

                                        <DataGrid.Columns>
                                            <DataGridTextColumn Header="اليوم" Binding="{Binding Day}" Width="80"/>
                                            <DataGridTextColumn Header="عدد الفواتير" Binding="{Binding InvoiceCount}" Width="120"/>
                                            <DataGridTextColumn Header="إجمالي المبالغ" Binding="{Binding TotalAmount}" Width="150"/>
                                            <DataGridTextColumn Header="المبالغ المدفوعة" Binding="{Binding PaidAmount}" Width="150"/>
                                            <DataGridTextColumn Header="المبالغ المستحقة" Binding="{Binding OutstandingAmount}" Width="150"/>
                                            <DataGridTextColumn Header="نسبة التحصيل" Binding="{Binding CollectionRate}" Width="120"/>
                                        </DataGrid.Columns>
                                    </DataGrid>
                                </Border>
                            </StackPanel>
                        </ScrollViewer>
                    </Grid>
                </TabItem>
            </TabControl>
        </Border>

        <!-- Bottom Statistics Bar -->
        <Border Grid.Row="2" Background="{StaticResource SidebarGradient}" CornerRadius="20" Margin="0,20,0,0" Padding="20,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Status Info -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Information" 
                                           Width="20" Height="20" 
                                           Foreground="White" 
                                           Margin="0,0,8,0"/>
                    <TextBlock x:Name="StatusInfoText" 
                             Text="جاهز لعرض التقارير" 
                             FontSize="14" 
                             FontWeight="Medium"
                             Foreground="White"/>
                </StackPanel>

                <!-- Current Report Info -->
                <StackPanel Grid.Column="1" 
                           Orientation="Horizontal" 
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center">
                    <TextBlock x:Name="CurrentReportText" 
                             Text="اختر تبويب التقرير المطلوب" 
                             FontSize="14" 
                             FontWeight="Medium"
                             Foreground="#E0FFFFFF"/>
                </StackPanel>

                <!-- Last Update Info -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Clock" 
                                           Width="16" Height="16" 
                                           Foreground="#E0FFFFFF" 
                                           Margin="0,0,6,0"/>
                    <TextBlock x:Name="LastUpdateText" 
                             Text="آخر تحديث: --" 
                             FontSize="12" 
                             Foreground="#E0FFFFFF"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
