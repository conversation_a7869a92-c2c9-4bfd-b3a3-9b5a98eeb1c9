<Window x:Class="HR_InvoiceArchiver.Windows.AddEditPaymentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إضافة/تعديل مدفوعة"
        Height="800" Width="700"
        WindowStartupLocation="CenterOwner"
        Background="Transparent"
        FontFamily="{DynamicResource MaterialDesignFont}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize"
        MinHeight="750" MinWidth="650"
        WindowStyle="None"
        AllowsTransparency="True">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Modern Gradient Brushes -->
        <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#10B981" Offset="0"/>
            <GradientStop Color="#059669" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#40000000" BlurRadius="20" ShadowDepth="8" Direction="270"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Enhanced TextBox Style -->
        <Style x:Key="EnhancedTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="Padding" Value="16,16,16,8"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="#667eea"/>
            <Setter Property="materialDesign:TextFieldAssist.RippleOnFocusEnabled" Value="True"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.75"/>
        </Style>

        <!-- Enhanced ComboBox Style -->
        <Style x:Key="EnhancedComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="Padding" Value="16,16,16,8"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="#667eea"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.75"/>
        </Style>

        <!-- Enhanced DatePicker Style -->
        <Style x:Key="EnhancedDatePickerStyle" TargetType="DatePicker" BasedOn="{StaticResource MaterialDesignOutlinedDatePicker}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="Padding" Value="16,16,16,8"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="#667eea"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.75"/>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="48"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Padding" Value="24,0"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
        </Style>
    </Window.Resources>

    <!-- Main Container with Shadow and Rounded Corners -->
    <Border Background="White"
            CornerRadius="16"
            Margin="12">
        <Border.Effect>
            <DropShadowEffect Color="#30000000"
                            BlurRadius="35"
                            ShadowDepth="12"
                            Direction="270"
                            Opacity="0.4"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Window Title Bar -->
            <Border Grid.Row="0"
                    Background="White"
                    CornerRadius="16,16,0,0"
                    Padding="20,15">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Window Title -->
                    <StackPanel Grid.Column="0"
                              Orientation="Horizontal"
                              VerticalAlignment="Center"
                              Margin="25,0,0,0">
                        <Border Background="{StaticResource PrimaryGradientBrush}"
                                CornerRadius="6"
                                Width="28" Height="28"
                                VerticalAlignment="Center"
                                Margin="0,0,12,0">
                            <materialDesign:PackIcon Kind="CreditCardMultiple"
                                                   Width="16" Height="16"
                                                   Foreground="White"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock x:Name="WindowTitleTextBlock"
                                   Text="إضافة مدفوعة جديدة"
                                   FontSize="15"
                                   FontWeight="SemiBold"
                                   Foreground="#1F2937"
                                   VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- Minimize Button -->
                    <Button Grid.Column="1"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="35" Height="35"
                            Padding="0"
                            Foreground="#6B7280"
                            Click="MinimizeButton_Click"
                            ToolTip="تصغير"
                            Margin="0,0,5,0">
                        <materialDesign:PackIcon Kind="WindowMinimize" Width="18" Height="18"/>
                    </Button>

                    <!-- Close Button -->
                    <Button Grid.Column="2"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="35" Height="35"
                            Padding="0"
                            Foreground="#6B7280"
                            Click="CloseButton_Click"
                            ToolTip="إغلاق">
                        <materialDesign:PackIcon Kind="Close" Width="18" Height="18"/>
                    </Button>
                </Grid>
            </Border>

            <!-- Header Section -->
            <materialDesign:Card Grid.Row="1"
                               Margin="25,20,25,0"
                               materialDesign:ElevationAssist.Elevation="Dp2"
                               Background="White">
                <Border Padding="30,25"
                        Background="White"
                        CornerRadius="8">
                    <Border.Effect>
                        <DropShadowEffect Color="#10000000" BlurRadius="8" ShadowDepth="2" Direction="270"/>
                    </Border.Effect>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Icon Container -->
                        <Border Grid.Column="0"
                                Background="{StaticResource PrimaryGradientBrush}"
                                CornerRadius="16"
                                Width="64" Height="64"
                                VerticalAlignment="Center"
                                Margin="0,0,25,0">
                            <Border.Effect>
                                <DropShadowEffect Color="#667eea" BlurRadius="20" ShadowDepth="8" Opacity="0.3"/>
                            </Border.Effect>
                            <materialDesign:PackIcon Kind="CreditCardMultiple"
                                                   Width="32" Height="32"
                                                   Foreground="White"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"/>
                        </Border>

                        <!-- Title and Description -->
                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock x:Name="HeaderTitleTextBlock"
                                       Text="إضافة مدفوعة جديدة"
                                       FontSize="26"
                                       FontWeight="Bold"
                                       Foreground="#1F2937"
                                       Margin="0,0,0,8"
                                       LineHeight="32"/>
                            <TextBlock x:Name="HeaderDescriptionTextBlock"
                                       Text="قم بإدخال بيانات المدفوعة الجديدة بعناية ودقة"
                                       FontSize="15"
                                       Foreground="#6B7280"
                                       TextWrapping="Wrap"
                                       LineHeight="22"
                                       Opacity="0.9"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </materialDesign:Card>

            <!-- Main Content -->
            <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" Padding="25,20,25,0">
                <materialDesign:Card Style="{StaticResource ModernCardStyle}">
                    <Grid Margin="32">
                        <StackPanel>
                            <!-- Invoice Selection -->
                            <TextBlock Text="الفاتورة *" FontWeight="Medium" FontSize="15"
                                     Foreground="#374151" Margin="0,0,0,16"/>
                            <ComboBox x:Name="InvoiceComboBox"
                                    Style="{StaticResource EnhancedComboBoxStyle}"
                                    materialDesign:HintAssist.Hint="اختر الفاتورة"
                                    DisplayMemberPath="DisplayText"
                                    SelectedValuePath="Id"
                                    SelectionChanged="InvoiceComboBox_SelectionChanged"
                                    Margin="0,0,0,28"/>

                            <!-- Invoice Info Panel -->
                            <Border x:Name="InvoiceInfoPanel"
                                  Background="#F0F9FF"
                                  BorderBrush="#0EA5E9" BorderThickness="2"
                                  CornerRadius="16" Padding="24" Margin="0,0,0,28"
                                  Visibility="Collapsed">
                                <Border.Effect>
                                    <DropShadowEffect Color="#0EA5E9" BlurRadius="10" ShadowDepth="3" Direction="270" Opacity="0.2"/>
                                </Border.Effect>
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                        <materialDesign:PackIcon Kind="InformationOutline" Width="24" Height="24"
                                                               Foreground="#0EA5E9" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                        <TextBlock Text="معلومات الفاتورة" FontWeight="Medium" FontSize="16"
                                                 Foreground="#0EA5E9" VerticalAlignment="Center"/>
                                    </StackPanel>
                                    <TextBlock x:Name="InvoiceInfoTextBlock" TextWrapping="Wrap"
                                             FontSize="14" Foreground="#374151" LineHeight="22"/>
                                </StackPanel>
                            </Border>

                            <!-- Receipt Number -->
                            <TextBlock Text="رقم الوصل *" FontWeight="Medium" FontSize="15"
                                     Foreground="#374151" Margin="0,0,0,16"/>
                            <TextBox x:Name="ReceiptNumberTextBox"
                                   Style="{StaticResource EnhancedTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="أدخل رقم الوصل"
                                   Margin="0,0,0,28"/>

                            <!-- Payment Date -->
                            <TextBlock Text="تاريخ الدفع *" FontWeight="Medium" FontSize="15"
                                     Foreground="#374151" Margin="0,0,0,16"/>
                            <DatePicker x:Name="PaymentDatePicker"
                                      Style="{StaticResource EnhancedDatePickerStyle}"
                                      materialDesign:HintAssist.Hint="اختر تاريخ الدفع"
                                      Margin="0,0,0,28"/>

                            <!-- Payment Status -->
                            <TextBlock Text="حالة التسديد *" FontWeight="Medium" FontSize="15"
                                     Foreground="#374151" Margin="0,0,0,16"/>
                            <ComboBox x:Name="PaymentStatusComboBox"
                                    Style="{StaticResource EnhancedComboBoxStyle}"
                                    materialDesign:HintAssist.Hint="اختر حالة التسديد"
                                    SelectionChanged="PaymentStatusComboBox_SelectionChanged"
                                    Margin="0,0,0,28">
                                <ComboBoxItem Content="تسديد كامل" Tag="FullPayment"/>
                                <ComboBoxItem Content="تسديد جزئي" Tag="PartialPayment"/>
                                <ComboBoxItem Content="تسديد مع خصم" Tag="PaymentWithDiscount"/>
                                <ComboBoxItem Content="تسديد واسترجاع المتبقي" Tag="PaymentWithRefund"/>
                            </ComboBox>

                            <!-- Amount -->
                            <TextBlock Text="المبلغ (دينار عراقي) *" FontWeight="Medium" FontSize="15"
                                     Foreground="#374151" Margin="0,0,0,16"/>
                            <TextBox x:Name="AmountTextBox"
                                   Style="{StaticResource EnhancedTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="أدخل المبلغ"
                                   TextChanged="AmountTextBox_TextChanged"
                                   Margin="0,0,0,28"/>

                            <!-- Discount Amount (visible only when PaymentWithDiscount is selected) -->
                            <TextBlock x:Name="DiscountLabel" Text="مبلغ الخصم (دينار عراقي)" FontWeight="Medium" FontSize="15"
                                     Foreground="#374151" Margin="0,0,0,16" Visibility="Collapsed"/>
                            <TextBox x:Name="DiscountAmountTextBox"
                                   Style="{StaticResource EnhancedTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="أدخل مبلغ الخصم"
                                   Visibility="Collapsed"
                                   TextChanged="DiscountAmountTextBox_TextChanged"
                                   Margin="0,0,0,28"/>

                            <!-- Refund Value Display (visible only when PaymentWithRefund is selected) -->
                            <TextBlock x:Name="RefundValueLabel" Text="قيمة البضاعة المسترجعة:" FontWeight="Medium" FontSize="15"
                                     Foreground="#374151" Margin="0,0,0,16" Visibility="Collapsed"/>
                            <Border x:Name="RefundValueDisplay"
                                    Background="#FEF3C7"
                                    BorderBrush="#F59E0B"
                                    BorderThickness="2"
                                    CornerRadius="16"
                                    Padding="20"
                                    Visibility="Collapsed"
                                    Margin="0,0,0,28">
                                <Border.Effect>
                                    <DropShadowEffect Color="#F59E0B" BlurRadius="8" ShadowDepth="2" Direction="270" Opacity="0.2"/>
                                </Border.Effect>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CurrencyUsd" Width="24" Height="24"
                                                           Foreground="#F59E0B" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                    <TextBlock x:Name="RefundValueText"
                                             Text="0 د.ع"
                                             FontSize="16"
                                             FontWeight="SemiBold"
                                             Foreground="#92400E"
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <!-- Payment Method -->
                            <TextBlock Text="طريقة الدفع *" FontWeight="Medium" FontSize="15"
                                     Foreground="#374151" Margin="0,0,0,16"/>
                            <ComboBox x:Name="PaymentMethodComboBox"
                                    Style="{StaticResource EnhancedComboBoxStyle}"
                                    materialDesign:HintAssist.Hint="اختر طريقة الدفع"
                                    Margin="0,0,0,28">
                                <ComboBoxItem Content="نقدي" Tag="Cash"/>
                                <ComboBoxItem Content="بطاقة" Tag="CreditCard"/>
                            </ComboBox>

                            <!-- Notes -->
                            <TextBlock Text="الملاحظات" FontWeight="Medium" FontSize="15"
                                     Foreground="#374151" Margin="0,0,0,16"/>
                            <TextBox x:Name="NotesTextBox"
                                   Style="{StaticResource EnhancedTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="أدخل ملاحظات إضافية (اختياري)"
                                   Height="90"
                                   TextWrapping="Wrap"
                                   AcceptsReturn="True"
                                   VerticalScrollBarVisibility="Auto"
                                   Margin="0,0,0,28"/>

                            <!-- Receipt Attachment -->
                            <TextBlock Text="مرفق الوصل" FontWeight="Medium" FontSize="15"
                                     Foreground="#374151" Margin="0,0,0,16"/>
                            <Grid Margin="0,0,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBox x:Name="AttachmentPathTextBox"
                                       Grid.Column="0"
                                       Style="{StaticResource EnhancedTextBoxStyle}"
                                       materialDesign:HintAssist.Hint="اسم الملف المرفق"
                                       IsReadOnly="True"
                                       Margin="0,0,16,0"/>

                                <Button Grid.Column="1"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Click="BrowseAttachmentButton_Click"
                                      Height="56" Padding="20,0"
                                      Margin="0,0,12,0">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FolderOpen" Width="20" Height="20" Margin="0,0,8,0"/>
                                        <TextBlock Text="تصفح" FontSize="14"/>
                                    </StackPanel>
                                </Button>

                                <Button x:Name="PreviewAttachmentButton"
                                      Grid.Column="2"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Click="PreviewAttachmentButton_Click"
                                      ToolTip="معاينة المرفق"
                                      Height="56" Width="56"
                                      Visibility="Collapsed"
                                      Margin="0,0,12,0">
                                    <materialDesign:PackIcon Kind="Eye" Width="22" Height="22" Foreground="#0EA5E9"/>
                                </Button>

                                <Button Grid.Column="3"
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      Click="RemoveAttachmentButton_Click"
                                      ToolTip="إزالة المرفق"
                                      Height="56" Width="56">
                                    <materialDesign:PackIcon Kind="Close" Width="22" Height="22" Foreground="#EF4444"/>
                                </Button>
                            </Grid>
                        </StackPanel>
                    </Grid>
                </materialDesign:Card>
            </ScrollViewer>

            <!-- Action Buttons -->
            <Border Grid.Row="3"
                    Background="#FAFBFC"
                    BorderBrush="#E5E7EB"
                    BorderThickness="0,1,0,0"
                    Padding="25,20"
                    CornerRadius="0,0,16,16">
                <Border.Effect>
                    <DropShadowEffect Color="#10000000" BlurRadius="8" ShadowDepth="2" Direction="90"/>
                </Border.Effect>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="SaveButton"
                            Style="{StaticResource ModernButtonStyle}"
                            Background="{StaticResource PrimaryGradientBrush}"
                            Foreground="White"
                            Click="SaveButton_Click"
                            MinWidth="160"
                            Height="48"
                            Margin="0,0,15,0"
                            materialDesign:ButtonAssist.CornerRadius="12">
                        <Button.Effect>
                            <DropShadowEffect Color="#667eea" BlurRadius="12" ShadowDepth="4" Direction="270" Opacity="0.3"/>
                        </Button.Effect>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ContentSave" Width="18" Height="18" Margin="0,0,10,0"/>
                            <TextBlock x:Name="SaveButtonText" Text="حفظ المدفوعة" FontSize="14" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="CancelButton"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Background="White"
                            Foreground="#6B7280"
                            BorderBrush="#D1D5DB"
                            BorderThickness="1.5"
                            Click="CancelButton_Click"
                            MinWidth="120"
                            Height="48"
                            Margin="15,0,0,0"
                            materialDesign:ButtonAssist.CornerRadius="12">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Cancel" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="إلغاء" FontSize="14" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window>
