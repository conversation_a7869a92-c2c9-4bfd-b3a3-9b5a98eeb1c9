<UserControl x:Class="HR_InvoiceArchiver.Pages.SuppliersPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:HR_InvoiceArchiver.Converters"
             Background="#F8F9FF"
             FontFamily="{DynamicResource MaterialDesignFont}"
             TextElement.Foreground="{DynamicResource MaterialDesignBody}"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <!-- Converters -->
        <converters:StringNullOrEmptyConverter x:Key="StringNullOrEmptyConverter"/>

        <!-- Modern Gradient Brushes -->
        <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#10B981" Offset="0"/>
            <GradientStop Color="#059669" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#40000000" BlurRadius="20" ShadowDepth="8" Direction="270"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#40000000" BlurRadius="8" ShadowDepth="2" Direction="270"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Search Box Style -->
        <Style x:Key="SearchBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="45"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="10">
                            <Grid>
                                <ScrollViewer x:Name="PART_ContentHost"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                                <TextBlock x:Name="PlaceholderText"
                                         Text="🔍 البحث في الموردين..."
                                         Foreground="#9E9E9E"
                                         VerticalAlignment="Center"
                                         Margin="{TemplateBinding Padding}"
                                         IsHitTestVisible="False"/>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="Text" Value="">
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                            </Trigger>
                            <Trigger Property="Text" Value="{x:Null}">
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                            </Trigger>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Collapsed"/>
                            </Trigger>
                            <MultiTrigger>
                                <MultiTrigger.Conditions>
                                    <Condition Property="Text" Value=""/>
                                    <Condition Property="IsFocused" Value="False"/>
                                </MultiTrigger.Conditions>
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                            </MultiTrigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان والأزرار -->
        <materialDesign:Card Grid.Row="0" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
            <Grid Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- العنوان مع أيقونة -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="AccountMultiple"
                                           Width="32" Height="32"
                                           Foreground="{StaticResource PrimaryGradientBrush}"
                                           VerticalAlignment="Center"
                                           Margin="0,0,10,0"/>
                    <StackPanel>
                        <TextBlock Text="إدارة الموردين"
                                   FontSize="24" FontWeight="Bold"
                                   Foreground="#2D3748"/>
                        <TextBlock Text="إدارة ومتابعة الموردين والحسابات التجارية"
                                   FontSize="14"
                                   Foreground="#718096"
                                   Margin="0,2,0,0"/>
                    </StackPanel>
                </StackPanel>

                <!-- الأزرار -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="AddNewSupplierButton"
                            Style="{StaticResource ModernButtonStyle}"
                            Background="{StaticResource PrimaryGradientBrush}"
                            Margin="8,0,0,0"
                            Click="AddNewSupplierButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="إضافة مورد جديد" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="SupplierStatementButton"
                            Style="{StaticResource ModernButtonStyle}"
                            Background="{StaticResource SecondaryGradientBrush}"
                            Margin="8,0,0,0"
                            Click="SupplierStatementButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileDocumentEdit" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="كشف حساب" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="ExportButton"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            materialDesign:ButtonAssist.CornerRadius="8"
                            Height="40"
                            Padding="12,8"
                            Margin="8,0,0,0"
                            BorderBrush="{StaticResource PrimaryGradientBrush}"
                            Foreground="{StaticResource PrimaryGradientBrush}"
                            Click="ExportButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileExcel" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="تصدير Excel" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="RefreshButton"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            materialDesign:ButtonAssist.CornerRadius="8"
                            Height="40"
                            Padding="12,8"
                            Margin="8,0,0,0"
                            BorderBrush="{StaticResource SecondaryGradientBrush}"
                            Foreground="{StaticResource SecondaryGradientBrush}"
                            Click="RefreshButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث" FontWeight="Medium"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- شريط البحث والفلاتر -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
            <Expander Header="🔍 البحث والفلاتر المتقدمة"
                      IsExpanded="False"
                      FontSize="16"
                      FontWeight="Medium"
                      Foreground="#2D3748">
                <Grid Margin="20,15,20,20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- شريط البحث -->
                    <TextBox x:Name="SearchTextBox"
                             Grid.Row="0"
                             Style="{StaticResource SearchBoxStyle}"
                             Margin="0,0,0,20"
                             TextChanged="SearchTextBox_TextChanged">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>

                    <!-- الفلاتر المتقدمة -->
                    <Grid Grid.Row="1" Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- فلتر حالة المورد -->
                        <Border Grid.Column="0"
                                Background="#F7FAFC"
                                CornerRadius="8"
                                Padding="12"
                                Margin="0,0,8,0">
                            <StackPanel>
                                <TextBlock Text="📊 حالة المورد"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="#4A5568"
                                           Margin="0,0,0,8"/>
                                <ComboBox x:Name="StatusFilterComboBox"
                                          materialDesign:HintAssist.Hint="اختر الحالة"
                                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                          FontSize="12"
                                          Height="35"
                                          SelectionChanged="StatusFilter_SelectionChanged">
                                    <ComboBoxItem Content="الكل"/>
                                    <ComboBoxItem Content="نشط"/>
                                    <ComboBoxItem Content="غير نشط"/>
                                </ComboBox>
                            </StackPanel>
                        </Border>

                        <!-- فلتر نطاق المبلغ -->
                        <Border Grid.Column="1"
                                Background="#F7FAFC"
                                CornerRadius="8"
                                Padding="12"
                                Margin="0,0,8,0">
                            <StackPanel>
                                <TextBlock Text="💰 نطاق المبلغ"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="#4A5568"
                                           Margin="0,0,0,8"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBox x:Name="MinAmountTextBox"
                                             Grid.Column="0"
                                             materialDesign:HintAssist.Hint="من"
                                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                             FontSize="12"
                                             Height="35"
                                             TextChanged="AmountFilter_TextChanged"/>

                                    <TextBlock Grid.Column="1" Text=" - "
                                               VerticalAlignment="Center"
                                               Margin="8,0"
                                               Foreground="#718096"/>

                                    <TextBox x:Name="MaxAmountTextBox"
                                             Grid.Column="2"
                                             materialDesign:HintAssist.Hint="إلى"
                                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                             FontSize="12"
                                             Height="35"
                                             TextChanged="AmountFilter_TextChanged"/>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- فلتر عدد الفواتير -->
                        <Border Grid.Column="2"
                                Background="#F7FAFC"
                                CornerRadius="8"
                                Padding="12"
                                Margin="0,0,8,0">
                            <StackPanel>
                                <TextBlock Text="📄 عدد الفواتير"
                                           FontSize="13"
                                           FontWeight="Medium"
                                           Foreground="#4A5568"
                                           Margin="0,0,0,8"/>
                                <ComboBox x:Name="InvoiceCountFilterComboBox"
                                          materialDesign:HintAssist.Hint="اختر النطاق"
                                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                          FontSize="12"
                                          Height="35"
                                          SelectionChanged="InvoiceCountFilter_SelectionChanged">
                                    <ComboBoxItem Content="الكل"/>
                                    <ComboBoxItem Content="أقل من 5"/>
                                    <ComboBoxItem Content="5-10"/>
                                    <ComboBoxItem Content="أكثر من 10"/>
                                </ComboBox>
                            </StackPanel>
                        </Border>

                        <!-- زر مسح الفلاتر وعدد النتائج -->
                        <StackPanel Grid.Column="3" VerticalAlignment="Center">
                            <Button x:Name="ClearFiltersButton"
                                    Content="🗑️ مسح الفلاتر"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                    materialDesign:ButtonAssist.CornerRadius="8"
                                    Height="40"
                                    Padding="16,8"
                                    FontWeight="Medium"
                                    BorderBrush="#E53E3E"
                                    Foreground="#E53E3E"
                                    Margin="0,0,0,8"
                                    Click="ClearSearchButton_Click"/>

                            <!-- عدد النتائج -->
                            <TextBlock x:Name="ResultsCountTextBlock"
                                       Text="0 نتيجة"
                                       FontSize="13"
                                       FontWeight="Medium"
                                       HorizontalAlignment="Center"
                                       Foreground="#718096"/>
                        </StackPanel>
                    </Grid>
                </Grid>
            </Expander>
        </materialDesign:Card>



        <!-- جدول الموردين -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- عنوان الجدول -->
                <Border Grid.Row="0"
                        Background="{StaticResource PrimaryGradientBrush}"
                        CornerRadius="8,8,0,0"
                        Padding="20,15">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="TableLarge"
                                               Width="24" Height="24"
                                               Foreground="White"
                                               VerticalAlignment="Center"
                                               Margin="0,0,10,0"/>
                        <TextBlock Text="قائمة الموردين"
                                   FontSize="18"
                                   FontWeight="Bold"
                                   Foreground="White"
                                   VerticalAlignment="Center"/>
                        <TextBlock x:Name="TotalCountTextBlock"
                                   Text="(0 مورد)"
                                   FontSize="14"
                                   Foreground="White"
                                   Opacity="0.8"
                                   VerticalAlignment="Center"
                                   Margin="10,0,0,0"/>
                    </StackPanel>
                </Border>

                <!-- الجدول -->
                <DataGrid x:Name="SuppliersDataGrid"
                          Grid.Row="1"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          SelectionMode="Single"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          materialDesign:DataGridAssist.CellPadding="12"
                          materialDesign:DataGridAssist.ColumnHeaderPadding="12"
                          FontSize="13"
                          FontFamily="Segoe UI"
                          Background="White"
                          RowBackground="White"
                          AlternatingRowBackground="#FAFBFC"
                          FlowDirection="RightToLeft"
                          SelectionChanged="SuppliersDataGrid_SelectionChanged">

                    <DataGrid.ColumnHeaderStyle>
                        <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                            <Setter Property="Background" Value="#F8F9FA"/>
                            <Setter Property="Foreground" Value="#2D3748"/>
                            <Setter Property="FontWeight" Value="Bold"/>
                            <Setter Property="FontSize" Value="14"/>
                            <Setter Property="Height" Value="50"/>
                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                            <Setter Property="Padding" Value="8"/>
                        </Style>
                    </DataGrid.ColumnHeaderStyle>

                    <DataGrid.RowStyle>
                        <Style TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
                            <Setter Property="Height" Value="55"/>
                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#E6F3FF"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="#CCE7FF"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </DataGrid.RowStyle>

                    <DataGrid.CellStyle>
                        <Style TargetType="DataGridCell" BasedOn="{StaticResource MaterialDesignDataGridCell}">
                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                            <Setter Property="Padding" Value="8,4"/>
                            <Setter Property="FontSize" Value="13"/>
                            <Setter Property="FontWeight" Value="Medium"/>
                        </Style>
                    </DataGrid.CellStyle>
                    <DataGrid.Columns>
                        <!-- اسم المورد -->
                        <DataGridTextColumn Header="🏢 اسم المورد"
                                            Binding="{Binding Name}"
                                            Width="200">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="FontWeight" Value="Medium"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Margin" Value="0,0,15,0"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                    <Setter Property="Foreground" Value="#1976D2"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- الشخص المسؤول -->
                        <DataGridTextColumn Header="👤 المسؤول"
                                            Binding="{Binding ContactPerson}"
                                            Width="140">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="FontWeight" Value="Medium"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                    <Setter Property="Foreground" Value="#424242"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- رقم الهاتف -->
                        <DataGridTextColumn Header="📞 الهاتف"
                                            Binding="{Binding Phone}"
                                            Width="120">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="FontWeight" Value="Medium"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                    <Setter Property="FlowDirection" Value="LeftToRight"/>
                                    <Setter Property="Foreground" Value="#424242"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- البريد الإلكتروني -->
                        <DataGridTextColumn Header="📧 الإيميل"
                                            Binding="{Binding Email}"
                                            Width="160">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="12"/>
                                    <Setter Property="FontWeight" Value="Medium"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontStyle" Value="Italic"/>
                                    <Setter Property="FlowDirection" Value="LeftToRight"/>
                                    <Setter Property="Foreground" Value="#424242"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- العنوان -->
                        <DataGridTextColumn Header="📍 العنوان"
                                            Binding="{Binding Address}"
                                            Width="150">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="12"/>
                                    <Setter Property="FontWeight" Value="Medium"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="TextWrapping" Value="NoWrap"/>
                                    <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                    <Setter Property="Foreground" Value="#64748B"/>
                                    <Setter Property="Margin" Value="0,0,10,0"/>
                                    <Setter Property="FlowDirection" Value="RightToLeft"/>
                                    <Setter Property="ToolTip" Value="{Binding Address}"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- عدد الفواتير -->
                        <DataGridTextColumn Header="📄 الفواتير"
                                            Binding="{Binding InvoiceCount}"
                                            Width="90">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Foreground" Value="#10B981"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- إجمالي المبلغ -->
                        <DataGridTextColumn Header="💰 الإجمالي"
                                            Binding="{Binding TotalAmount, StringFormat='{}{0:N0}'}"
                                            Width="120">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Foreground" Value="#F59E0B"/>
                                    <Setter Property="FlowDirection" Value="LeftToRight"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- الإجراءات -->
                        <DataGridTemplateColumn Header="⚙️ الإجراءات" Width="*" MinWidth="160" CanUserSort="False" CanUserReorder="False">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <!-- Edit Button -->
                                        <Button ToolTip="تعديل المورد"
                                               Style="{StaticResource MaterialDesignIconButton}"
                                               Width="32" Height="32"
                                               Background="#E8F5E8"
                                               Foreground="#2E7D32"
                                               Tag="{Binding}"
                                               Click="EditSupplierButton_Click"
                                               Margin="2">
                                            <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                        </Button>

                                        <!-- Statement Button -->
                                        <Button ToolTip="كشف الحساب"
                                               Style="{StaticResource MaterialDesignIconButton}"
                                               Width="32" Height="32"
                                               Background="#E3F2FD"
                                               Foreground="#1976D2"
                                               Tag="{Binding}"
                                               Click="StatementButton_Click"
                                               Margin="2">
                                            <materialDesign:PackIcon Kind="FileDocumentEdit" Width="16" Height="16"/>
                                        </Button>

                                        <!-- Delete Button -->
                                        <Button ToolTip="حذف المورد"
                                               Style="{StaticResource MaterialDesignIconButton}"
                                               Width="32" Height="32"
                                               Background="#FFEBEE"
                                               Foreground="#D32F2F"
                                               Tag="{Binding}"
                                               Click="DeleteSupplierButton_Click"
                                               Margin="2">
                                            <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- إحصائيات الموردين - نفس تصميم صفحة العروض -->
        <materialDesign:Card Grid.Row="3" Style="{StaticResource ModernCardStyle}" Margin="0,0,0,0">
            <Grid Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- إحصائيات الموردين -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Border Background="{StaticResource PrimaryGradientBrush}"
                            CornerRadius="20"
                            Padding="16,8"
                            Margin="0,0,12,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AccountMultiple"
                                                   Width="20" Height="20"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock x:Name="TotalSuppliersTextBlock"
                                       Text="إجمالي الموردين: 0"
                                       Foreground="White"
                                       FontWeight="Bold"
                                       FontSize="13"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <Border Background="{StaticResource SecondaryGradientBrush}"
                            CornerRadius="20"
                            Padding="16,8"
                            Margin="0,0,12,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Filter"
                                                   Width="20" Height="20"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock x:Name="FilteredSuppliersTextBlock"
                                       Text="الموردين المعروضين: 0"
                                       Foreground="White"
                                       FontWeight="Bold"
                                       FontSize="13"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <Border Background="#10B981"
                            CornerRadius="20"
                            Padding="16,8"
                            Margin="0,0,12,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CheckCircle"
                                                   Width="20" Height="20"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock x:Name="ActiveSuppliersTextBlock"
                                       Text="الموردين النشطين: 0"
                                       Foreground="White"
                                       FontWeight="Bold"
                                       FontSize="13"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <Border Background="#F59E0B"
                            CornerRadius="20"
                            Padding="16,8">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CashMultiple"
                                                   Width="20" Height="20"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock x:Name="TotalAmountTextBlock"
                                       Text="إجمالي المبالغ: 0 د.ع"
                                       Foreground="White"
                                       FontWeight="Bold"
                                       FontSize="13"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- مؤشر التحميل -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <ProgressBar x:Name="LoadingProgressBar"
                                 Style="{StaticResource MaterialDesignCircularProgressBar}"
                                 Width="24"
                                 Height="24"
                                 IsIndeterminate="True"
                                 Visibility="Collapsed"
                                 Margin="0,0,10,0"/>

                    <TextBlock Text="آخر تحديث: الآن"
                               FontSize="12"
                               Foreground="#718096"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Loading Panel -->
        <Border x:Name="LoadingPanel"
                Grid.RowSpan="4"
                Background="#CC000000"
                Visibility="Collapsed">
            <materialDesign:Card HorizontalAlignment="Center" VerticalAlignment="Center"
                               Padding="40,30" Style="{StaticResource ModernCardStyle}">
                <StackPanel HorizontalAlignment="Center">
                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                Width="50" Height="50"
                                IsIndeterminate="True"
                                Foreground="{StaticResource PrimaryGradientBrush}"/>
                    <TextBlock Text="جاري تحميل بيانات الموردين..."
                              FontSize="14" FontWeight="Medium"
                              Foreground="#2D3748"
                              HorizontalAlignment="Center"
                              Margin="0,15,0,0"/>
                </StackPanel>
            </materialDesign:Card>
        </Border>

        <!-- Empty State Panel -->
        <materialDesign:Card x:Name="EmptyStatePanel"
                           Grid.Row="2"
                           Style="{StaticResource ModernCardStyle}"
                           Visibility="Collapsed"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Margin="0,0,0,20"
                           Padding="60,40">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="AccountMultipleOutline"
                                       Width="80" Height="80"
                                       Foreground="#CED4DA"
                                       HorizontalAlignment="Center"/>
                <TextBlock Text="لا توجد موردين مسجلين"
                         FontSize="20" FontWeight="SemiBold"
                         Foreground="#6C757D"
                         HorizontalAlignment="Center"
                         Margin="0,20,0,8"/>
                <TextBlock Text="ابدأ بإضافة مورد جديد لعرض البيانات هنا"
                         FontSize="14" Foreground="#ADB5BD"
                         HorizontalAlignment="Center"
                         TextWrapping="Wrap" TextAlignment="Center"
                         Margin="0,0,0,20"/>
                <Button Style="{StaticResource ModernButtonStyle}"
                        Background="{StaticResource PrimaryGradientBrush}"
                        Click="AddNewSupplierButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,6,0"/>
                        <TextBlock Text="إضافة مورد جديد"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
